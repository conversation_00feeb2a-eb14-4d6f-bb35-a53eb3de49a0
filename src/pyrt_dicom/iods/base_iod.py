"""Base IOD for DICOM Information Object Definitions.

Provides foundation for all DICOM IOD implementations using explicit module
constructors and on-demand dataset generation.
"""

from abc import ABC, abstractmethod
from typing import Dict, Optional, Any
import pydicom
from pydicom.uid import generate_uid


class IODValidationError(Exception):
    """Raised when IOD module requirements are not satisfied."""
    pass


class BaseIOD(ABC):
    """Base class for all DICOM Information Object Definitions.
    
    Provides module storage, dataset generation, and validation framework
    for DICOM IOD implementations. Uses composition over inheritance with
    explicit module constructors.
    """
    
    def __init__(self):
        self._modules: Dict[str, Any] = {}
    
    def generate_dataset(self) -> pydicom.Dataset:
        """Generate a fresh DICOM dataset from current module state.
        
        This method explicitly converts the collection of modules into a pydicom
        Dataset by merging all module data. Each call generates a new dataset
        reflecting the current state of all modules.
        
        Returns:
            Fresh DICOM dataset containing all module data
            
        Example:
            # Create IOD with modules
            rt_dose = RTDoseIOD(patient_module, study_module, ...)
            
            # Generate dataset for export
            dataset = rt_dose.generate_dataset()
            dataset.save_as("dose.dcm")
            
            # Modify modules
            rt_dose.get_module('rt_dose').dose_grid_scaling = 0.002
            
            # Generate fresh dataset with updated data
            updated_dataset = rt_dose.generate_dataset()
            updated_dataset.save_as("dose_updated.dcm")
        """
        ds = pydicom.Dataset()
        for module in self._modules.values():
            ds.update(module)
        return ds
    
    def generate_file_dataset(self, filename: str = "") -> pydicom.FileDataset:
        """Generate a DICOM FileDataset ready for file I/O operations.
        
        This method creates a FileDataset with proper file meta information
        for direct saving to DICOM files. Use this when you need the full
        file dataset structure with DICOM file headers.
        
        Args:
            filename: The filename for the DICOM file (optional)
        
        Returns:
            FileDataset with complete DICOM file structure
            
        Example:
            # Generate file dataset for saving
            file_dataset = rt_dose.generate_file_dataset("dose_with_meta.dcm")
            file_dataset.save_as("dose_with_meta.dcm")
        """
        ds = self.generate_dataset()
        
        # Create file meta information
        file_meta = pydicom.Dataset()
        file_meta.MediaStorageSOPClassUID = getattr(ds, 'SOPClassUID', '')
        file_meta.MediaStorageSOPInstanceUID = getattr(ds, 'SOPInstanceUID', '')
        file_meta.ImplementationClassUID = "1.2.826.0.1.3680043.8.498.1"  # PyRT-DICOM
        file_meta.ImplementationVersionName = "PyRT-DICOM 0.1.0"
        file_meta.TransferSyntaxUID = "1.2.840.10008.1.2.1"  # Explicit VR Little Endian
        
        # Create FileDataset
        file_ds = pydicom.FileDataset(
            filename=filename,
            dataset=ds,
            file_meta=file_meta,
            is_implicit_VR=False,
            is_little_endian=True
        )
        
        return file_ds
    
    def get_module(self, module_name: str) -> Optional[Any]:
        """Get reference to a specific module for direct modification.
        
        Args:
            module_name: Name of module (e.g., 'patient', 'study', 'series')
            
        Returns:
            Live reference to module, or None if not present
            
        Note:
            Modifying returned module will affect subsequent calls to
            generate_dataset() and generate_file_dataset().
        """
        return self._modules.get(module_name)
    
    def validate(self) -> Dict[str, list[str]]:
        """Validate entire IOD including all modules.
        
        Returns:
            Dictionary with 'errors' and 'warnings' lists
        """
        result = {"errors": [], "warnings": []}
        
        # Validate each module
        for name, module in self._modules.items():
            if hasattr(module, 'validate'):
                try:
                    module_result = module.validate()
                    if isinstance(module_result, dict):
                        result['errors'].extend([f"{name}: {error}" for error in module_result.get('errors', [])])
                        result['warnings'].extend([f"{name}: {warning}" for warning in module_result.get('warnings', [])])
                except Exception as e:
                    result['errors'].append(f"Validation error in {name}: {e}")
        
        # IOD-specific validation
        self._validate_iod_requirements(result)
        
        return result
    
    def _validate_iod_requirements(self, result: Dict[str, list[str]]) -> None:
        """Validate base IOD requirements.
        
        Args:
            result: Validation result to update
        """
        # Basic IOD validation - subclasses can override for specific requirements
        dataset = self.generate_dataset()
        
        if not hasattr(dataset, 'SOPClassUID') or not dataset.SOPClassUID:
            result['errors'].append("SOPClassUID is required")
        
        if not hasattr(dataset, 'SOPInstanceUID') or not dataset.SOPInstanceUID:
            result['errors'].append("SOPInstanceUID is required")
    
    @abstractmethod
    def _validate_dependencies(self, *args, **kwargs) -> None:
        """Validate IOD-specific module dependencies.
        
        This method must be implemented by each IOD subclass to validate
        their specific module requirements and dependencies.
        """
        pass
    
    def __repr__(self) -> str:
        """String representation of the IOD."""
        class_name = self.__class__.__name__
        modules = list(self._modules.keys())
        dataset = self.generate_dataset()
        sop_instance_uid = getattr(dataset, 'SOPInstanceUID', 'Unknown')
        return f"{class_name}(modules={modules}, sop_instance_uid='{sop_instance_uid}')"