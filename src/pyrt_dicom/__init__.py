"""PyRT-DICOM: Python library for creating radiotherapy DICOM files.

This library provides a strongly-typed, IntelliSense-friendly interface for creating 
DICOM Information Object Definitions (IODs) through modular composition.

Built on top of pydicom, PyRT-DICOM supports:
- CT Image IOD
- RT Image IOD  
- RT Dose IOD
- RT Plan IOD
- RT Structure Set IOD

Key Features:
- Modular architecture using DICOM modules as Python classes
- Type safety with IntelliSense support
- No free text configuration - strongly-typed interfaces
- Optional validation with detailed error reporting
- Auto-generated UIDs with cross-dataset linking
- Memory-based storage for fast access
"""

from .__about__ import __version__

__all__ = ["__version__"]
