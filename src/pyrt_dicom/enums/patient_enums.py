"""Patient-related DICOM enumerations."""

from enum import Enum


class PatientSex(Enum):
    """Patient Sex (0010,0040) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.1.1:
    - M = Male
    - F = Female  
    - O = Other
    """
    MALE = "M"
    FEMALE = "F" 
    OTHER = "O"


class ResponsiblePersonRole(Enum):
    """Responsible Person Role (0010,2298) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.1.1.1.2:
    """
    OWNER = "OWNER"
    PARENT = "PARENT"
    CHILD = "CHILD"
    SPOUSE = "SPOUSE"
    SIBLING = "SIBLING"
    RELATIVE = "RELATIVE"
    GUARDIAN = "GUARDIAN"
    CUSTODIAN = "CUSTODIAN"
    AGENT = "AGENT"
    INVESTIGATOR = "INVESTIGATOR"
    VETERINARIAN = "VETERINARIAN"


class TypeOfPatientID(Enum):
    """Type of Patient ID (0010,0022) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.1.1:
    """
    TEXT = "TEXT"
    RFID = "RFID" 
    BARCODE = "BARCODE"