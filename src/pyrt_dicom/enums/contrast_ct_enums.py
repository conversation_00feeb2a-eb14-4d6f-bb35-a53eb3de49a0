"""
Contrast and CT Module Enums - Enumerated values for contrast and CT-related DICOM modules.

This module contains enumerated values used across contrast/bolus and CT image modules
including CT Image, Multi-energy CT Image, Contrast/Bolus, and General Acquisition modules.
"""
from enum import Enum


class ContrastBolusIngredient(Enum):
    """Contrast/Bolus Ingredient enumerated values (0018,1048)."""
    IODINE = "IODINE"                # Iodine-based contrast agent
    GADOLINIUM = "GADOLINIUM"        # Gadolinium-based contrast agent
    CARBON_DIOXIDE = "CARBON DIOXIDE"  # Carbon dioxide contrast agent
    BARIUM = "BARIUM"                # Barium-based contrast agent


class MultiEnergyCTAcquisition(Enum):
    """Multi-energy CT Acquisition enumerated values (0018,9361)."""
    YES = "YES"  # Image is created by means of Multi-energy technique
    NO = "NO"    # Image is not created by means of Multi-energy technique


class RotationDirection(Enum):
    """Rotation Direction enumerated values (0018,1140)."""
    CW = "CW"  # Clockwise rotation
    CC = "CC"  # Counter clockwise rotation


class ExposureModulationType(Enum):
    """Exposure Modulation Type enumerated values (0018,9323)."""
    NONE = "NONE"  # No exposure modulation used


class CTImageTypeValue1(Enum):
    """CT Image Type Value 1 enumerated values (0008,0008)."""
    ORIGINAL = "ORIGINAL"  # Original Image
    DERIVED = "DERIVED"    # Derived Image


class CTImageTypeValue2(Enum):
    """CT Image Type Value 2 enumerated values (0008,0008)."""
    PRIMARY = "PRIMARY"    # Primary Image
    SECONDARY = "SECONDARY"  # Secondary Image


class CTImageTypeValue3(Enum):
    """CT Image Type Value 3 enumerated values (0008,0008)."""
    AXIAL = "AXIAL"        # CT Cross-sectional Image
    LOCALIZER = "LOCALIZER"  # CT Localizer Image


class CTImageTypeValue4(Enum):
    """CT Image Type Value 4 enumerated values for Multi-energy CT (0008,0008)."""
    VMI = "VMI"                      # Virtual Monoenergetic Image
    MAT_SPECIFIC = "MAT_SPECIFIC"    # Material-Specific Image
    MAT_REMOVED = "MAT_REMOVED"      # Material-Removed Image
    MAT_FRACTIONAL = "MAT_FRACTIONAL"  # Material-Fractional Image
    EFF_ATOMIC_NUM = "EFF_ATOMIC_NUM"  # Effective Atomic Number Image
    ELECTRON_DENSITY = "ELECTRON_DENSITY"  # Electron Density Image
    MAT_MODIFIED = "MAT_MODIFIED"    # Material-Modified Image
    MAT_VALUE_BASED = "MAT_VALUE_BASED"  # Value-Based Image


class CTSamplesPerPixel(Enum):
    """CT Samples per Pixel enumerated values (0028,0002)."""
    ONE = 1  # Single sample per pixel for CT images


class CTBitsAllocated(Enum):
    """CT Bits Allocated enumerated values (0028,0100)."""
    SIXTEEN = 16  # 16 bits allocated for CT images


class CTBitsStored(Enum):
    """CT Bits Stored enumerated values (0028,0101)."""
    TWELVE = 12      # 12 bits stored
    THIRTEEN = 13    # 13 bits stored
    FOURTEEN = 14    # 14 bits stored
    FIFTEEN = 15     # 15 bits stored
    SIXTEEN = 16     # 16 bits stored


class MultiEnergySourceTechnique(Enum):
    """Multi-energy Source Technique enumerated values (0018,9368)."""
    SWITCHING_SOURCE = "SWITCHING_SOURCE"  # Physical X-Ray source uses beam mode switching
    CONSTANT_SOURCE = "CONSTANT_SOURCE"    # Physical X-Ray source uses beam with constant characteristics


class MultiEnergyDetectorType(Enum):
    """Multi-energy Detector Type enumerated values (0018,9372)."""
    INTEGRATING = "INTEGRATING"        # Physical detector integrates the full X-Ray spectrum
    MULTILAYER = "MULTILAYER"          # Physical detector layers absorb different parts of X-Ray spectrum
    PHOTON_COUNTING = "PHOTON_COUNTING"  # Physical detector counts photons with energy discrimination


class RescaleType(Enum):
    """Rescale Type enumerated values (0028,1054)."""
    HU = "HU"                    # Hounsfield Units
    US = "US"                    # Unspecified
    MGML = "MGML"               # Milligrams per milliliter
    PCNT = "PCNT"               # Percent
    CPS = "CPS"                 # Counts per second
    NONE = "NONE"               # No rescaling function
    CM = "CM"                   # Centimeters
    MM = "MM"                   # Millimeters
    PIXVAL = "PIXVAL"           # Pixel value
    COUNTS = "COUNTS"           # Counts
    PROPCNT = "PROPCNT"         # Proportional to counts
    DISP = "DISP"               # Display units
    UMOL = "UMOL"               # Micromoles
    CONC = "CONC"               # Concentration
    RWU = "RWU"                 # Relative water units
    DENS = "DENS"               # Density
    TEMP = "TEMP"               # Temperature
    FLOW = "FLOW"               # Flow
    PERF = "PERF"               # Perfusion
    DIFF = "DIFF"               # Diffusion
    RELAX = "RELAX"             # Relaxation
    METAB = "METAB"             # Metabolic
    RATIO = "RATIO"             # Ratio
    OTHER = "OTHER"             # Other units
    DVPX = "DVPX"               # Device-specific units
    COEF = "COEF"               # Coefficient
    GRAD = "GRAD"               # Gradient
    FRAC = "FRAC"               # Fraction
    MASS = "MASS"               # Mass
    MLMIN = "MLMIN"             # Milliliters per minute
    MLMINM2 = "MLMINM2"         # Milliliters per minute per square meter
    ML100GM = "ML100GM"         # Milliliters per 100 grams
    MLMIN100G = "MLMIN100G"     # Milliliters per minute per 100 grams
    DEGC = "DEGC"               # Degrees Celsius
    SEC = "SEC"                 # Seconds
    MSEC = "MSEC"               # Milliseconds
    USEC = "USEC"               # Microseconds
    HZ = "HZ"                   # Hertz
    PPM = "PPM"                 # Parts per million
    RAD = "RAD"                 # Radians
    DEG = "DEG"                 # Degrees


class FilterMaterial(Enum):
    """Filter Material enumerated values (0018,7050)."""
    # Common X-Ray filter materials
    ALUMINUM = "AL"             # Aluminum
    COPPER = "CU"               # Copper
    MOLYBDENUM = "MO"           # Molybdenum
    RHODIUM = "RH"              # Rhodium
    SILVER = "AG"               # Silver
    TIN = "SN"                  # Tin
    GADOLINIUM = "GD"           # Gadolinium
    HOLMIUM = "HO"              # Holmium
    ERBIUM = "ER"               # Erbium
    YTTERBIUM = "YB"            # Ytterbium
    TUNGSTEN = "W"              # Tungsten
    LEAD = "PB"                 # Lead
    TITANIUM = "TI"             # Titanium
    NICKEL = "NI"               # Nickel
    IRON = "FE"                 # Iron
    BERYLLIUM = "BE"            # Beryllium
    CARBON = "C"                # Carbon
    POLYIMIDE = "POLYIMIDE"     # Polyimide
    LEXAN = "LEXAN"             # Lexan
    KAPTON = "KAPTON"           # Kapton
    MYLAR = "MYLAR"             # Mylar


class ScanOptions(Enum):
    """Common Scan Options enumerated values (0018,0022).

    Note: This is not a DICOM-defined enumeration but provides common values
    for convenience. Custom values are allowed per DICOM standard.
    """
    HELICAL_CT = "HELICAL_CT"       # Helical/spiral CT scan
    AXIAL_CT = "AXIAL_CT"           # Axial/sequential CT scan
    CINE_CT = "CINE_CT"             # Cine CT scan
    CARDIAC_GATING = "CARDIAC_GATING"  # Cardiac gated scan
    RESPIRATORY_GATING = "RESPIRATORY_GATING"  # Respiratory gated scan
    CONTRAST_ENHANCED = "CONTRAST_ENHANCED"  # Contrast enhanced scan
    NON_CONTRAST = "NON_CONTRAST"   # Non-contrast scan
    PERFUSION = "PERFUSION"         # Perfusion scan
    ANGIOGRAPHY = "ANGIOGRAPHY"     # CT angiography
    CALCIUM_SCORING = "CALCIUM_SCORING"  # Calcium scoring scan


class FilterType(Enum):
    """Common Filter Type enumerated values (0018,1160).

    Note: This is not a DICOM-defined enumeration but provides common values
    for convenience. Custom values are allowed per DICOM standard.
    """
    NONE = "NONE"                   # No filter
    ALUMINUM = "ALUMINUM"           # Aluminum filter
    COPPER = "COPPER"               # Copper filter
    MOLYBDENUM = "MOLYBDENUM"       # Molybdenum filter
    RHODIUM = "RHODIUM"             # Rhodium filter
    SILVER = "SILVER"               # Silver filter
    TIN = "TIN"                     # Tin filter
    GADOLINIUM = "GADOLINIUM"       # Gadolinium filter
    TUNGSTEN = "TUNGSTEN"           # Tungsten filter
    LEAD = "LEAD"                   # Lead filter
    TITANIUM = "TITANIUM"           # Titanium filter
    BERYLLIUM = "BERYLLIUM"         # Beryllium filter
    CARBON = "CARBON"               # Carbon filter
    COMPOSITE = "COMPOSITE"         # Composite filter
    WEDGE = "WEDGE"                 # Wedge filter
    BOW_TIE = "BOW_TIE"            # Bow-tie filter
    FLAT = "FLAT"                   # Flat filter
