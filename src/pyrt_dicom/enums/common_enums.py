"""Common DICOM enumerations for SOP and reference modules."""

from enum import Enum


class SyntheticData(Enum):
    """Synthetic Data (0008,001C) - DICOM PS3.3 C.12.1."""
    
    YES = "YES"
    NO = "NO"


class SOPInstanceStatus(Enum):
    """SOP Instance Status (0100,0410) - DICOM PS3.3 C.12.1."""
    
    NS = "NS"  # Not Specified
    AO = "AO"  # Authorized Original
    AC = "AC"  # Authorized Copy


class QueryRetrieveView(Enum):
    """Query/Retrieve View (0008,0053) - DICOM PS3.3 C.12.1."""
    
    CLASSIC = "CLASSIC"
    ENHANCED = "ENHANCED"


class ContentQualification(Enum):
    """Content Qualification (0018,9004) - DICOM PS3.3 C.12.1."""
    
    PRODUCT = "PRODUCT"
    RESEARCH = "RESEARCH"
    SERVICE = "SERVICE"


class LongitudinalTemporalInformationModified(Enum):
    """Longitudinal Temporal Information Modified (0028,0303) - DICOM PS3.3 C.12.1."""
    
    UNMODIFIED = "UNMODIFIED"
    MODIFIED = "MODIFIED"
    REMOVED = "REMOVED"


class SpatialLocationsPreserved(Enum):
    """Spatial Locations Preserved (0028,135A) - DICOM PS3.3 C.12.4."""
    
    YES = "YES"
    NO = "NO"
    REORIENTED_ONLY = "REORIENTED_ONLY"
