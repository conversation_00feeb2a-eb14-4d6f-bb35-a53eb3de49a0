"""Series-related DICOM enumerations."""

from enum import Enum


class Modality(Enum):
    """Modality (0008,0060) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.*******.1:
    Common modalities for medical imaging
    """
    # Common imaging modalities
    CT = "CT"  # Computed Tomography
    MR = "MR"  # Magnetic Resonance
    US = "US"  # Ultrasound
    XA = "XA"  # X-Ray Angiography
    RF = "RF"  # Radio Fluoroscopy
    DX = "DX"  # Digital Radiography
    CR = "CR"  # Computed Radiography
    MG = "MG"  # Mammography
    NM = "NM"  # Nuclear Medicine
    PT = "PT"  # Positron emission tomography (PET)
    
    # RT modalities
    RTIMAGE = "RTIMAGE"  # Radiotherapy Image
    RTDOSE = "RTDOSE"    # Radiotherapy Dose
    RTSTRUCT = "RTSTRUCT"  # Radiotherapy Structure Set
    RTPLAN = "RTPLAN"    # Radiotherapy Plan
    RTRECORD = "RTRECORD"  # RT Treatment Record
    
    # Other modalities
    SR = "SR"    # SR Document
    DOC = "DOC"  # Document
    PR = "PR"    # Presentation State
    KO = "KO"    # Key Object Selection
    SEG = "SEG"  # Segmentation
    REG = "REG"  # Registration
    PLAN = "PLAN"  # Plan
    OT = "OT"    # Other


class Laterality(Enum):
    """Laterality (0020,0060) - DICOM VR: CS
    
    Enumerated Values per DICOM PS3.3 C.7.3.1:
    - R = right
    - L = left
    """
    RIGHT = "R"
    LEFT = "L"


class PatientPosition(Enum):
    """Patient Position (0018,5100) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.*******.2:
    Patient position descriptor relative to the equipment
    """
    # Head First positions
    HFP = "HFP"   # Head First-Prone
    HFS = "HFS"   # Head First-Supine
    HFDR = "HFDR" # Head First-Decubitus Right
    HFDL = "HFDL" # Head First-Decubitus Left
    HFV = "HFV"   # Head First-Vertical
    HFI = "HFI"   # Head First-Inverted
    
    # Feet First positions
    FFP = "FFP"   # Feet First-Prone
    FFS = "FFS"   # Feet First-Supine
    FFDR = "FFDR" # Feet First-Decubitus Right
    FFDL = "FFDL" # Feet First-Decubitus Left
    FFV = "FFV"   # Feet First-Vertical
    FFI = "FFI"   # Feet First-Inverted
    
    # Left First positions
    LFP = "LFP"   # Left First-Prone
    LFS = "LFS"   # Left First-Supine
    LFDR = "LFDR" # Left First-Decubitus Right
    LFDL = "LFDL" # Left First-Decubitus Left
    
    # Right First positions
    RFP = "RFP"   # Right First-Prone
    RFS = "RFS"   # Right First-Supine
    RFDR = "RFDR" # Right First-Decubitus Right
    RFDL = "RFDL" # Right First-Decubitus Left
    
    # Anterior First positions
    AFP = "AFP"   # Anterior First-Prone
    AFS = "AFS"   # Anterior First-Supine
    AFDR = "AFDR" # Anterior First-Decubitus Right
    AFDL = "AFDL" # Anterior First-Decubitus Left
    
    # Posterior First positions
    PFP = "PFP"   # Posterior First-Prone
    PFS = "PFS"   # Posterior First-Supine
    PFDR = "PFDR" # Posterior First-Decubitus Right
    PFDL = "PFDL" # Posterior First-Decubitus Left


class AnatomicalOrientationType(Enum):
    """Anatomical Orientation Type (0010,2210) - DICOM VR: CS
    
    Enumerated Values per DICOM PS3.3 C.7.3.1:
    - BIPED = Bipedal anatomical orientation
    - QUADRUPED = Quadrupedal anatomical orientation
    """
    BIPED = "BIPED"
    QUADRUPED = "QUADRUPED"
