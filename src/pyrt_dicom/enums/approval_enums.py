"""Approval-related DICOM enumerations."""

from enum import Enum


class ApprovalStatus(Enum):
    """Approval Status enumerated values (300E,0002)."""
    APPROVED = "APPROVED"      # Reviewer recorded that object met an implied criterion
    UNAPPROVED = "UNAPPROVED"  # No review of object has been recorded
    REJECTED = "REJECTED"      # Reviewer recorded that object failed to meet an implied criterion
