"""RT Dose-related DICOM enumerations."""

from enum import Enum


class DoseUnits(Enum):
    """Dose Units (3004,0002) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.3:
    - GY = Gray
    - RELATIVE = Relative dose
    """
    GY = "GY"
    RELATIVE = "RELATIVE"


class DoseType(Enum):
    """Dose Type (3004,0004) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.8.8.3:
    - PHYSICAL = Physical dose
    - EFFECTIVE = Effective dose  
    - ERROR = Dose error/uncertainty
    """
    PHYSICAL = "PHYSICAL"
    EFFECTIVE = "EFFECTIVE"
    ERROR = "ERROR"