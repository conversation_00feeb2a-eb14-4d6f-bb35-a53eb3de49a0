"""Synchronization Module DICOM enumerations - PS3.3 C.7.4.2."""

from enum import Enum


class SynchronizationTrigger(Enum):
    """Synchronization Trigger (0018,106A) - DICOM PS3.3 C.7.4.2.
    
    Data acquisition synchronization with external equipment.
    """
    
    SOURCE = "SOURCE"  # This equipment provides synchronization channel or trigger to other equipment
    EXTERNAL = "EXTERNAL"  # This equipment receives synchronization channel or trigger from other equipment
    PASSTHRU = "PASSTHRU"  # This equipment receives synchronization channel or trigger and forwards it
    NO_TRIGGER = "NO TRIGGER"  # Data acquisition not synchronized by common channel or trigger


class AcquisitionTimeSynchronized(Enum):
    """Acquisition Time Synchronized (0018,1800) - DICOM PS3.3 C.7.4.2.
    
    Acquisition DateTime synchronized with external time reference.
    """
    
    YES = "Y"  # Acquisition DateTime is synchronized with external time reference
    NO = "N"   # Acquisition DateTime is not synchronized with external time reference


class TimeDistributionProtocol(Enum):
    """Time Distribution Protocol (0018,1802) - DICOM PS3.3 C.7.4.2.
    
    Method of time distribution used to synchronize this equipment.
    """
    
    NTP = "NTP"    # Network Time Protocol
    IRIG = "IRIG"  # Inter Range Instrumentation Group
    GPS = "GPS"    # Global Positioning System
    SNTP = "SNTP"  # Simple Network Time Protocol
    PTP = "PTP"    # IEEE 1588 Precision Time Protocol
