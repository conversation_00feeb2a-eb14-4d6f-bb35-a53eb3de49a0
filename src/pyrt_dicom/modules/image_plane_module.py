"""
Image Plane Module - DICOM PS3.3 C.7.6.2

The Image Plane Module defines the transmitted pixel array of a two dimensional 
image plane in a three dimensional space.
"""
from .base_module import BaseModule
from ..validators.modules.image_plane_validator import ImagePlaneValidator
from ..validators.modules.base_validator import ValidationConfig


class ImagePlaneModule(BaseModule):
    """Image Plane Module implementation for DICOM PS3.3 C.7.6.2.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Defines the transmitted pixel array of a two dimensional image plane 
    in a three dimensional space.
    
    Usage:
        # Create with required elements
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[0.5, 0.5],
            image_orientation_patient=[1.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            image_position_patient=[100.0, 100.0, 50.0],
            slice_thickness="5.0"
        )
        
        # Add optional elements
        plane.with_optional_elements(
            spacing_between_slices=5.0,
            slice_location=50.0
        )
        
        # Validate
        result = plane.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        pixel_spacing: list[float],
        image_orientation_patient: list[float],
        image_position_patient: list[float],
        slice_thickness: str = ""
    ) -> 'ImagePlaneModule':
        """Create ImagePlaneModule from all required (Type 1 and Type 2) data elements.
        
        Args:
            pixel_spacing (list[float]): Physical distance between pixel centers (0028,0030) Type 1.
                Format: [row_spacing, column_spacing] in mm
            image_orientation_patient (list[float]): Direction cosines of first row and column (0020,0037) Type 1.
                Format: [row_x, row_y, row_z, col_x, col_y, col_z]
            image_position_patient (list[float]): Upper left corner coordinates (0020,0032) Type 1.
                Format: [x, y, z] in mm
            slice_thickness (str): Nominal slice thickness in mm (0018,0050) Type 2
            
        Returns:
            ImagePlaneModule: New dataset instance with required data elements set
        """
        instance = cls()
        instance.PixelSpacing = pixel_spacing
        instance.ImageOrientationPatient = image_orientation_patient
        instance.ImagePositionPatient = image_position_patient
        instance.SliceThickness = slice_thickness
        return instance
    
    def with_optional_elements(
        self,
        spacing_between_slices: float | None = None,
        slice_location: float | None = None
    ) -> 'ImagePlaneModule':
        """Add optional (Type 3) data elements.
        
        Args:
            spacing_between_slices (float | None): Spacing between adjacent slices in mm (0018,0088) Type 3.
                Measured center-to-center. Must not be negative unless specialized IOD defines meaning.
            slice_location (float | None): Relative position of image plane in mm (0020,1041) Type 3.
                Relative to implementation-specific reference point.
            
        Returns:
            ImagePlaneModule: Self with optional elements added
        """
        self._set_attribute_if_not_none('SpacingBetweenSlices', spacing_between_slices)
        self._set_attribute_if_not_none('SliceLocation', slice_location)
        return self
    
    @property
    def is_orthogonal(self) -> bool:
        """Check if row and column direction cosines are orthogonal."""
        if not hasattr(self, 'ImageOrientationPatient') or len(self.ImageOrientationPatient) != 6:
            return False
        
        row_cosines = self.ImageOrientationPatient[:3]
        col_cosines = self.ImageOrientationPatient[3:]
        
        # Calculate dot product
        dot_product = sum(r * c for r, c in zip(row_cosines, col_cosines))
        
        # Check if dot product is approximately zero (orthogonal)
        return abs(dot_product) < 1e-6
    
    @property
    def is_normalized(self) -> bool:
        """Check if row and column direction cosines are normalized (unit vectors)."""
        if not hasattr(self, 'ImageOrientationPatient') or len(self.ImageOrientationPatient) != 6:
            return False
        
        row_cosines = self.ImageOrientationPatient[:3]
        col_cosines = self.ImageOrientationPatient[3:]
        
        # Calculate magnitudes
        row_magnitude = sum(r * r for r in row_cosines) ** 0.5
        col_magnitude = sum(c * c for c in col_cosines) ** 0.5
        
        # Check if magnitudes are approximately 1.0
        return abs(row_magnitude - 1.0) < 1e-6 and abs(col_magnitude - 1.0) < 1e-6
    
    @property
    def has_valid_spacing_between_slices(self) -> bool:
        """Check if spacing between slices is valid (non-negative)."""
        if not hasattr(self, 'SpacingBetweenSlices'):
            return True  # Not present is valid
        return self.SpacingBetweenSlices >= 0
    
    @property
    def pixel_area(self) -> float | None:
        """Calculate pixel area in mm²."""
        if not hasattr(self, 'PixelSpacing') or len(self.PixelSpacing) != 2:
            return None
        return self.PixelSpacing[0] * self.PixelSpacing[1]
    
    @property
    def voxel_volume(self) -> float | None:
        """Calculate voxel volume in mm³ if slice thickness is available."""
        pixel_area = self.pixel_area
        if pixel_area is None or not hasattr(self, 'SliceThickness'):
            return None
        try:
            thickness = float(self.SliceThickness)
            return pixel_area * thickness
        except (ValueError, TypeError):
            return None
    
    def get_pixel_coordinates(self, row: int, col: int) -> list[float] | None:
        """Calculate real-world coordinates for a pixel position.
        
        Args:
            row (int): Row index (0-based)
            col (int): Column index (0-based)
            
        Returns:
            list[float] | None: [x, y, z] coordinates in mm, or None if calculation fails
        """
        if not all(hasattr(self, attr) for attr in ['ImagePositionPatient', 'ImageOrientationPatient', 'PixelSpacing']):
            return None
        
        if len(self.ImagePositionPatient) != 3 or len(self.ImageOrientationPatient) != 6 or len(self.PixelSpacing) != 2:
            return None
        
        # Extract components
        S = self.ImagePositionPatient  # Origin
        X = self.ImageOrientationPatient[:3]  # Row direction cosines
        Y = self.ImageOrientationPatient[3:]  # Column direction cosines
        pixel_spacing_row = self.PixelSpacing[0]
        pixel_spacing_col = self.PixelSpacing[1]
        
        # Calculate coordinates using DICOM equation
        x = S[0] + X[0] * pixel_spacing_col * col + Y[0] * pixel_spacing_row * row
        y = S[1] + X[1] * pixel_spacing_col * col + Y[1] * pixel_spacing_row * row
        z = S[2] + X[2] * pixel_spacing_col * col + Y[2] * pixel_spacing_row * row
        
        return [x, y, z]
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate module data against DICOM standard."""
        return ImagePlaneValidator.validate(self, config)
