"""
Overlay Plane Module - DICOM PS3.3 C.9.2

The Overlay Plane Module describes characteristics of an Overlay Plane.
An Overlay Plane describes graphics or bit-mapped text that is associated with an Image.
"""
from .base_module import BaseModule
from ..enums.image_enums import OverlayType, OverlaySubtype
from ..validators.modules.overlay_plane_validator import OverlayPlaneValidator
from ..validators.modules.base_validator import ValidationConfig


class OverlayPlaneModule(BaseModule):
    """Overlay Plane Module implementation for DICOM PS3.3 C.9.2.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Describes characteristics of an Overlay Plane that contains graphics 
    or bit-mapped text associated with an Image.
    
    Note: This implementation uses group 6000 for overlay attributes.
    DICOM allows groups 6000-601E (even groups only) for multiple overlays.
    
    Usage:
        # Create with required elements
        overlay = OverlayPlaneModule.from_required_elements(
            overlay_rows=512,
            overlay_columns=512,
            overlay_type=OverlayType.GRAPHICS,
            overlay_origin=[1, 1],
            overlay_data=b'...'  # 1-bit overlay data
        )
        
        # Add optional elements
        overlay.with_optional_elements(
            overlay_description="ROI boundary",
            overlay_subtype=OverlaySubtype.USER,
            overlay_label="Tumor boundary"
        )
        
        # Add ROI statistics if overlay type is ROI
        overlay.with_roi_statistics(
            roi_area=1024,
            roi_mean=150.5,
            roi_standard_deviation=25.3
        )
        
        # Validate
        result = overlay.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        overlay_rows: int,
        overlay_columns: int,
        overlay_type: str | OverlayType,
        overlay_origin: list[int],
        overlay_data: bytes,
        overlay_group: int = 0x6000
    ) -> 'OverlayPlaneModule':
        """Create OverlayPlaneModule from all required (Type 1) data elements.
        
        Args:
            overlay_rows (int): Number of rows in overlay (60xx,0010) Type 1
            overlay_columns (int): Number of columns in overlay (60xx,0011) Type 1
            overlay_type (str | OverlayType): Overlay type - Graphics or ROI (60xx,0040) Type 1
            overlay_origin (list[int]): Location of first overlay point [row, column] (60xx,0050) Type 1.
                1-based coordinates relative to image pixels.
            overlay_data (bytes): Overlay pixel data (60xx,3000) Type 1.
                1-bit overlay data, left to right, top to bottom.
            overlay_group (int): DICOM group number for overlay (default: 0x6000).
                Must be even number between 0x6000 and 0x601E.
            
        Returns:
            OverlayPlaneModule: New dataset instance with required data elements set
        """
        instance = cls()
        
        # Validate overlay group
        if overlay_group < 0x6000 or overlay_group > 0x601E or overlay_group % 2 != 0:
            raise ValueError("Overlay group must be even number between 0x6000 and 0x601E")
        
        # Set attributes using the specified group and proper tag format
        # Overlay elements use group-specific tags: (60xx,yy)
        from pydicom.tag import Tag
        from pydicom.dataelem import DataElement
        
        # Create group-specific tags
        rows_tag = Tag(overlay_group, 0x0010)
        cols_tag = Tag(overlay_group, 0x0011) 
        type_tag = Tag(overlay_group, 0x0040)
        origin_tag = Tag(overlay_group, 0x0050)
        bits_alloc_tag = Tag(overlay_group, 0x0100)
        bit_pos_tag = Tag(overlay_group, 0x0102)
        data_tag = Tag(overlay_group, 0x3000)
        
        # Set data elements using add_new for repeating groups
        instance.add_new(rows_tag, 'US', overlay_rows)
        instance.add_new(cols_tag, 'US', overlay_columns)
        instance.add_new(type_tag, 'CS', instance._format_enum_value(overlay_type))
        instance.add_new(origin_tag, 'SS', overlay_origin)
        instance.add_new(bits_alloc_tag, 'US', 1)  # Always 1 for overlays
        instance.add_new(bit_pos_tag, 'US', 0)     # Always 0 for overlays
        instance.add_new(data_tag, 'OW', overlay_data)
        
        # Store group for reference
        instance._overlay_group = overlay_group
        
        return instance
    
    def with_optional_elements(
        self,
        overlay_description: str | None = None,
        overlay_subtype: str | OverlaySubtype | None = None,
        overlay_label: str | None = None
    ) -> 'OverlayPlaneModule':
        """Add optional (Type 3) data elements.
        
        Args:
            overlay_description (str | None): User-defined comments about overlay (60xx,0022) Type 3
            overlay_subtype (str | OverlaySubtype | None): Overlay purpose identifier (60xx,0045) Type 3
            overlay_label (str | None): User-defined text label for overlay (60xx,1500) Type 3
            
        Returns:
            OverlayPlaneModule: Self with optional elements added
        """
        from pydicom.tag import Tag
        
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        
        if overlay_description is not None:
            desc_tag = Tag(overlay_group, 0x0022)
            self.add_new(desc_tag, 'LO', overlay_description)
            
        if overlay_subtype is not None:
            subtype_tag = Tag(overlay_group, 0x0045)
            self.add_new(subtype_tag, 'LO', self._format_enum_value(overlay_subtype))
            
        if overlay_label is not None:
            label_tag = Tag(overlay_group, 0x1500)
            self.add_new(label_tag, 'LO', overlay_label)
        return self
    
    def with_roi_statistics(
        self,
        roi_area: int | None = None,
        roi_mean: float | None = None,
        roi_standard_deviation: float | None = None
    ) -> 'OverlayPlaneModule':
        """Add ROI statistical parameters (Type 3, relevant when overlay type is ROI).
        
        Args:
            roi_area (int | None): Number of pixels in ROI area (60xx,1301) Type 3
            roi_mean (float | None): ROI mean pixel value (60xx,1302) Type 3
            roi_standard_deviation (float | None): ROI standard deviation (60xx,1303) Type 3
            
        Returns:
            OverlayPlaneModule: Self with ROI statistics added
        """
        from pydicom.tag import Tag
        
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        
        if roi_area is not None:
            area_tag = Tag(overlay_group, 0x1301)
            self.add_new(area_tag, 'IS', str(roi_area))
            
        if roi_mean is not None:
            mean_tag = Tag(overlay_group, 0x1302)
            self.add_new(mean_tag, 'DS', str(roi_mean))
            
        if roi_standard_deviation is not None:
            std_tag = Tag(overlay_group, 0x1303)
            self.add_new(std_tag, 'DS', str(roi_standard_deviation))
        return self
    
    @staticmethod
    def calculate_overlay_data_size(rows: int, columns: int) -> int:
        """Calculate expected overlay data size in bytes.
        
        Args:
            rows (int): Number of overlay rows
            columns (int): Number of overlay columns
            
        Returns:
            int: Expected data size in bytes (padded to even length)
        """
        total_bits = rows * columns
        total_bytes = (total_bits + 7) // 8  # Round up to nearest byte
        # DICOM requires even length
        return total_bytes + (total_bytes % 2)
    
    @property
    def is_graphics_overlay(self) -> bool:
        """Check if this is a graphics overlay."""
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        type_tag = Tag(overlay_group, 0x0040)
        return type_tag in self and self[type_tag].value == "G"
    
    @property
    def is_roi_overlay(self) -> bool:
        """Check if this is an ROI overlay."""
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        type_tag = Tag(overlay_group, 0x0040)
        return type_tag in self and self[type_tag].value == "R"
    
    @property
    def has_roi_statistics(self) -> bool:
        """Check if ROI statistics are present."""
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        roi_tags = [
            Tag(overlay_group, 0x1301),  # ROIArea
            Tag(overlay_group, 0x1302),  # ROIMean
            Tag(overlay_group, 0x1303)   # ROIStandardDeviation
        ]
        return any(tag in self for tag in roi_tags)
    
    @property
    def overlay_pixel_count(self) -> int | None:
        """Calculate total number of overlay pixels."""
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        rows_tag = Tag(overlay_group, 0x0010)
        cols_tag = Tag(overlay_group, 0x0011)
        
        if rows_tag not in self or cols_tag not in self:
            return None
        return self[rows_tag].value * self[cols_tag].value
    
    @property
    def expected_data_size(self) -> int | None:
        """Calculate expected overlay data size."""
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        rows_tag = Tag(overlay_group, 0x0010)
        cols_tag = Tag(overlay_group, 0x0011)
        
        if rows_tag not in self or cols_tag not in self:
            return None
        return self.calculate_overlay_data_size(self[rows_tag].value, self[cols_tag].value)
    
    @property
    def actual_data_size(self) -> int | None:
        """Get actual overlay data size."""
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        data_tag = Tag(overlay_group, 0x3000)
        
        if data_tag not in self:
            return None
        return len(self[data_tag].value)
    
    @property
    def overlay_group(self) -> int:
        """Get the overlay group number."""
        return getattr(self, '_overlay_group', 0x6000)
    
    def get_overlay_origin_coordinates(self) -> tuple[int, int] | None:
        """Get overlay origin as (row, column) tuple.
        
        Returns:
            tuple[int, int] | None: (row, column) coordinates or None if not available
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        origin_tag = Tag(overlay_group, 0x0050)
        
        if origin_tag not in self:
            return None
        
        origin = self[origin_tag].value
        if len(origin) != 2:
            return None
        return (origin[0], origin[1])
    
    def is_overlay_within_image(self, image_rows: int, image_columns: int) -> bool:
        """Check if overlay fits within image boundaries.
        
        Args:
            image_rows (int): Number of rows in associated image
            image_columns (int): Number of columns in associated image
            
        Returns:
            bool: True if overlay fits within image boundaries
        """
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        rows_tag = Tag(overlay_group, 0x0010)
        cols_tag = Tag(overlay_group, 0x0011)
        
        origin = self.get_overlay_origin_coordinates()
        if origin is None or rows_tag not in self or cols_tag not in self:
            return False
        
        origin_row, origin_col = origin
        overlay_rows = self[rows_tag].value
        overlay_cols = self[cols_tag].value
        
        # Calculate overlay boundaries
        overlay_end_row = origin_row + overlay_rows - 1
        overlay_end_col = origin_col + overlay_cols - 1
        
        # Check if overlay extends beyond image
        return (origin_row >= 1 and origin_col >= 1 and
                overlay_end_row <= image_rows and overlay_end_col <= image_columns)
    
    # Compatibility properties for tests and easier access
    @property
    def OverlayRows(self) -> int:
        """Get overlay rows."""
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        rows_tag = Tag(overlay_group, 0x0010)
        if rows_tag in self:
            return self[rows_tag].value
        raise AttributeError("OverlayRows not set")
    
    @property 
    def OverlayColumns(self) -> int:
        """Get overlay columns."""
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        cols_tag = Tag(overlay_group, 0x0011)
        if cols_tag in self:
            return self[cols_tag].value
        raise AttributeError("OverlayColumns not set")
    
    @property
    def OverlayType(self) -> str:
        """Get overlay type."""
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        type_tag = Tag(overlay_group, 0x0040)
        if type_tag in self:
            return self[type_tag].value
        raise AttributeError("OverlayType not set")
    
    @property
    def OverlayOrigin(self) -> list:
        """Get overlay origin."""
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        origin_tag = Tag(overlay_group, 0x0050)
        if origin_tag in self:
            return self[origin_tag].value
        raise AttributeError("OverlayOrigin not set")
    
    @property
    def OverlayBitsAllocated(self) -> int:
        """Get overlay bits allocated."""
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        bits_tag = Tag(overlay_group, 0x0100)
        if bits_tag in self:
            return self[bits_tag].value
        raise AttributeError("OverlayBitsAllocated not set")
    
    @property
    def OverlayBitPosition(self) -> int:
        """Get overlay bit position."""
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        pos_tag = Tag(overlay_group, 0x0102)
        if pos_tag in self:
            return self[pos_tag].value
        raise AttributeError("OverlayBitPosition not set")
    
    @property
    def OverlayData(self) -> bytes:
        """Get overlay data."""
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        data_tag = Tag(overlay_group, 0x3000)
        if data_tag in self:
            return self[data_tag].value
        raise AttributeError("OverlayData not set")
    
    @property
    def OverlayDescription(self) -> str:
        """Get overlay description."""
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        desc_tag = Tag(overlay_group, 0x0022)
        if desc_tag in self:
            return self[desc_tag].value
        raise AttributeError("OverlayDescription not set")
    
    @property
    def OverlayLabel(self) -> str:
        """Get overlay label."""
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        label_tag = Tag(overlay_group, 0x1500)
        if label_tag in self:
            return self[label_tag].value
        raise AttributeError("OverlayLabel not set")
    
    @property
    def OverlaySubtype(self) -> str:
        """Get overlay subtype."""
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        subtype_tag = Tag(overlay_group, 0x0045)
        if subtype_tag in self:
            return self[subtype_tag].value
        raise AttributeError("OverlaySubtype not set")
    
    @property
    def ROIArea(self) -> int:
        """Get ROI area."""
        from pydicom.tag import Tag
        overlay_group = getattr(self, '_overlay_group', 0x6000)
        area_tag = Tag(overlay_group, 0x1301)
        if area_tag in self:
            return int(self[area_tag].value)
        raise AttributeError("ROIArea not set")

    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate module data against DICOM standard."""
        return OverlayPlaneValidator.validate(self, config)
