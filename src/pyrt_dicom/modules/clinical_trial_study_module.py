"""
Clinical Trial Study Module - DICOM PS3.3 C.7.2.3

The Clinical Trial Study Module contains attributes that identify a Study 
in the context of a clinical trial or research.
"""
from typing import Optional, List, Dict, Any
from pydicom import Dataset
from .base_module import BaseModule
from ..enums.clinical_trial_enums import ConsentForDistribution, DistributionType, LongitudinalTemporalEventType
from ..validators.modules.base_validator import ValidationConfig
from ..validators.modules.clinical_trial_study_validator import ClinicalTrialStudyValidator


class ClinicalTrialStudyModule(BaseModule):
    """Clinical Trial Study Module implementation for DICOM PS3.3 C.7.2.3.
    
    Inherits from BaseModule to provide native DICOM data handling.
    Contains attributes that identify a Study in the context of a clinical trial or research.
    
    Usage:
        # Create with required elements
        trial_study = ClinicalTrialStudyModule.from_required_elements(
            clinical_trial_time_point_id="TP001"
        )
        
        # Add optional elements
        trial_study.with_optional_elements(
            clinical_trial_time_point_description="Baseline imaging",
            longitudinal_temporal_offset_from_event=0.0
        )
        
        # Add temporal event information
        trial_study.with_temporal_event(
            longitudinal_temporal_offset_from_event=30.5,
            longitudinal_temporal_event_type=LongitudinalTemporalEventType.ENROLLMENT
        )
        
        # Add consent information
        trial_study.with_consent_for_clinical_trial_use([
            trial_study.create_consent_item(
                distribution_type=DistributionType.NAMED_PROTOCOL,
                consent_for_distribution_flag=ConsentForDistribution.YES
            )
        ])
        
        # Validate
        result = trial_study.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        clinical_trial_time_point_id: str = ""
    ) -> 'ClinicalTrialStudyModule':
        """Create ClinicalTrialStudyModule from all required (Type 1 and Type 2) data elements.
        
        Args:
            clinical_trial_time_point_id (str): Identifier specifying Studies grouped as clinical time point (0012,0050) Type 2
            
        Returns:
            ClinicalTrialStudyModule: New dataset instance with required data elements set
        """
        instance = cls()
        instance.ClinicalTrialTimePointID = clinical_trial_time_point_id
        return instance
    
    def with_optional_elements(
        self,
        issuer_of_clinical_trial_time_point_id: Optional[str] = None,
        clinical_trial_time_point_description: Optional[str] = None,
        clinical_trial_time_point_type_code_sequence: Optional[List[Dict[str, Any]]] = None,
        longitudinal_temporal_offset_from_event: Optional[float] = None,
        consent_for_clinical_trial_use_sequence: Optional[List[Dict[str, Any]]] = None
    ) -> 'ClinicalTrialStudyModule':
        """Add optional (Type 3) data elements without conditional requirements.
        
        Args:
            issuer_of_clinical_trial_time_point_id (str | None): Assigning Authority for time point ID (0012,0055) Type 3
            clinical_trial_time_point_description (str | None): Description of clinical time point (0012,0051) Type 3
            clinical_trial_time_point_type_code_sequence (list[dict[str, any]] | None): Pre-defined time point type (0012,0054) Type 3
            longitudinal_temporal_offset_from_event (float | None): Offset in days from event (0012,0052) Type 3
            consent_for_clinical_trial_use_sequence (list[dict[str, any]] | None): Consent information (0012,0083) Type 3
            
        Returns:
            ClinicalTrialStudyModule: Self with optional elements added
        """
        self._set_attribute_if_not_none('IssuerOfClinicalTrialTimePointID', issuer_of_clinical_trial_time_point_id)
        self._set_attribute_if_not_none('ClinicalTrialTimePointDescription', clinical_trial_time_point_description)
        self._set_attribute_if_not_none('ClinicalTrialTimePointTypeCodeSequence', clinical_trial_time_point_type_code_sequence)
        self._set_attribute_if_not_none('LongitudinalTemporalOffsetFromEvent', longitudinal_temporal_offset_from_event)
        self._set_attribute_if_not_none('ConsentForClinicalTrialUseSequence', consent_for_clinical_trial_use_sequence)
        return self
    
    def with_temporal_event(
        self,
        longitudinal_temporal_offset_from_event: float,
        longitudinal_temporal_event_type: str | LongitudinalTemporalEventType
    ) -> 'ClinicalTrialStudyModule':
        """Add temporal event information with required conditional logic.
        
        Note: Longitudinal Temporal Event Type (0012,0053) is Type 1C - required if 
        Longitudinal Temporal Offset from Event is present.
        
        Args:
            longitudinal_temporal_offset_from_event (float): Offset in days from event (0012,0052) Type 3
            longitudinal_temporal_event_type (str | LongitudinalTemporalEventType): Type of event (0012,0053) Type 1C
            
        Returns:
            ClinicalTrialStudyModule: Self with temporal event elements added
        """
        self.LongitudinalTemporalOffsetFromEvent = longitudinal_temporal_offset_from_event
        self.LongitudinalTemporalEventType = self._format_enum_value(longitudinal_temporal_event_type)
        return self
    
    def with_consent_for_clinical_trial_use(
        self,
        consent_for_clinical_trial_use_sequence: List[Dict[str, Any]]
    ) -> 'ClinicalTrialStudyModule':
        """Add consent for clinical trial use information.
        
        Args:
            consent_for_clinical_trial_use_sequence (list[dict[str, any]]): Consent information (0012,0083) Type 3
            
        Returns:
            ClinicalTrialStudyModule: Self with consent elements added
        """
        self.ConsentForClinicalTrialUseSequence = consent_for_clinical_trial_use_sequence
        return self
    
    @staticmethod
    def create_consent_item(
        consent_for_distribution_flag: ConsentForDistribution,
        distribution_type: Optional[DistributionType] = None,
        clinical_trial_protocol_id: Optional[str] = None,
        issuer_of_clinical_trial_protocol_id: Optional[str] = None
    ) -> Dataset:
        """Create an item for Consent for Clinical Trial Use Sequence (0012,0083).
        
        Args:
            consent_for_distribution_flag (ConsentForDistribution): Whether consent granted (0012,0085) Type 1
            distribution_type (DistributionType | None): Type of distribution (0012,0084) Type 1C
            clinical_trial_protocol_id (str | None): Protocol identifier (0012,0020) Type 1C
            issuer_of_clinical_trial_protocol_id (str | None): Assigning Authority (0012,0022) Type 3
            
        Returns:
            pydicom.dataset.Dataset: Sequence item with consent information
            
        Raises:
            ValueError: If required conditional elements are missing
        """
        item = Dataset()
        item.ConsentForDistributionFlag = consent_for_distribution_flag.value
        
        # Type 1C requirement: Distribution Type required if consent is YES or WITHDRAWN
        if consent_for_distribution_flag in [ConsentForDistribution.YES, ConsentForDistribution.WITHDRAWN]:
            if distribution_type is None:
                raise ValueError("distribution_type is required when consent_for_distribution_flag is YES or WITHDRAWN")
            item.DistributionType = distribution_type.value
            
            # Type 1C requirement: Protocol ID required if distribution type is NAMED_PROTOCOL
            # and protocol is not specified in Clinical Trial Subject Module
            if distribution_type == DistributionType.NAMED_PROTOCOL and clinical_trial_protocol_id is not None:
                item.ClinicalTrialProtocolID = clinical_trial_protocol_id
        
        if issuer_of_clinical_trial_protocol_id is not None:
            item.IssuerOfClinicalTrialProtocolID = issuer_of_clinical_trial_protocol_id
        
        return item
    
    @staticmethod
    def create_time_point_type_code_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str
    ) -> Dataset:
        """Create an item for Clinical Trial Time Point Type Code Sequence (0012,0054).
        
        Args:
            code_value (str): Code value for time point type
            coding_scheme_designator (str): Coding scheme designator
            code_meaning (str): Code meaning
            
        Returns:
            pydicom.dataset.Dataset: Sequence item with time point type code
        """
        item = Dataset()
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme_designator
        item.CodeMeaning = code_meaning
        return item
    
    @property
    def has_temporal_event_info(self) -> bool:
        """Check if temporal event information is present.
        
        Returns:
            bool: True if temporal event elements are present
        """
        return (hasattr(self, 'LongitudinalTemporalOffsetFromEvent') or 
                hasattr(self, 'LongitudinalTemporalEventType'))
    
    @property
    def has_consent_info(self) -> bool:
        """Check if consent information is present.
        
        Returns:
            bool: True if consent sequence is present
        """
        return hasattr(self, 'ConsentForClinicalTrialUseSequence')
    
    @property
    def has_time_point_description(self) -> bool:
        """Check if time point description information is present.
        
        Returns:
            bool: True if time point description elements are present
        """
        return any(hasattr(self, attr) for attr in [
            'ClinicalTrialTimePointDescription',
            'ClinicalTrialTimePointTypeCodeSequence'
        ])
    
    def validate(self, config: Optional[ValidationConfig] = None) -> Dict[str, List[str]]:
        """Validate this Clinical Trial Study Module instance.

        Args:
            config (ValidationConfig | None): Optional validation configuration

        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        return ClinicalTrialStudyValidator.validate(self, config)
