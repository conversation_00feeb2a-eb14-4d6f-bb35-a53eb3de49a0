"""
General Acquisition Module - DICOM PS3.3 C.7.10.1

The General Acquisition Module contains attributes that identify and describe 
general information about an Acquisition.
"""
from datetime import datetime, date
from .base_module import BaseModule
from ..validators.modules.general_acquisition_validator import GeneralAcquisitionValidator
from ..validators.modules.base_validator import ValidationConfig


class GeneralAcquisitionModule(BaseModule):
    """General Acquisition Module implementation for DICOM PS3.3 C.7.10.1.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Contains attributes that identify and describe general information about 
    an Acquisition.
    
    Usage:
        # Create with required elements (all Type 3, so empty instance is valid)
        general_acquisition = GeneralAcquisitionModule.from_required_elements()
        
        # Add optional elements
        general_acquisition.with_optional_elements(
            acquisition_uid="1.2.3.4.5.6.7.8.9.10.11.12",
            acquisition_number=1,
            acquisition_date="20240101",
            acquisition_time="120000",
            acquisition_datetime="20240101120000.000000",
            acquisition_duration=30.5,
            images_in_acquisition=100,
            irradiation_event_uid="1.2.3.4.5.6.7.8.9.10.11.13"
        )
        
        # Validate
        result = general_acquisition.validate()
    """

    @classmethod
    def from_required_elements(cls) -> 'GeneralAcquisitionModule':
        """Create General Acquisition Module with all required elements.
        
        Note: All elements in this module are Type 3 (optional), so no required elements.
        """
        instance = cls()
        return instance
    
    def with_optional_elements(
        self,
        # Type 3 elements as kwargs with None defaults (truly optional)
        acquisition_uid: str | None = None,
        acquisition_number: int | None = None,
        acquisition_date: str | datetime | date | None = None,
        acquisition_time: str | datetime | None = None,
        acquisition_datetime: str | datetime | None = None,
        acquisition_duration: float | None = None,
        images_in_acquisition: int | None = None,
        irradiation_event_uid: str | None = None
    ) -> 'GeneralAcquisitionModule':
        """Add optional (Type 3) elements."""
        # Use BaseModule helper methods
        self._set_attribute_if_not_none('AcquisitionUID', acquisition_uid)
        self._set_attribute_if_not_none('AcquisitionNumber', acquisition_number)
        
        # Format date value if needed
        if acquisition_date is not None:
            formatted_date = self._format_date_value(acquisition_date)
            self._set_attribute_if_not_none('AcquisitionDate', formatted_date)
        
        # Format time value if needed
        if acquisition_time is not None:
            formatted_time = self._format_time_value(acquisition_time)
            self._set_attribute_if_not_none('AcquisitionTime', formatted_time)
        
        # Format datetime value if needed
        if acquisition_datetime is not None:
            formatted_datetime = self._format_datetime_value(acquisition_datetime)
            self._set_attribute_if_not_none('AcquisitionDateTime', formatted_datetime)
        
        self._set_attribute_if_not_none('AcquisitionDuration', acquisition_duration)
        self._set_attribute_if_not_none('ImagesInAcquisition', images_in_acquisition)
        self._set_attribute_if_not_none('IrradiationEventUID', irradiation_event_uid)
        
        return self
    
    def _format_datetime_value(self, datetime_value: str | datetime) -> str:
        """Format datetime value to DICOM DT format (YYYYMMDDHHMMSS.FFFFFF).
        
        Args:
            datetime_value: Datetime value to format
            
        Returns:
            Formatted datetime string
        """
        if isinstance(datetime_value, datetime):
            return datetime_value.strftime("%Y%m%d%H%M%S.%f")
        elif isinstance(datetime_value, str):
            # Assume already formatted or validate format
            return datetime_value
        else:
            return str(datetime_value)
    
    @property
    def has_acquisition_identification(self) -> bool:
        """Check if acquisition identification is present.
        
        Returns:
            bool: True if UID or number is present
        """
        return (hasattr(self, 'AcquisitionUID') or 
                hasattr(self, 'AcquisitionNumber'))
    
    @property
    def has_timing_information(self) -> bool:
        """Check if timing information is present.
        
        Returns:
            bool: True if date, time, datetime, or duration is present
        """
        return (hasattr(self, 'AcquisitionDate') or 
                hasattr(self, 'AcquisitionTime') or
                hasattr(self, 'AcquisitionDateTime') or
                hasattr(self, 'AcquisitionDuration'))
    
    @property
    def has_irradiation_event(self) -> bool:
        """Check if irradiation event information is present.
        
        Returns:
            bool: True if Irradiation Event UID is present
        """
        return hasattr(self, 'IrradiationEventUID')
    
    @property
    def has_image_count(self) -> bool:
        """Check if image count information is present.
        
        Returns:
            bool: True if Images in Acquisition is present
        """
        return hasattr(self, 'ImagesInAcquisition')
    
    @property
    def acquisition_uid_value(self) -> str | None:
        """Get the acquisition UID value.
        
        Returns:
            str | None: Acquisition UID value or None if not present
        """
        return getattr(self, 'AcquisitionUID', None)
    
    @property
    def acquisition_number_value(self) -> int | None:
        """Get the acquisition number value.
        
        Returns:
            int | None: Acquisition number value or None if not present
        """
        return getattr(self, 'AcquisitionNumber', None)
    
    @property
    def acquisition_date_value(self) -> str | None:
        """Get the acquisition date value.
        
        Returns:
            str | None: Acquisition date value or None if not present
        """
        return getattr(self, 'AcquisitionDate', None)
    
    @property
    def acquisition_time_value(self) -> str | None:
        """Get the acquisition time value.
        
        Returns:
            str | None: Acquisition time value or None if not present
        """
        return getattr(self, 'AcquisitionTime', None)
    
    @property
    def acquisition_datetime_value(self) -> str | None:
        """Get the acquisition datetime value.
        
        Returns:
            str | None: Acquisition datetime value or None if not present
        """
        return getattr(self, 'AcquisitionDateTime', None)
    
    @property
    def acquisition_duration_value(self) -> float | None:
        """Get the acquisition duration value in seconds.
        
        Returns:
            float | None: Acquisition duration in seconds or None if not present
        """
        return getattr(self, 'AcquisitionDuration', None)
    
    @property
    def images_in_acquisition_value(self) -> int | None:
        """Get the number of images in acquisition.
        
        Returns:
            int | None: Number of images or None if not present
        """
        return getattr(self, 'ImagesInAcquisition', None)
    
    @property
    def irradiation_event_uid_value(self) -> str | None:
        """Get the irradiation event UID value.
        
        Returns:
            str | None: Irradiation event UID value or None if not present
        """
        return getattr(self, 'IrradiationEventUID', None)
    
    def is_synchronized_with_external_clock(self) -> bool:
        """Check if acquisition time is synchronized with external clock.
        
        Note: This requires checking the Synchronization Module's 
        Acquisition Time Synchronized (0018,1800) attribute, which is 
        outside this module's scope.
        
        Returns:
            bool: False (cannot determine from this module alone)
        """
        # This would require access to the Synchronization Module
        # which is outside the scope of this module
        return False
    
    def get_acquisition_summary(self) -> dict[str, any]:
        """Get a summary of acquisition information.
        
        Returns:
            dict: Summary of acquisition attributes
        """
        summary = {}
        
        if self.has_acquisition_identification:
            summary['identification'] = {
                'uid': self.acquisition_uid_value,
                'number': self.acquisition_number_value
            }
        
        if self.has_timing_information:
            summary['timing'] = {
                'date': self.acquisition_date_value,
                'time': self.acquisition_time_value,
                'datetime': self.acquisition_datetime_value,
                'duration_seconds': self.acquisition_duration_value
            }
        
        if self.has_image_count:
            summary['images_count'] = self.images_in_acquisition_value
        
        if self.has_irradiation_event:
            summary['irradiation_event_uid'] = self.irradiation_event_uid_value
        
        return summary
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate this General Acquisition Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        return GeneralAcquisitionValidator.validate(self, config)
