"""
Cine Module - DICOM PS3.3 C.7.6.5

The Cine Module describes a Multi-frame Cine Image.
"""
from datetime import datetime
from pydicom import Dataset
from .base_module import BaseModule
from ..enums.image_enums import PreferredPlaybackSequencing, ChannelMode
from ..validators.modules.cine_validator import CineValidator
from ..validators.modules.base_validator import ValidationConfig


class CineModule(BaseModule):
    """Cine Module implementation for DICOM PS3.3 C.7.6.5.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Describes a Multi-frame Cine Image.
    
    Usage:
        # Create with conditional elements based on frame increment pointer
        cine = CineModule.from_frame_time(frame_time=33.33)  # 30 fps
        
        # Or create with frame time vector
        cine = CineModule.from_frame_time_vector(
            frame_time_vector=[0, 33.33, 33.33, 33.33, 33.33]
        )
        
        # Add optional elements
        cine.with_optional_elements(
            preferred_playback_sequencing=PreferredPlaybackSequencing.LOOPING,
            recommended_display_frame_rate=30.0,
            cine_rate=30.0
        )
        
        # Add audio channels if needed
        cine.with_audio_channels([
            cine.create_audio_channel_item(
                channel_identification_code=1,
                channel_mode=ChannelMode.MONO,
                source_code_value="MAIN",
                source_coding_scheme="DCM"
            )
        ])
        
        # Validate
        result = cine.validate()
    """
    
    @classmethod
    def from_required_elements(
        cls,
        frame_time: float | list[float]
    ) -> 'CineModule':
        """Create CineModule with no required elements.
        
        The Cine Module has no Type 1 or Type 2 elements - all elements are either
        Type 1C (conditional) or Type 3 (optional). Use from_frame_time() or 
        from_frame_time_vector() for conditional elements.
        
        Returns:
            CineModule: New empty module instance
        """
        if isinstance(frame_time, list):
            return cls._from_frame_time_vector(frame_time)
        else:
            return cls._from_frame_time(frame_time)

    @classmethod
    def _from_frame_time(
        cls,
        frame_time: float
    ) -> 'CineModule':
        """Create CineModule with Frame Time (Type 1C when Frame Increment Pointer points to it).
        
        Args:
            frame_time (float): Nominal time in msec per individual frame (0018,1063) Type 1C
            
        Returns:
            CineModule: New dataset instance with Frame Time set
        """
        instance = cls()
        instance.FrameTime = frame_time
        return instance
    
    @classmethod
    def _from_frame_time_vector(
        cls,
        frame_time_vector: list[float]
    ) -> 'CineModule':
        """Create CineModule with Frame Time Vector (Type 1C when Frame Increment Pointer points to it).
        
        Args:
            frame_time_vector (list[float]): Time increments in msec between frames (0018,1065) Type 1C.
                First frame always has time increment of 0.
            
        Returns:
            CineModule: New dataset instance with Frame Time Vector set
        """
        instance = cls()
        instance.FrameTimeVector = frame_time_vector
        return instance
    
    def with_audio_channels(
        self,
        audio_channels: list[dict[str, any]] | None = None
    ) -> 'CineModule':
        """Add multiplexed audio channels (Type 2C when transfer syntax contains audio).
        
        Args:
            audio_channels (list[dict[str, any]] | None): Audio channel descriptions (003A,0300) Type 2C.
                Each item should contain:
                - channel_identification_code (int): Channel reference (003A,0301) Type 1
                - channel_mode (str | ChannelMode): Channel mode (003A,0302) Type 1
                - channel_source_sequence (list[dict]): Channel source codes (003A,0208) Type 1
            
        Returns:
            CineModule: Self with audio channels added
        """
        if audio_channels is not None:
            sequence_items = []
            for channel in audio_channels:
                item = Dataset()
                if 'channel_identification_code' in channel:
                    item.ChannelIdentificationCode = channel['channel_identification_code']
                if 'channel_mode' in channel:
                    item.ChannelMode = self._format_enum_value(channel['channel_mode'])
                if 'channel_source_sequence' in channel:
                    # Convert source sequence dicts to Dataset objects if needed
                    source_items = []
                    for source in channel['channel_source_sequence']:
                        if isinstance(source, Dataset):
                            source_items.append(source)
                        else:
                            source_dataset = Dataset()
                            for key, value in source.items():
                                setattr(source_dataset, key, value)
                            source_items.append(source_dataset)
                    item.ChannelSourceSequence = source_items
                sequence_items.append(item)
            
            self.MultiplexedAudioChannelsDescriptionCodeSequence = sequence_items
        
        return self
    
    def with_optional_elements(
        self,
        preferred_playback_sequencing: int | PreferredPlaybackSequencing | None = None,
        start_trim: int | None = None,
        stop_trim: int | None = None,
        recommended_display_frame_rate: float | None = None,
        cine_rate: float | None = None,
        frame_delay: float | None = None,
        image_trigger_delay: float | None = None,
        effective_duration: float | None = None,
        actual_frame_duration: float | None = None
    ) -> 'CineModule':
        """Add optional (Type 3) data elements.
        
        Args:
            preferred_playback_sequencing (int | PreferredPlaybackSequencing | None): Playback method (0018,1244) Type 3
            start_trim (int | None): First frame to display (0008,2142) Type 3
            stop_trim (int | None): Last frame to display (0008,2143) Type 3
            recommended_display_frame_rate (float | None): Display rate in frames/sec (0008,2144) Type 3
            cine_rate (float | None): Frames per second (0018,0040) Type 3
            frame_delay (float | None): Time from Content Time to first frame in msec (0018,1066) Type 3
            image_trigger_delay (float | None): Delay from trigger to first frame in msec (0018,1067) Type 3
            effective_duration (float | None): Total acquisition time in seconds (0018,0072) Type 3
            actual_frame_duration (float | None): Elapsed acquisition time per frame in msec (0018,1242) Type 3
            
        Returns:
            CineModule: Self with optional elements added
        """
        if preferred_playback_sequencing is not None:
            self.PreferredPlaybackSequencing = self._format_enum_value(preferred_playback_sequencing)
        self._set_attribute_if_not_none('StartTrim', start_trim)
        self._set_attribute_if_not_none('StopTrim', stop_trim)
        self._set_attribute_if_not_none('RecommendedDisplayFrameRate', recommended_display_frame_rate)
        self._set_attribute_if_not_none('CineRate', cine_rate)
        self._set_attribute_if_not_none('FrameDelay', frame_delay)
        self._set_attribute_if_not_none('ImageTriggerDelay', image_trigger_delay)
        self._set_attribute_if_not_none('EffectiveDuration', effective_duration)
        self._set_attribute_if_not_none('ActualFrameDuration', actual_frame_duration)
        return self
    
    @staticmethod
    def create_audio_channel_item(
        channel_id: int,
        channel_mode: str | ChannelMode,
        source_code_value: str,
        source_coding_scheme: str = "DCM",
        source_code_meaning: str | None = None
    ) -> Dataset:
        """Create audio channel sequence item.
        
        Args:
            channel_id (int): Channel identification code (1 for main, 2+ for additional)
            channel_mode (str | ChannelMode): Channel mode (MONO/STEREO)
            source_code_value (str): Audio source code value
            source_coding_scheme (str): Coding scheme designator (default: "DCM")
            source_code_meaning (str | None): Human-readable meaning
            
        Returns:
            Dataset: Audio channel sequence item
        """
        channel_source_item = Dataset()
        channel_source_item.CodeValue = source_code_value
        channel_source_item.CodingSchemeDesignator = source_coding_scheme
        if source_code_meaning:
            channel_source_item.CodeMeaning = source_code_meaning
        
        item = Dataset()
        item.ChannelIdentificationCode = channel_id
        item.ChannelMode = channel_mode.value if hasattr(channel_mode, 'value') else str(channel_mode)
        item.ChannelSourceSequence = [channel_source_item]
        return item
    
    @property
    def uses_frame_time(self) -> bool:
        """Check if Frame Time is present."""
        return hasattr(self, 'FrameTime')
    
    @property
    def uses_frame_time_vector(self) -> bool:
        """Check if Frame Time Vector is present."""
        return hasattr(self, 'FrameTimeVector')
    
    @property
    def has_audio_channels(self) -> bool:
        """Check if audio channels are defined."""
        return hasattr(self, 'MultiplexedAudioChannelsDescriptionCodeSequence')
    
    @property
    def audio_channel_count(self) -> int:
        """Get number of audio channels."""
        if not self.has_audio_channels:
            return 0
        return len(self.MultiplexedAudioChannelsDescriptionCodeSequence)
    
    @property
    def effective_frame_rate(self) -> float | None:
        """Calculate effective frame rate from available timing information."""
        if hasattr(self, 'CineRate'):
            return self.CineRate
        elif hasattr(self, 'RecommendedDisplayFrameRate'):
            return self.RecommendedDisplayFrameRate
        elif hasattr(self, 'FrameTime'):
            # Convert from msec per frame to frames per second
            return 1000.0 / self.FrameTime if self.FrameTime > 0 else None
        return None
    
    def calculate_frame_relative_time(self, frame_number: int) -> float | None:
        """Calculate relative time for a specific frame.
        
        Args:
            frame_number (int): Frame number (1-based)
            
        Returns:
            float | None: Relative time in msec, or None if cannot calculate
        """
        if frame_number < 1:
            return None
        
        frame_delay = getattr(self, 'FrameDelay', 0)
        
        if self.uses_frame_time:
            # Frame 'Relative Time' (n) = Frame Delay + Frame Time * (n-1)
            return frame_delay + self.FrameTime * (frame_number - 1)
        elif self.uses_frame_time_vector:
            # Sum time increments up to frame n
            if frame_number > len(self.FrameTimeVector):
                return None
            return frame_delay + sum(self.FrameTimeVector[:frame_number])
        
        return None
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate module data against DICOM standard."""
        return CineValidator.validate(self, config)
