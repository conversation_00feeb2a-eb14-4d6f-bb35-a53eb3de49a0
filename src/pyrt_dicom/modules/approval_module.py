"""
Approval Module - DICOM PS3.3 C.8.8.16

The Approval Module contains attributes that describe the approval status
of a DICOM object at the time the SOP Instance was created.
"""
from typing import Optional, Union, List, Dict
from datetime import datetime, date
from .base_module import BaseModule
from ..enums.approval_enums import ApprovalStatus
from ..validators.modules.approval_validator import ApprovalValidator
from ..validators.modules.base_validator import ValidationConfig


class ApprovalModule(BaseModule):
    """Approval Module implementation for DICOM PS3.3 C.8.8.16.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Contains attributes that describe the approval status of a DICOM object
    at the time the SOP Instance was created.
    
    Usage:
        # Create with approved status
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20240101",
            review_time="120000",
            reviewer_name="<PERSON><PERSON> <PERSON>"
        )
        
        # Create with unapproved status (no review info needed)
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.UNAPPROVED
        )
        
        # Validate
        result = approval.validate()
    """
    
    @classmethod
    def from_required_elements(
        cls,
        approval_status: Union[ApprovalStatus, str]
    ) -> 'ApprovalModule':
        """Create module with required elements.
        
        Args:
            approval_status: Approval status at the time the SOP Instance was created
            
        Returns:
            ApprovalModule: New module instance with required data elements set
        """
        instance = cls()
        instance.ApprovalStatus = instance._format_enum_value(approval_status)
        return instance
    
    def with_review_information(
        self,
        review_date: Union[str, date, datetime],
        review_time: Union[str, datetime],
        reviewer_name: str
    ) -> 'ApprovalModule':
        """Add review information (Type 2C).
        
        Required if Approval Status is APPROVED or REJECTED.
        
        Args:
            review_date: Date on which object was reviewed (YYYYMMDD format)
            review_time: Time at which object was reviewed (HHMMSS format)
            reviewer_name: Name of person who reviewed object
            
        Returns:
            ApprovalModule: Self for method chaining
        """
        self.ReviewDate = self._format_date_value(review_date)
        self.ReviewTime = self._format_time_value(review_time)
        self.ReviewerName = reviewer_name
        return self
    
    def with_optional_elements(
        self,
        review_date: Optional[str] = None,
        review_time: Optional[str] = None,
        reviewer_name: Optional[str] = None,
    ) -> 'ApprovalModule':
        """Add extended approval elements (commonly used but not in core DICOM standard).
        
        Note: The core DICOM PS3.3 C.8.8.16 Approval Module only defines review information
        as Type 2C conditional elements. This method provides additional commonly used
        approval tracking elements for extended functionality.
        
        Args:
            review_date (str | None): Review date if available (matches with_review_information)
            review_time (str | None): Review time if available (matches with_review_information)
            reviewer_name (str | None): Reviewer name if available (matches with_review_information)
            
        Returns:
            ApprovalModule: Self for method chaining
        """
        # Set review information if provided (Type 2C elements)
        if review_date is not None:
            self.ReviewDate = self._format_date_value(review_date)
        if review_time is not None:
            self.ReviewTime = self._format_time_value(review_time)
        if reviewer_name is not None:
            self.ReviewerName = reviewer_name
        
        return self
    
    @property
    def is_approved(self) -> bool:
        """Check if object is approved.
        
        Returns:
            bool: True if approval status is APPROVED
        """
        return (hasattr(self, 'ApprovalStatus') and 
                self.ApprovalStatus == ApprovalStatus.APPROVED.value)
    
    @property
    def is_rejected(self) -> bool:
        """Check if object is rejected.
        
        Returns:
            bool: True if approval status is REJECTED
        """
        return (hasattr(self, 'ApprovalStatus') and 
                self.ApprovalStatus == ApprovalStatus.REJECTED.value)
    
    @property
    def is_unapproved(self) -> bool:
        """Check if object is unapproved.
        
        Returns:
            bool: True if approval status is UNAPPROVED
        """
        return (hasattr(self, 'ApprovalStatus') and 
                self.ApprovalStatus == ApprovalStatus.UNAPPROVED.value)
    
    @property
    def requires_review_information(self) -> bool:
        """Check if review information is required.
        
        Returns:
            bool: True if approval status requires review information
        """
        return self.is_approved or self.is_rejected
    
    @property
    def has_review_information(self) -> bool:
        """Check if review information is present.
        
        Returns:
            bool: True if all review information fields are present
        """
        return (hasattr(self, 'ReviewDate') and 
                hasattr(self, 'ReviewTime') and 
                hasattr(self, 'ReviewerName'))
    
    @property
    def is_configured(self) -> bool:
        """Check if module is properly configured.
        
        Returns:
            bool: True if approval status is present
        """
        return hasattr(self, 'ApprovalStatus')
    
    def validate(self, config: Optional[ValidationConfig] = None) -> Dict[str, List[str]]:
        """Validate this Approval Module instance.
        
        Args:
            config: Optional validation configuration
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        return ApprovalValidator.validate(self, config)
