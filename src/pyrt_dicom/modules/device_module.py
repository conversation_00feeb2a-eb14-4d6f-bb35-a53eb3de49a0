"""
Device Module - DICOM PS3.3 C.7.6.12

The Device Module identifies and describes devices or calibration objects 
(e.g., catheters, markers, baskets) or other quality control materials 
that are associated with a Study and/or image.
"""
from pydicom import Dataset
from .base_module import BaseModule
from ..enums.equipment_enums import DeviceDiameterUnits
from ..validators.modules.device_validator import DeviceValidator
from ..validators.modules.base_validator import ValidationConfig


class DeviceModule(BaseModule):
    """Device Module implementation for DICOM PS3.3 C.7.6.12.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Identifies and describes devices or calibration objects that are associated 
    with a Study and/or image.
    
    Usage:
        # Create with required elements
        device = DeviceModule.from_required_elements(
            device_sequence=[
                DeviceModule.create_device_item(
                    code_value="A-04000",
                    coding_scheme_designator="SRT", 
                    code_meaning="Catheter"
                )
            ]
        )
        
        # Add device details with conditional elements
        device.with_device_details(
            device_diameter=2.5,
            device_diameter_units=DeviceDiameterUnits.MM
        )
        
        # Validate
        result = device.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        device_sequence: list[dict[str, any]]
    ) -> 'DeviceModule':
        """Create DeviceModule from all required (Type 1) data elements.
        
        Args:
            device_sequence (list[dict]): Sequence of devices used (0050,0010) Type 1.
                Each item must include Code Sequence Macro attributes.
                
        Returns:
            DeviceModule: New module instance with required data elements set
        """
        instance = cls()
        instance.DeviceSequence = device_sequence
        return instance
    
    def with_device_details(
        self,
        device_diameter: float | None = None,
        device_diameter_units: str | DeviceDiameterUnits | None = None
    ) -> 'DeviceModule':
        """Add device diameter with required units specification.
        
        Note: Device Diameter Units (0050,0017) is Type 2C - required if 
        Device Diameter (0050,0016) is present.
        
        Args:
            device_diameter (float | None): Unit diameter of device (0050,0016) Type 3
            device_diameter_units (str | DeviceDiameterUnits | None): Units for diameter (0050,0017) Type 2C
            
        Returns:
            DeviceModule: Self for method chaining
        """
        # Validate conditional requirement
        if device_diameter is not None and device_diameter_units is None:
            raise ValueError("Device Diameter Units (0050,0017) is required when Device Diameter (0050,0016) is present")
        
        # Update device sequence items with diameter information
        if hasattr(self, 'DeviceSequence') and device_diameter is not None:
            for item in self.DeviceSequence:
                item['DeviceDiameter'] = device_diameter
                item['DeviceDiameterUnits'] = self._format_enum_value(device_diameter_units)
        
        return self
    
    def with_optional_elements(self) -> 'DeviceModule':
        """There are no optional elements for this module. See DeviceModule.create_device_item() to create
        a device item to be added to the Device Sequence (0050,0010).
        
        Args:
            None
            
        Returns:
            DeviceModule: Self for method chaining
        """
        return self
    
    @staticmethod
    def create_device_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str,
        manufacturer: str | None = None,
        manufacturers_model_name: str | None = None,
        device_serial_number: str | None = None,
        device_id: str | None = None,
        device_length: float | None = None,
        device_diameter: float | None = None,
        device_diameter_units: str | DeviceDiameterUnits | None = None,
        device_volume: float | None = None,
        inter_marker_distance: float | None = None,
        device_description: str | None = None,
        date_of_manufacture: str | None = None
    ) -> Dataset:
        """Create device sequence item.

        Args:
            code_value (str): Code value for device type
            coding_scheme_designator (str): Coding scheme designator
            code_meaning (str): Code meaning
            manufacturer (str | None): Manufacturer of the device
            manufacturers_model_name (str | None): Manufacturer's model name
            device_serial_number (str | None): Device serial number
            device_id (str | None): User-supplied identifier
            device_length (float | None): Length in mm
            device_diameter (float | None): Diameter of device
            device_diameter_units (str | DeviceDiameterUnits | None): Units for diameter
            device_volume (float | None): Volume in ml
            inter_marker_distance (float | None): Distance between markers in mm
            device_description (str | None): Free form description
            date_of_manufacture (str | None): Date of manufacture

        Returns:
            Dataset: Device sequence item
        """
        # Validate conditional requirement
        if device_diameter is not None and device_diameter_units is None:
            raise ValueError("Device Diameter Units is required when Device Diameter is present")

        item = Dataset()
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme_designator
        item.CodeMeaning = code_meaning

        if manufacturer is not None:
            item.Manufacturer = manufacturer
        if manufacturers_model_name is not None:
            item.ManufacturerModelName = manufacturers_model_name
        if device_serial_number is not None:
            item.DeviceSerialNumber = device_serial_number
        if device_id is not None:
            item.DeviceID = device_id
        if device_length is not None:
            item.DeviceLength = device_length
        if device_diameter is not None:
            item.DeviceDiameter = device_diameter
            item.DeviceDiameterUnits = device_diameter_units.value if hasattr(device_diameter_units, 'value') else str(device_diameter_units)
        if device_volume is not None:
            item.DeviceVolume = device_volume
        if inter_marker_distance is not None:
            item.InterMarkerDistance = inter_marker_distance
        if device_description is not None:
            item.DeviceDescription = device_description
        if date_of_manufacture is not None:
            from datetime import datetime, date
            if isinstance(date_of_manufacture, (datetime, date)):
                item.DateOfManufacture = date_of_manufacture.strftime("%Y%m%d")
            else:
                item.DateOfManufacture = str(date_of_manufacture)

        return item
    
    @property
    def device_count(self) -> int:
        """Get the number of devices in the sequence.
        
        Returns:
            int: Number of device items
        """
        return len(getattr(self, 'DeviceSequence', []))
    
    @property
    def has_device_measurements(self) -> bool:
        """Check if any device has measurement data.
        
        Returns:
            bool: True if any device has length, diameter, or volume data
        """
        if not hasattr(self, 'DeviceSequence'):
            return False
        
        for item in self.DeviceSequence:
            if any(key in item for key in ['DeviceLength', 'DeviceDiameter', 'DeviceVolume', 'InterMarkerDistance']):
                return True
        return False
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate this Device Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        return DeviceValidator.validate(self, config)
