"""
Specimen Module - DICOM PS3.3 C.7.6.22

The Specimen Module contains attributes that identify one or more Specimens being imaged.
Specimens are physically managed by being placed in or on a container, and both specimens 
and containers have logical identifiers for workflow management.
"""
from pydicom import Dataset
from .base_module import BaseModule
from ..validators.modules.specimen_validator import SpecimenValidator
from ..validators.modules.base_validator import ValidationConfig
from ..enums.specimen_enums import ContainerComponentMaterial


class SpecimenModule(BaseModule):
    """Specimen Module implementation for DICOM PS3.3 C.7.6.22.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Contains attributes that identify one or more Specimens being imaged, including
    container information and detailed specimen descriptions.
    
    Usage:
        # Create with required elements
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[
                SpecimenModule.create_specimen_description_item(
                    specimen_identifier="SPEC_001",
                    specimen_uid="*******.*******.9.10"
                )
            ],
            issuer_of_container_identifier_sequence=[
                SpecimenModule.create_hierarchic_designator_item(
                    local_namespace_entity_id="LAB_001"
                )
            ],
            container_type_code_sequence=[
                SpecimenModule.create_code_sequence_item(
                    code_value="433466003",
                    coding_scheme_designator="SCT",
                    code_meaning="Microscope slide"
                )
            ]
        )
        
        # Add optional elements
        specimen.with_optional_elements(
            container_description="Standard microscope slide",
            alternate_container_identifier_sequence=[
                SpecimenModule.create_alternate_container_item(
                    container_identifier="ALT_SLIDE_001"
                )
            ]
        )
        
        # Validate
        result = specimen.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        container_identifier: str,
        specimen_description_sequence: list[dict[str, any]],
        issuer_of_container_identifier_sequence: list[dict[str, any]] = None,
        container_type_code_sequence: list[dict[str, any]] = None
    ) -> 'SpecimenModule':
        """Create Specimen Module from all required (Type 1) data elements.
        
        Args:
            container_identifier (str): Identifier for the container that contains 
                the specimen(s) being imaged (0040,0512) Type 1.
            specimen_description_sequence (list[dict]): Sequence of identifiers and 
                detailed description of the specimen(s) being imaged (0040,0560) Type 1.
                One or more Items shall be included.
            issuer_of_container_identifier_sequence (list[dict] | None): Organization 
                that assigned the Container Identifier (0040,0513) Type 2. Zero or one 
                Item shall be included.
            container_type_code_sequence (list[dict] | None): Type of container that 
                contains the specimen(s) being imaged (0040,0518) Type 2. Zero or one 
                Item shall be included.
            
        Returns:
            SpecimenModule: New dataset instance with required data elements set
        """
        instance = cls()
        instance.ContainerIdentifier = container_identifier
        instance.SpecimenDescriptionSequence = specimen_description_sequence

        # Type 2 elements are required but can be empty
        instance.IssuerOfContainerIdentifierSequence = issuer_of_container_identifier_sequence or []
        instance.ContainerTypeCodeSequence = container_type_code_sequence or []
        return instance
    
    def with_optional_elements(
        self,
        alternate_container_identifier_sequence: list[dict[str, any]] | None = None,
        container_description: str | None = None,
        container_component_sequence: list[dict[str, any]] | None = None
    ) -> 'SpecimenModule':
        """Add optional (Type 3) data elements.
        
        Args:
            alternate_container_identifier_sequence (list[dict] | None): Sequence of 
                alternate identifiers for the container (0040,0515) Type 3. These may 
                have been assigned by manufacturer or another institution.
            container_description (str | None): Description of the container (0040,051A) Type 3.
            container_component_sequence (list[dict] | None): Description of one or more 
                components of the container (0040,0520) Type 3. E.g., slide and coverslip.
            
        Returns:
            SpecimenModule: Self for method chaining
        """
        self._set_attribute_if_not_none('AlternateContainerIdentifierSequence', alternate_container_identifier_sequence)
        self._set_attribute_if_not_none('ContainerDescription', container_description)
        self._set_attribute_if_not_none('ContainerComponentSequence', container_component_sequence)
        return self

    @staticmethod
    def create_specimen_description_item(
        specimen_identifier: str,
        specimen_uid: str,
        issuer_of_specimen_identifier_sequence: list[dict[str, any]] | None = None,
        specimen_type_code_sequence: list[dict[str, any]] | None = None,
        specimen_short_description: str | None = None,
        specimen_detailed_description: str | None = None,
        specimen_preparation_sequence: list[dict[str, any]] | None = None,
        specimen_localization_content_item_sequence: list[dict[str, any]] | None = None
    ) -> Dataset:
        """Create specimen description sequence item.

        Args:
            specimen_identifier (str): Departmental information system identifier
                for the Specimen (0040,0551) Type 1.
            specimen_uid (str): Unique Identifier for Specimen (0040,0554) Type 1.
            issuer_of_specimen_identifier_sequence (list[dict] | None): Name or code
                for institution that assigned Specimen Identifier (0040,0562) Type 2.
            specimen_type_code_sequence (list[dict] | None): Specimen Type (0040,059A) Type 3.
            specimen_short_description (str | None): Short textual specimen description
                (0040,0600) Type 3.
            specimen_detailed_description (str | None): Detailed textual specimen
                description (0040,0602) Type 3.
            specimen_preparation_sequence (list[dict] | None): Process steps used to
                prepare specimen (0040,0610) Type 2.
            specimen_localization_content_item_sequence (list[dict] | None): Location
                of specimen in container/image (0040,0620) Type 1C.

        Returns:
            Dataset: Specimen description sequence item
        """
        item = Dataset()
        item.SpecimenIdentifier = specimen_identifier
        item.SpecimenUID = specimen_uid

        # Type 2 elements (required but can be empty)
        item.IssuerOfSpecimenIdentifierSequence = issuer_of_specimen_identifier_sequence or []
        item.SpecimenPreparationSequence = specimen_preparation_sequence or []

        # Type 3 elements (optional)
        if specimen_type_code_sequence is not None:
            item.SpecimenTypeCodeSequence = specimen_type_code_sequence
        if specimen_short_description is not None:
            item.SpecimenShortDescription = specimen_short_description
        if specimen_detailed_description is not None:
            item.SpecimenDetailedDescription = specimen_detailed_description
        if specimen_localization_content_item_sequence is not None:
            item.SpecimenLocalizationContentItemSequence = specimen_localization_content_item_sequence

        return item

    @staticmethod
    def create_hierarchic_designator_item(
        local_namespace_entity_id: str | None = None,
        universal_entity_id: str | None = None,
        universal_entity_id_type: str | None = None
    ) -> Dataset:
        """Create HL7v2 Hierarchic Designator Macro item for issuer sequences.

        Args:
            local_namespace_entity_id (str | None): Local Namespace Entity ID Type 1C
            universal_entity_id (str | None): Universal Entity ID Type 1C
            universal_entity_id_type (str | None): Universal Entity ID Type Type 1C

        Returns:
            Dataset: Hierarchic designator item
        """
        item = Dataset()

        if local_namespace_entity_id is not None:
            item.LocalNamespaceEntityID = local_namespace_entity_id
        if universal_entity_id is not None:
            item.UniversalEntityID = universal_entity_id
        if universal_entity_id_type is not None:
            item.UniversalEntityIDType = universal_entity_id_type

        return item

    @staticmethod
    def create_code_sequence_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str,
        coding_scheme_version: str | None = None,
        context_identifier: str | None = None,
        context_uid: str | None = None,
        mapping_resource: str | None = None,
        context_group_version: str | None = None,
        context_group_extension_flag: str | None = None,
        context_group_local_version: str | None = None,
        context_group_extension_creator_uid: str | None = None
    ) -> Dataset:
        """Create code sequence item for various code sequences.

        Args:
            code_value (str): Code Value (0008,0100) Type 1
            coding_scheme_designator (str): Coding Scheme Designator (0008,0102) Type 1
            code_meaning (str): Code Meaning (0008,0104) Type 1
            coding_scheme_version (str | None): Coding Scheme Version (0008,0103) Type 1C
            context_identifier (str | None): Context Identifier (0008,010F) Type 3
            context_uid (str | None): Context UID (0008,0117) Type 3
            mapping_resource (str | None): Mapping Resource (0008,0105) Type 1C
            context_group_version (str | None): Context Group Version (0008,0106) Type 1C
            context_group_extension_flag (str | None): Context Group Extension Flag (0008,010B) Type 3
            context_group_local_version (str | None): Context Group Local Version (0008,0107) Type 1C
            context_group_extension_creator_uid (str | None): Context Group Extension Creator UID (0008,010D) Type 1C

        Returns:
            Dataset: Code sequence item with required and optional attributes
        """
        item = Dataset()
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme_designator
        item.CodeMeaning = code_meaning

        if coding_scheme_version is not None:
            item.CodingSchemeVersion = coding_scheme_version
        if context_identifier is not None:
            item.ContextIdentifier = context_identifier
        if context_uid is not None:
            item.ContextUID = context_uid
        if mapping_resource is not None:
            item.MappingResource = mapping_resource
        if context_group_version is not None:
            item.ContextGroupVersion = context_group_version
        if context_group_extension_flag is not None:
            item.ContextGroupExtensionFlag = context_group_extension_flag
        if context_group_local_version is not None:
            item.ContextGroupLocalVersion = context_group_local_version
        if context_group_extension_creator_uid is not None:
            item.ContextGroupExtensionCreatorUID = context_group_extension_creator_uid

        return item

    @staticmethod
    def create_alternate_container_item(
        container_identifier: str,
        issuer_of_container_identifier_sequence: list[dict[str, any]] | None = None
    ) -> Dataset:
        """Create alternate container identifier sequence item.

        Args:
            container_identifier (str): Identifier for the container (0040,0512) Type 1
            issuer_of_container_identifier_sequence (list[dict] | None): Organization
                that assigned the Container Identifier (0040,0513) Type 2

        Returns:
            Dataset: Alternate container identifier sequence item
        """
        item = Dataset()
        item.ContainerIdentifier = container_identifier
        item.IssuerOfContainerIdentifierSequence = issuer_of_container_identifier_sequence or []
        return item

    @staticmethod
    def create_container_component_item(
        container_component_type_code_sequence: list[dict[str, any]],
        manufacturer: str | None = None,
        manufacturers_model_name: str | None = None,
        container_component_id: str | None = None,
        container_component_length: float | None = None,
        container_component_width: float | None = None,
        container_component_diameter: float | None = None,
        container_component_thickness: float | None = None,
        container_component_material: str | ContainerComponentMaterial | None = None,
        container_component_description: str | None = None
    ) -> Dataset:
        """Create container component sequence item.

        Args:
            container_component_type_code_sequence (list[dict]): Type of container
                component (0050,0012) Type 1. Only single Item shall be included.
            manufacturer (str | None): Manufacturer of container component (0008,0070) Type 3
            manufacturers_model_name (str | None): Manufacturer's model name (0008,1090) Type 3
            container_component_id (str | None): Manufacturer's identifier (0050,001B) Type 3
            container_component_length (float | None): Length in mm (0050,001C) Type 3
            container_component_width (float | None): Width in mm (0050,0015) Type 3
            container_component_diameter (float | None): Diameter in mm (0050,001D) Type 3
            container_component_thickness (float | None): Thickness in mm (0050,0013) Type 3
            container_component_material (str | ContainerComponentMaterial | None): Material - GLASS/PLASTIC/METAL (0050,001A) Type 3
            container_component_description (str | None): Text description (0050,001E) Type 3

        Returns:
            Dataset: Container component sequence item
        """
        item = Dataset()
        item.ContainerComponentTypeCodeSequence = container_component_type_code_sequence

        if manufacturer is not None:
            item.Manufacturer = manufacturer
        if manufacturers_model_name is not None:
            item.ManufacturersModelName = manufacturers_model_name
        if container_component_id is not None:
            item.ContainerComponentID = container_component_id
        if container_component_length is not None:
            item.ContainerComponentLength = container_component_length
        if container_component_width is not None:
            item.ContainerComponentWidth = container_component_width
        if container_component_diameter is not None:
            item.ContainerComponentDiameter = container_component_diameter
        if container_component_thickness is not None:
            item.ContainerComponentThickness = container_component_thickness
        if container_component_material is not None:
            item.ContainerComponentMaterial = container_component_material.value if hasattr(container_component_material, 'value') else container_component_material
        if container_component_description is not None:
            item.ContainerComponentDescription = container_component_description

        return item

    @property
    def has_specimen_data(self) -> bool:
        """Check if specimen data is present.

        Returns:
            bool: True if required specimen elements are present
        """
        return (hasattr(self, 'ContainerIdentifier') and
                hasattr(self, 'SpecimenDescriptionSequence'))

    @property
    def specimen_count(self) -> int:
        """Get number of specimens described.

        Returns:
            int: Number of items in specimen description sequence
        """
        return len(getattr(self, 'SpecimenDescriptionSequence', []))

    @property
    def has_multiple_specimens(self) -> bool:
        """Check if multiple specimens are present.

        Returns:
            bool: True if more than one specimen is described
        """
        return self.specimen_count > 1

    @property
    def has_container_components(self) -> bool:
        """Check if container components are described.

        Returns:
            bool: True if container component sequence is present
        """
        return hasattr(self, 'ContainerComponentSequence')

    @property
    def has_alternate_identifiers(self) -> bool:
        """Check if alternate container identifiers are present.

        Returns:
            bool: True if alternate container identifier sequence is present
        """
        return hasattr(self, 'AlternateContainerIdentifierSequence')

    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Specimen Module data against DICOM standard.

        Args:
            config (ValidationConfig | None): Validation configuration options

        Returns:
            dict: Validation results with 'errors' and 'warnings' lists
        """
        return SpecimenValidator.validate(self, config)
