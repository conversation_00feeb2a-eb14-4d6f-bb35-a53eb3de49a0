"""
Common Instance Reference Module - DICOM PS3.3 C.12.2

The Common Instance Reference Module defines the Attributes that describe 
the hierarchical relationships of any SOP Instances referenced from other 
Modules within the Instance in which this Module occurs.
"""
from pydicom import Dataset
from .base_module import BaseModule
from ..validators.modules.common_instance_reference_validator import CommonInstanceReferenceValidator
from ..validators.modules.base_validator import ValidationConfig


class CommonInstanceReferenceModule(BaseModule):
    """Common Instance Reference Module implementation for DICOM PS3.3 C.12.2.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Describes the hierarchical relationships of any SOP Instances referenced 
    from other Modules within the Instance.
    
    Usage:
        # Create with no required elements (all are Type 1C conditional)
        reference = CommonInstanceReferenceModule.from_required_elements()
        
        # Add references to instances in this study
        reference.with_referenced_series(
            referenced_series_sequence=[
                reference.create_referenced_series_item(
                    series_instance_uid="*******.*******.9",
                    referenced_instance_sequence=[
                        reference.create_referenced_instance_item(
                            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
                            referenced_sop_instance_uid="*******.*******.10"
                        )
                    }]
                )
            }]
        )
        
        # Add references to instances in other studies
        reference.with_other_studies(
            studies_containing_other_referenced_instances_sequence=[
                reference.create_other_study_item(
                    study_instance_uid="*******.*******.11"
                )
            ]
        )
        
        # Validate
        result = reference.validate()
    """

    @classmethod
    def from_required_elements(cls) -> 'CommonInstanceReferenceModule':
        """Create CommonInstanceReferenceModule with no required elements (all are Type 1C).
        
        Returns:
            CommonInstanceReferenceModule: New module instance
        """
        return cls()
    
    def with_referenced_series(
        self,
        referenced_series_sequence: list[dict[str, any]]
    ) -> 'CommonInstanceReferenceModule':
        """Add references to instances in this study.
        
        Note: Referenced Series Sequence (0008,1115) is Type 1C - required if 
        this Instance references Instances in this Study.
        
        Args:
            referenced_series_sequence (list[dict]): Sequence of series containing referenced instances (0008,1115) Type 1C.
                Each item must include SeriesInstanceUID and ReferencedInstanceSequence.
                
        Returns:
            CommonInstanceReferenceModule: Self for method chaining
        """
        self.ReferencedSeriesSequence = referenced_series_sequence
        return self
    
    def with_other_studies(
        self,
        studies_containing_other_referenced_instances_sequence: list[dict[str, any]]
    ) -> 'CommonInstanceReferenceModule':
        """Add references to instances in other studies.
        
        Note: Studies Containing Other Referenced Instances Sequence (0008,1200) is Type 1C - 
        required if this Instance references Instances in other Studies.
        
        Args:
            studies_containing_other_referenced_instances_sequence (list[dict]): Studies containing other referenced instances (0008,1200) Type 1C.
                Each item must include StudyInstanceUID and Series and Instance Reference Macro attributes.
                
        Returns:
            CommonInstanceReferenceModule: Self for method chaining
        """
        self.StudiesContainingOtherReferencedInstancesSequence = studies_containing_other_referenced_instances_sequence
        return self
    
    def with_optional_elements(self, **kwargs) -> 'CommonInstanceReferenceModule':
        """Add optional (Type 3) data elements to the module instance.
        
        The Common Instance Reference Module has no Type 3 elements defined in DICOM PS3.3 C.12.2.
        This method is provided for API consistency but accepts no parameters.
        
        Args:
            **kwargs: No optional elements are supported
            
        Returns:
            CommonInstanceReferenceModule: Self for method chaining
            
        Raises:
            ValueError: If any keyword arguments are provided
        """
        if kwargs:
            raise ValueError(f"CommonInstanceReferenceModule has no optional elements. Unexpected arguments: {list(kwargs.keys())}")
        return self
    
    @staticmethod
    def create_referenced_series_item(
        series_instance_uid: str,
        referenced_instance_sequence: list[dict[str, any]]
    ) -> Dataset:
        """Create referenced series sequence item.
        
        Args:
            series_instance_uid (str): Unique identifier of the Series (0020,000E) Type 1
            referenced_instance_sequence (list[dict]): Referenced instances in this series (0008,114A) Type 1
                
        Returns:
            Dataset: Referenced series sequence item
        """
        item = Dataset()
        item.SeriesInstanceUID = series_instance_uid
        item.ReferencedInstanceSequence = referenced_instance_sequence
        return item
    
    @staticmethod
    def create_referenced_instance_item(
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str,
        referenced_frame_number: list[int] | None = None
    ) -> Dataset:
        """Create referenced instance sequence item.
        
        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID (0008,1150) Type 1
            referenced_sop_instance_uid (str): Referenced SOP Instance UID (0008,1155) Type 1
            referenced_frame_number (list[int] | None): Referenced frame numbers (0008,1160) Type 1C
                
        Returns:
            Dataset: Referenced instance sequence item
        """
        item = Dataset()
        item.ReferencedSOPClassUID = referenced_sop_class_uid
        item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        if referenced_frame_number is not None:
            item.ReferencedFrameNumber = referenced_frame_number
        return item
    
    @staticmethod
    def create_other_study_item(
        study_instance_uid: str,
        referenced_series_sequence: list[dict[str, any]] | None = None
    ) -> Dataset:
        """Create studies containing other referenced instances sequence item.
        
        Args:
            study_instance_uid (str): Unique identifier of the Study (0020,000D) Type 1
            referenced_series_sequence (list[dict] | None): Series and Instance Reference Macro attributes
                
        Returns:
            Dataset: Studies containing other referenced instances sequence item
        """
        item = Dataset()
        item.StudyInstanceUID = study_instance_uid
        if referenced_series_sequence is not None:
            item.ReferencedSeriesSequence = referenced_series_sequence
        return item
    
    @property
    def has_referenced_series(self) -> bool:
        """Check if referenced series are present.
        
        Returns:
            bool: True if referenced series sequence is present
        """
        return hasattr(self, 'ReferencedSeriesSequence')
    
    @property
    def has_other_studies(self) -> bool:
        """Check if other studies are referenced.
        
        Returns:
            bool: True if studies containing other referenced instances sequence is present
        """
        return hasattr(self, 'StudiesContainingOtherReferencedInstancesSequence')
    
    @property
    def referenced_series_count(self) -> int:
        """Get the number of referenced series.
        
        Returns:
            int: Number of referenced series items
        """
        return len(getattr(self, 'ReferencedSeriesSequence', []))
    
    @property
    def other_studies_count(self) -> int:
        """Get the number of other studies referenced.
        
        Returns:
            int: Number of other studies items
        """
        return len(getattr(self, 'StudiesContainingOtherReferencedInstancesSequence', []))
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate this Common Instance Reference Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        return CommonInstanceReferenceValidator.validate(self, config)
