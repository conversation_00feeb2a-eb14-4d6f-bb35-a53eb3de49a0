"""
RT General Plan Module - DICOM PS3.3 C.8.8.9

The RT General Plan Module contains general information about the RT Plan.
"""
from datetime import datetime, date
from .base_module import BaseModule
from ..enums.rt_enums import PlanIntent, RTplanGeometry, RTPlanRelationship
from ..validators.modules.rt_general_plan_validator import RTGeneralPlanValidator
from ..validators.modules.base_validator import ValidationConfig


class RTGeneralPlanModule(BaseModule):
    """RT General Plan Module implementation for DICOM PS3.3 C.8.8.9.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    Contains general information about the RT Plan.
    
    Usage:
        # Create with required elements
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Treatment Plan 1",
            rt_plan_date="20240101",
            rt_plan_time="120000",
            rt_plan_geometry=RTplanGeometry.PATIENT
        )
        
        # Add optional elements
        plan.with_optional_elements(
            rt_plan_name="Primary Treatment Plan",
            rt_plan_description="Curative treatment for lung cancer",
            instance_number="1",
            treatment_protocols="RTOG 0617",
            plan_intent=PlanIntent.CURATIVE,
            treatment_site="Lung"
        )
        
        # Add conditional structure set reference if geometry is PATIENT
        plan.with_structure_set_reference(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9"
        )
        
        # Validate
        result = plan.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        rt_plan_label: str,
        rt_plan_date: str | datetime | date = "",
        rt_plan_time: str | datetime = "",
        rt_plan_geometry: str | RTplanGeometry = ""
    ) -> 'RTGeneralPlanModule':
        """Create RTGeneralPlanModule from all required (Type 1 and Type 2) data elements.
        
        Args:
            rt_plan_label (str): User-defined label for treatment plan (300A,0002) Type 1
            rt_plan_date (str | datetime | date): Date treatment plan was last modified (300A,0006) Type 2
            rt_plan_time (str | datetime): Time treatment plan was last modified (300A,0007) Type 2
            rt_plan_geometry (str | RTplanGeometry): Whether RT Plan is based on patient or treatment device geometry (300A,000C) Type 1
                
        Returns:
            RTGeneralPlanModule: New module instance with required data elements set
        """
        instance = cls()
        instance.RTPlanLabel = rt_plan_label
        instance.RTPlanDate = instance._format_date_value(rt_plan_date)
        instance.RTPlanTime = instance._format_time_value(rt_plan_time)
        instance.RTPlanGeometry = instance._format_enum_value(rt_plan_geometry)
        return instance
    
    def with_optional_elements(
        self,
        rt_plan_name: str | None = None,
        rt_plan_description: str | None = None,
        instance_number: str | None = None,
        treatment_protocols: str | None = None,
        plan_intent: str | PlanIntent | None = None,
        treatment_site: str | None = None,
        treatment_site_code_sequence: list[dict[str, any]] | None = None,
        referenced_dose_sequence: list[dict[str, any]] | None = None,
        referenced_rt_plan_sequence: list[dict[str, any]] | None = None,
        frame_of_reference_to_displayed_coordinate_system_transformation_matrix: list[float] | None = None,
        rt_assertions_sequence: list[dict[str, any]] | None = None
    ) -> 'RTGeneralPlanModule':
        """Add optional (Type 3) elements.
        
        Args:
            rt_plan_name (str | None): User-defined name for treatment plan (300A,0003) Type 3
            rt_plan_description (str | None): User-defined description of treatment plan (300A,0004) Type 3
            instance_number (str | None): A number that identifies this object Instance (0020,0013) Type 3
            treatment_protocols (str | None): Planned treatment protocols (300A,0009) Type 3
            plan_intent (str | PlanIntent | None): Intent of this plan (300A,000A) Type 3
            treatment_site (str | None): Free-text label describing anatomical treatment site (3010,0077) Type 3
            treatment_site_code_sequence (list[dict[str, any]] | None): Coded description of treatment site (3010,0078) Type 3
            referenced_dose_sequence (list[dict[str, any]] | None): Related Instances of RT Dose (300C,0080) Type 3
            referenced_rt_plan_sequence (list[dict[str, any]] | None): Related Instances of RT Plan (300C,0002) Type 3
            frame_of_reference_to_displayed_coordinate_system_transformation_matrix (list[float] | None): 4x4 transformation matrix (0070,030B) Type 3
            rt_assertions_sequence (list[dict[str, any]] | None): Assertions made for this instance (0044,0110) Type 3
            
        Returns:
            RTGeneralPlanModule: Self for method chaining
        """
        self._set_attribute_if_not_none('RTPlanName', rt_plan_name)
        self._set_attribute_if_not_none('RTPlanDescription', rt_plan_description)
        self._set_attribute_if_not_none('InstanceNumber', instance_number)
        self._set_attribute_if_not_none('TreatmentProtocols', treatment_protocols)
        if plan_intent is not None:
            self.PlanIntent = self._format_enum_value(plan_intent)
        self._set_attribute_if_not_none('TreatmentSite', treatment_site)
        self._set_attribute_if_not_none('TreatmentSiteCodeSequence', treatment_site_code_sequence)
        self._set_attribute_if_not_none('ReferencedDoseSequence', referenced_dose_sequence)
        self._set_attribute_if_not_none('ReferencedRTPlanSequence', referenced_rt_plan_sequence)
        self._set_attribute_if_not_none('FrameOfReferenceToDisplayedCoordinateSystemTransformationMatrix', 
                                       frame_of_reference_to_displayed_coordinate_system_transformation_matrix)
        self._set_attribute_if_not_none('RTAssertionsSequence', rt_assertions_sequence)
        return self
    
    def with_structure_set_reference(
        self,
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str
    ) -> 'RTGeneralPlanModule':
        """Add referenced structure set (Type 1C - required if RT Plan Geometry is PATIENT).
        
        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID (0008,1150)
            referenced_sop_instance_uid (str): Referenced SOP Instance UID (0008,1155)
            
        Returns:
            RTGeneralPlanModule: Self for method chaining
        """
        # Validate conditional requirement
        geometry = getattr(self, 'RTPlanGeometry', '')
        self._validate_conditional_requirement(
            geometry == "PATIENT",
            [referenced_sop_class_uid, referenced_sop_instance_uid],
            "Referenced Structure Set Sequence (300C,0060) is required when RT Plan Geometry is PATIENT"
        )
        
        from pydicom import Dataset
        structure_set_item = Dataset()
        structure_set_item.ReferencedSOPClassUID = referenced_sop_class_uid
        structure_set_item.ReferencedSOPInstanceUID = referenced_sop_instance_uid
        self.ReferencedStructureSetSequence = [structure_set_item]
        return self
    
    @staticmethod
    def create_treatment_site_code_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str,
        coding_scheme_version: str | None = None,
        treatment_site_modifier_code_sequence: list[dict[str, any]] | None = None
    ) -> dict[str, any]:
        """Create treatment site code sequence item.
        
        Args:
            code_value (str): Code value
            coding_scheme_designator (str): Coding scheme designator
            code_meaning (str): Code meaning
            coding_scheme_version (str | None): Coding scheme version
            treatment_site_modifier_code_sequence (list[dict[str, any]] | None): Treatment site modifier codes
            
        Returns:
            dict: Treatment site code sequence item
        """
        item = {
            'CodeValue': code_value,
            'CodingSchemeDesignator': coding_scheme_designator,
            'CodeMeaning': code_meaning
        }
        if coding_scheme_version is not None:
            item['CodingSchemeVersion'] = coding_scheme_version
        if treatment_site_modifier_code_sequence is not None:
            item['TreatmentSiteModifierCodeSequence'] = treatment_site_modifier_code_sequence
        return item
    
    @staticmethod
    def create_referenced_rt_plan_item(
        referenced_sop_class_uid: str,
        referenced_sop_instance_uid: str,
        rt_plan_relationship: str | RTPlanRelationship
    ) -> dict[str, any]:
        """Create referenced RT plan sequence item.
        
        Args:
            referenced_sop_class_uid (str): Referenced SOP Class UID
            referenced_sop_instance_uid (str): Referenced SOP Instance UID
            rt_plan_relationship (str | RTPlanRelationship): Relationship of referenced plan
            
        Returns:
            dict: Referenced RT plan sequence item
        """
        return {
            'ReferencedSOPClassUID': referenced_sop_class_uid,
            'ReferencedSOPInstanceUID': referenced_sop_instance_uid,
            'RTPlanRelationship': rt_plan_relationship.value if hasattr(rt_plan_relationship, 'value') else str(rt_plan_relationship)
        }
    
    @property
    def is_patient_based(self) -> bool:
        """Check if plan is based on patient geometry.
        
        Returns:
            bool: True if RT Plan Geometry is PATIENT
        """
        return getattr(self, 'RTPlanGeometry', '') == "PATIENT"
    
    @property
    def has_structure_set_reference(self) -> bool:
        """Check if structure set reference is present.
        
        Returns:
            bool: True if Referenced Structure Set Sequence is present
        """
        return hasattr(self, 'ReferencedStructureSetSequence')
    
    @property
    def has_treatment_site_info(self) -> bool:
        """Check if treatment site information is present.
        
        Returns:
            bool: True if treatment site or treatment site code sequence is present
        """
        return (hasattr(self, 'TreatmentSite') or 
                hasattr(self, 'TreatmentSiteCodeSequence'))
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate this RT General Plan Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        return RTGeneralPlanValidator.validate(self, config)
