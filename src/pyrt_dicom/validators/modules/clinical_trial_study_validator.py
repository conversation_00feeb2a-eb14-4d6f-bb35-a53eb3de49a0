"""Clinical Trial Study Module DICOM validation - PS3.3 C.7.2.3"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig


class ClinicalTrialStudyValidator:
    """Validator for DICOM Clinical Trial Study Module (PS3.3 C.7.2.3)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Clinical Trial Study Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate conditional requirements
        if config.validate_conditional_requirements:
            result = ClinicalTrialStudyValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = ClinicalTrialStudyValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            result = ClinicalTrialStudyValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1C conditional requirements."""
        
        # Type 1C: Longitudinal Temporal Event Type required if Longitudinal Temporal Offset from Event is present
        has_offset = hasattr(dataset, 'LongitudinalTemporalOffsetFromEvent')
        if has_offset and not hasattr(dataset, 'LongitudinalTemporalEventType'):
            result["errors"].append(
                "Longitudinal Temporal Event Type (0012,0053) is required when "
                "Longitudinal Temporal Offset from Event (0012,0052) is present"
            )
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values against DICOM specifications."""
        
        # Longitudinal Temporal Event Type (0012,0053)
        event_type = getattr(dataset, 'LongitudinalTemporalEventType', '')
        if event_type:
            BaseValidator.validate_enumerated_value(
                event_type, ["ENROLLMENT", "BASELINE"],
                "Longitudinal Temporal Event Type (0012,0053)", result
            )
        
        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate sequence structure requirements."""
        
        # Consent for Clinical Trial Use Sequence validation
        consent_seq = getattr(dataset, 'ConsentForClinicalTrialUseSequence', [])
        for i, item in enumerate(consent_seq):
            # Type 1: Consent for Distribution Flag is required
            if not item.get('ConsentForDistributionFlag'):
                result["errors"].append(
                    f"Consent for Clinical Trial Use Sequence item {i}: "
                    "Consent for Distribution Flag (0012,0085) is required"
                )
            else:
                # Validate enumerated values for Consent for Distribution Flag
                flag_value = item.get('ConsentForDistributionFlag')
                if flag_value not in ["NO", "YES", "WITHDRAWN"]:
                    result["warnings"].append(
                        f"Consent for Clinical Trial Use Sequence item {i}: "
                        f"Consent for Distribution Flag (0012,0085) value '{flag_value}' "
                        "should be one of: NO, YES, WITHDRAWN"
                    )
                
                # Type 1C: Distribution Type required if flag is YES or WITHDRAWN
                if flag_value in ["YES", "WITHDRAWN"]:
                    if not item.get('DistributionType'):
                        result["errors"].append(
                            f"Consent for Clinical Trial Use Sequence item {i}: "
                            "Distribution Type (0012,0084) is required when "
                            "Consent for Distribution Flag is YES or WITHDRAWN"
                        )
                    else:
                        # Validate Distribution Type enumerated values
                        dist_type = item.get('DistributionType')
                        if dist_type not in ["NAMED_PROTOCOL", "RESTRICTED_REUSE", "PUBLIC_RELEASE"]:
                            result["warnings"].append(
                                f"Consent for Clinical Trial Use Sequence item {i}: "
                                f"Distribution Type (0012,0084) value '{dist_type}' "
                                "should be one of: NAMED_PROTOCOL, RESTRICTED_REUSE, PUBLIC_RELEASE"
                            )
                        
                        # Type 1C: Clinical Trial Protocol ID required if Distribution Type is NAMED_PROTOCOL
                        # and protocol is not the same as in Clinical Trial Subject Module
                        if dist_type == "NAMED_PROTOCOL":
                            if not item.get('ClinicalTrialProtocolID'):
                                result["errors"].append(
                                    f"Consent for Clinical Trial Use Sequence item {i}: "
                                    "Clinical Trial Protocol ID (0012,0020) is required when "
                                    "Distribution Type is NAMED_PROTOCOL"
                                )
        
        return result
