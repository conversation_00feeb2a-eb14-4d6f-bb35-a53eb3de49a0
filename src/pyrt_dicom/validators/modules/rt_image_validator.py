"""RT Image Module DICOM validation - PS3.3 C.8.8.2"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ...enums.rt_enums import (
    ConversionType, ReportedValuesOrigin, RTImagePlane, PrimaryDosimeterUnit,
    PixelIntensityRelationshipSign, RTImageTypeValue3, RTBeamLimitingDeviceType,
    ApplicatorType, GeneralAccessoryType, BlockType, BlockDivergence, 
    BlockMountingPosition, FluenceDataSource, FluenceMode,
    EnhancedRTBeamLimitingDeviceDefinitionFlag
)
from ...enums.image_enums import PhotometricInterpretation, PixelRepresentation
from ...enums.series_enums import PatientPosition


class RTImageValidator:
    """Validator for DICOM RT Image Module (PS3.3 C.8.8.2)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate RT Image Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            result = RTImageValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = RTImageValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            result = RTImageValidator._validate_sequence_requirements(dataset, result)
        
        # Validate pixel data consistency
        result = RTImageValidator._validate_pixel_data_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1C and 2C conditional requirements."""
        
        # Type 1C: Pixel Intensity Relationship Sign required if Pixel Intensity Relationship is present
        has_pixel_intensity_relationship = hasattr(dataset, 'PixelIntensityRelationship')
        if has_pixel_intensity_relationship and not hasattr(dataset, 'PixelIntensityRelationshipSign'):
            result["errors"].append(
                "Pixel Intensity Relationship Sign (0028,1041) is required when "
                "Pixel Intensity Relationship (0028,1040) is present"
            )
        
        # Type 2C: Reported Values Origin required if Image Type Value 3 is SIMULATOR or PORTAL
        image_type = getattr(dataset, 'ImageType', [])
        image_type_value3 = image_type[2] if len(image_type) > 2 else ''
        
        if image_type_value3 in ['SIMULATOR', 'PORTAL']:
            if not hasattr(dataset, 'ReportedValuesOrigin'):
                result["errors"].append(
                    f"Reported Values Origin (3002,000A) is required when "
                    f"Image Type Value 3 is {image_type_value3}"
                )
        
        # Type 2C: RT Image Orientation required if RT Image Plane is NON_NORMAL
        rt_image_plane = getattr(dataset, 'RTImagePlane', '')
        if rt_image_plane == "NON_NORMAL" and not hasattr(dataset, 'RTImageOrientation'):
            result["errors"].append(
                "RT Image Orientation (3002,0010) is required when "
                "RT Image Plane (3002,000C) is NON_NORMAL"
            )
        
        # Type 1C: Patient Position required if Isocenter Position is present
        has_isocenter_position = hasattr(dataset, 'IsocenterPosition')
        if has_isocenter_position and not hasattr(dataset, 'PatientPosition'):
            result["errors"].append(
                "Patient Position (0018,5100) is required when "
                "Isocenter Position (300A,012C) is present"
            )
        
        # Type 1C: Fluence Map Sequence required if Image Type Value 3 is FLUENCE
        if image_type_value3 == 'FLUENCE' and not hasattr(dataset, 'FluenceMapSequence'):
            result["errors"].append(
                "Fluence Map Sequence (3002,0040) is required when "
                "Image Type Value 3 is FLUENCE"
            )
        
        # Validate exposure sequence conditional requirements
        exposure_seq = getattr(dataset, 'ExposureSequence', [])
        for i, exposure_item in enumerate(exposure_seq):
            # Type 2C: KVP required if Image Type Value 3 is PORTAL, SIMULATOR or RADIOGRAPH
            if image_type_value3 in ['PORTAL', 'SIMULATOR', 'RADIOGRAPH']:
                if not exposure_item.get('KVP'):
                    result["errors"].append(
                        f"Exposure Sequence item {i}: KVP (0018,0060) is required when "
                        f"Image Type Value 3 is {image_type_value3}"
                    )
            
            # Type 2C: X-Ray Tube Current required if Image Type Value 3 is SIMULATOR or RADIOGRAPH
            if image_type_value3 in ['SIMULATOR', 'RADIOGRAPH']:
                if not exposure_item.get('XRayTubeCurrent'):
                    result["errors"].append(
                        f"Exposure Sequence item {i}: X-Ray Tube Current (0018,1151) is required when "
                        f"Image Type Value 3 is {image_type_value3}"
                    )
            
            # Type 2C: Exposure Time required if Image Type Value 3 is SIMULATOR or RADIOGRAPH
            if image_type_value3 in ['SIMULATOR', 'RADIOGRAPH']:
                if not exposure_item.get('ExposureTime'):
                    result["errors"].append(
                        f"Exposure Sequence item {i}: Exposure Time (0018,1150) is required when "
                        f"Image Type Value 3 is {image_type_value3}"
                    )
            
            # Type 2C: Meterset Exposure required if Image Type Value 3 is PORTAL
            if image_type_value3 == 'PORTAL':
                if not exposure_item.get('MetersetExposure'):
                    result["errors"].append(
                        f"Exposure Sequence item {i}: Meterset Exposure (3002,0032) is required when "
                        f"Image Type Value 3 is PORTAL"
                    )
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values against DICOM specifications."""
        
        # Photometric Interpretation (0028,0004) - should be MONOCHROME2 for RT Image
        photometric_interp = getattr(dataset, 'PhotometricInterpretation', '')
        if photometric_interp and photometric_interp != 'MONOCHROME2':
            result["warnings"].append(
                "Photometric Interpretation (0028,0004) should be MONOCHROME2 for RT Image"
            )
        
        # Samples per Pixel (0028,0002) - should be 1 for RT Image
        samples_per_pixel = getattr(dataset, 'SamplesPerPixel', None)
        if samples_per_pixel is not None and samples_per_pixel != 1:
            result["warnings"].append(
                "Samples per Pixel (0028,0002) should be 1 for RT Image"
            )
        
        # Bits Allocated (0028,0100) - should be 8 or 16 for RT Image
        bits_allocated = getattr(dataset, 'BitsAllocated', None)
        if bits_allocated is not None and bits_allocated not in [8, 16]:
            result["warnings"].append(
                "Bits Allocated (0028,0100) should be 8 or 16 for RT Image"
            )
        
        # Conversion Type (0008,0064)
        conversion_type = getattr(dataset, 'ConversionType', '')
        if conversion_type:
            valid_types = [ctype.value for ctype in ConversionType]
            BaseValidator.validate_enumerated_value(
                conversion_type, valid_types,
                "Conversion Type (0008,0064)", result
            )
        
        # Reported Values Origin (3002,000A)
        reported_values_origin = getattr(dataset, 'ReportedValuesOrigin', '')
        if reported_values_origin:
            valid_origins = [origin.value for origin in ReportedValuesOrigin]
            BaseValidator.validate_enumerated_value(
                reported_values_origin, valid_origins,
                "Reported Values Origin (3002,000A)", result
            )
        
        # RT Image Plane (3002,000C)
        rt_image_plane = getattr(dataset, 'RTImagePlane', '')
        if rt_image_plane:
            valid_planes = [plane.value for plane in RTImagePlane]
            BaseValidator.validate_enumerated_value(
                rt_image_plane, valid_planes,
                "RT Image Plane (3002,000C)", result
            )
        
        # Primary Dosimeter Unit (300A,00B3)
        primary_dosimeter_unit = getattr(dataset, 'PrimaryDosimeterUnit', '')
        if primary_dosimeter_unit:
            valid_units = [unit.value for unit in PrimaryDosimeterUnit]
            BaseValidator.validate_enumerated_value(
                primary_dosimeter_unit, valid_units,
                "Primary Dosimeter Unit (300A,00B3)", result
            )
        
        # Enhanced RT Beam Limiting Device Definition Flag (3008,00A3)
        enhanced_flag = getattr(dataset, 'EnhancedRTBeamLimitingDeviceDefinitionFlag', '')
        if enhanced_flag:
            valid_flags = [flag.value for flag in EnhancedRTBeamLimitingDeviceDefinitionFlag]
            BaseValidator.validate_enumerated_value(
                enhanced_flag, valid_flags,
                "Enhanced RT Beam Limiting Device Definition Flag (3008,00A3)", result
            )
        
        # Validate Image Type Value 3
        image_type = getattr(dataset, 'ImageType', [])
        if len(image_type) > 2:
            image_type_value3 = image_type[2]
            valid_image_types = [itype.value for itype in RTImageTypeValue3]
            BaseValidator.validate_enumerated_value(
                image_type_value3, valid_image_types,
                "Image Type Value 3 (0008,0008)", result
            )
        
        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate sequence structure requirements."""
        
        # Referenced RT Plan Sequence validation
        ref_rt_plan_seq = getattr(dataset, 'ReferencedRTPlanSequence', [])
        for i, plan_item in enumerate(ref_rt_plan_seq):
            if not plan_item.get('ReferencedSOPClassUID'):
                result["errors"].append(
                    f"Referenced RT Plan Sequence item {i}: "
                    "Referenced SOP Class UID (0008,1150) is required"
                )
            if not plan_item.get('ReferencedSOPInstanceUID'):
                result["errors"].append(
                    f"Referenced RT Plan Sequence item {i}: "
                    "Referenced SOP Instance UID (0008,1155) is required"
                )
        
        # Exposure Sequence validation
        exposure_seq = getattr(dataset, 'ExposureSequence', [])
        for i, exposure_item in enumerate(exposure_seq):
            # Validate beam limiting device sequence if present
            beam_limiting_seq = exposure_item.get('BeamLimitingDeviceSequence', [])
            for j, device_item in enumerate(beam_limiting_seq):
                if not device_item.get('RTBeamLimitingDeviceType'):
                    result["errors"].append(
                        f"Beam Limiting Device Sequence item {j} in Exposure {i}: "
                        "RT Beam Limiting Device Type (300A,00B8) is required"
                    )
                if not device_item.get('NumberOfLeafJawPairs'):
                    result["errors"].append(
                        f"Beam Limiting Device Sequence item {j} in Exposure {i}: "
                        "Number of Leaf/Jaw Pairs (300A,00BC) is required"
                    )
            
            # Validate block sequence if present
            block_seq = exposure_item.get('BlockSequence', [])
            number_of_blocks = exposure_item.get('NumberOfBlocks', 0)
            
            if number_of_blocks > 0 and not block_seq:
                result["errors"].append(
                    f"Exposure Sequence item {i}: "
                    "Block Sequence (300A,00F4) is required when Number of Blocks is non-zero"
                )
            
            for j, block_item in enumerate(block_seq):
                required_block_fields = [
                    ('BlockNumber', '300A,00FC'),
                    ('BlockType', '300A,00F8'),
                    ('BlockDivergence', '300A,00FA'),
                    ('SourceToBlockTrayDistance', '300A,00F6'),
                    ('MaterialID', '300A,00E1'),
                    ('BlockNumberOfPoints', '300A,0104'),
                    ('BlockData', '300A,0106')
                ]
                
                for field_name, tag in required_block_fields:
                    if not block_item.get(field_name):
                        result["errors"].append(
                            f"Block Sequence item {j} in Exposure {i}: "
                            f"{field_name} ({tag}) is required"
                        )
        
        # Fluence Map Sequence validation
        fluence_seq = getattr(dataset, 'FluenceMapSequence', [])
        for i, fluence_item in enumerate(fluence_seq):
            if not fluence_item.get('FluenceDataSource'):
                result["errors"].append(
                    f"Fluence Map Sequence item {i}: "
                    "Fluence Data Source (3002,0041) is required"
                )
        
        return result
    
    @staticmethod
    def _validate_pixel_data_consistency(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate pixel data consistency and logical constraints."""
        
        # Validate bits stored/high bit consistency for RT Images
        bits_stored = getattr(dataset, 'BitsStored', None)
        high_bit = getattr(dataset, 'HighBit', None)
        
        if bits_stored is not None and high_bit is not None:
            if high_bit != bits_stored - 1:
                result["warnings"].append(
                    "For RT Images, High Bit (0028,0102) should be one less than Bits Stored (0028,0101)"
                )
        
        # Validate pixel representation for RT Images
        bits_allocated = getattr(dataset, 'BitsAllocated', None)
        pixel_representation = getattr(dataset, 'PixelRepresentation', '')
        
        if bits_allocated == 8 and pixel_representation != '0000H':
            result["warnings"].append(
                "Pixel Representation should be 0000H (unsigned) when Bits Allocated is 8"
            )
        
        return result
