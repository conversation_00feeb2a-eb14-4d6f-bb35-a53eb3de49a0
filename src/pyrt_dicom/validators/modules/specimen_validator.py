"""Specimen Module DICOM validation - PS3.3 C.7.6.22"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig


class SpecimenValidator:
    """Validator for DICOM Specimen Module (PS3.3 C.7.6.22)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Specimen Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate Type 1 requirements
        result = SpecimenValidator._validate_required_elements(dataset, result)
        
        # Validate conditional requirements
        if config.validate_conditional_requirements:
            result = SpecimenValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = SpecimenValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            result = SpecimenValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1 required elements."""
        
        # Container Identifier (0040,0512) - Type 1
        if not hasattr(dataset, 'ContainerIdentifier') or not dataset.ContainerIdentifier:
            result["errors"].append("Container Identifier (0040,0512) is required (Type 1)")
        
        # Specimen Description Sequence (0040,0560) - Type 1
        if not hasattr(dataset, 'SpecimenDescriptionSequence') or not dataset.SpecimenDescriptionSequence:
            result["errors"].append("Specimen Description Sequence (0040,0560) is required (Type 1)")
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1C and 2C conditional requirements."""
        
        # Specimen Localization Content Item Sequence (0040,0620) - Type 1C
        # Required if multiple specimens present in the image
        specimen_seq = getattr(dataset, 'SpecimenDescriptionSequence', [])
        if len(specimen_seq) > 1:
            for i, specimen_item in enumerate(specimen_seq):
                if not specimen_item.get('SpecimenLocalizationContentItemSequence'):
                    result["errors"].append(
                        f"Specimen Description Sequence item {i}: "
                        "Specimen Localization Content Item Sequence (0040,0620) is required "
                        "when multiple specimens are present (Type 1C)"
                    )
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values against DICOM specifications."""
        
        # Container Component Material enumerated values
        component_seq = getattr(dataset, 'ContainerComponentSequence', [])
        for i, component in enumerate(component_seq):
            material = component.get('ContainerComponentMaterial', '')
            if material:
                BaseValidator.validate_enumerated_value(
                    material, 
                    ["GLASS", "PLASTIC", "METAL"],
                    f"Container Component Sequence item {i}: Container Component Material (0050,001A)", 
                    result
                )
        
        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate sequence structure requirements."""
        
        # Validate Specimen Description Sequence structure
        specimen_seq = getattr(dataset, 'SpecimenDescriptionSequence', [])
        for i, specimen_item in enumerate(specimen_seq):
            # Type 1 elements within specimen description
            if not specimen_item.get('SpecimenIdentifier'):
                result["errors"].append(
                    f"Specimen Description Sequence item {i}: "
                    "Specimen Identifier (0040,0551) is required (Type 1)"
                )
            if not specimen_item.get('SpecimenUID'):
                result["errors"].append(
                    f"Specimen Description Sequence item {i}: "
                    "Specimen UID (0040,0554) is required (Type 1)"
                )
        
        # Validate Issuer of Container Identifier Sequence cardinality
        issuer_seq = getattr(dataset, 'IssuerOfContainerIdentifierSequence', [])
        if len(issuer_seq) > 1:
            result["errors"].append(
                "Issuer of Container Identifier Sequence (0040,0513): "
                "Zero or one Item shall be included in this Sequence"
            )
        
        # Validate Container Type Code Sequence cardinality
        container_type_seq = getattr(dataset, 'ContainerTypeCodeSequence', [])
        if len(container_type_seq) > 1:
            result["errors"].append(
                "Container Type Code Sequence (0040,0518): "
                "Zero or one Item shall be included in this Sequence"
            )
        
        # Validate Alternate Container Identifier Sequence structure
        alt_container_seq = getattr(dataset, 'AlternateContainerIdentifierSequence', [])
        for i, alt_item in enumerate(alt_container_seq):
            if not alt_item.get('ContainerIdentifier'):
                result["errors"].append(
                    f"Alternate Container Identifier Sequence item {i}: "
                    "Container Identifier (0040,0512) is required (Type 1)"
                )
            
            # Validate issuer sequence cardinality within alternate container items
            alt_issuer_seq = alt_item.get('IssuerOfContainerIdentifierSequence', [])
            if len(alt_issuer_seq) > 1:
                result["errors"].append(
                    f"Alternate Container Identifier Sequence item {i}: "
                    "Issuer of Container Identifier Sequence (0040,0513): "
                    "Zero or one Item shall be included in this Sequence"
                )
        
        # Validate Container Component Sequence structure
        component_seq = getattr(dataset, 'ContainerComponentSequence', [])
        for i, component in enumerate(component_seq):
            if not component.get('ContainerComponentTypeCodeSequence'):
                result["errors"].append(
                    f"Container Component Sequence item {i}: "
                    "Container Component Type Code Sequence (0050,0012) is required (Type 1)"
                )
            else:
                # Validate that only single item is included
                type_code_seq = component.get('ContainerComponentTypeCodeSequence', [])
                if len(type_code_seq) != 1:
                    result["errors"].append(
                        f"Container Component Sequence item {i}: "
                        "Container Component Type Code Sequence (0050,0012): "
                        "Only a single Item shall be included in this Sequence"
                    )
        
        # Validate Code Sequence structure for all code sequences
        SpecimenValidator._validate_code_sequences(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_code_sequences(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate code sequence structure requirements."""
        
        # Validate Container Type Code Sequence
        container_type_seq = getattr(dataset, 'ContainerTypeCodeSequence', [])
        for i, code_item in enumerate(container_type_seq):
            SpecimenValidator._validate_code_item(
                code_item, f"Container Type Code Sequence item {i}", result
            )
        
        # Validate Specimen Description Sequence code sequences
        specimen_seq = getattr(dataset, 'SpecimenDescriptionSequence', [])
        for i, specimen_item in enumerate(specimen_seq):
            # Validate Specimen Type Code Sequence
            specimen_type_seq = specimen_item.get('SpecimenTypeCodeSequence', [])
            for j, code_item in enumerate(specimen_type_seq):
                SpecimenValidator._validate_code_item(
                    code_item, f"Specimen Description Sequence item {i}, Specimen Type Code Sequence item {j}", result
                )
        
        # Validate Container Component Type Code Sequences
        component_seq = getattr(dataset, 'ContainerComponentSequence', [])
        for i, component in enumerate(component_seq):
            type_code_seq = component.get('ContainerComponentTypeCodeSequence', [])
            for j, code_item in enumerate(type_code_seq):
                SpecimenValidator._validate_code_item(
                    code_item, f"Container Component Sequence item {i}, Container Component Type Code Sequence item {j}", result
                )
        
        return result
    
    @staticmethod
    def _validate_code_item(code_item: dict, context: str, result: dict[str, list[str]]) -> None:
        """Validate individual code sequence item structure."""
        
        if not code_item.get('CodeValue'):
            result["errors"].append(f"{context}: Code Value (0008,0100) is required")
        if not code_item.get('CodingSchemeDesignator'):
            result["errors"].append(f"{context}: Coding Scheme Designator (0008,0102) is required")
        if not code_item.get('CodeMeaning'):
            result["errors"].append(f"{context}: Code Meaning (0008,0104) is required")
