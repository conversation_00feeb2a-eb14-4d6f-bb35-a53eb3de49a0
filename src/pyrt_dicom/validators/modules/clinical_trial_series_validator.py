"""Clinical Trial Series Module DICOM validation - PS3.3 C.7.3.2"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig


class ClinicalTrialSeriesValidator:
    """Validator for DICOM Clinical Trial Series Module (PS3.3 C.7.3.2)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Clinical Trial Series Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Check Type 2 requirements
        if not hasattr(dataset, 'ClinicalTrialCoordinatingCenterName'):
            result["warnings"].append(
                "Clinical Trial Coordinating Center Name (0012,0060) is recommended (Type 2)"
            )
        
        # All other attributes in this module are Type 3 (optional)
        # No specific validation rules beyond basic presence checks
        
        return result
