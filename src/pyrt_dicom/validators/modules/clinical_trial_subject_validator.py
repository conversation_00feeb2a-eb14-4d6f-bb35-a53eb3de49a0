"""Clinical Trial Subject Module DICOM validation - PS3.3 C.7.1.3"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig


class ClinicalTrialSubjectValidator:
    """Validator for DICOM Clinical Trial Subject Module (PS3.3 C.7.1.3)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Clinical Trial Subject Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate conditional requirements
        if config.validate_conditional_requirements:
            result = ClinicalTrialSubjectValidator._validate_conditional_requirements(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            result = ClinicalTrialSubjectValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1C conditional requirements."""
        
        # Type 1C: Clinical Trial Subject ID or Clinical Trial Subject Reading ID required
        has_subject_id = hasattr(dataset, 'ClinicalTrialSubjectID') and getattr(dataset, 'ClinicalTrialSubjectID', '')
        has_reading_id = hasattr(dataset, 'ClinicalTrialSubjectReadingID') and getattr(dataset, 'ClinicalTrialSubjectReadingID', '')
        
        if not has_subject_id and not has_reading_id:
            result["errors"].append(
                "Either Clinical Trial Subject ID (0012,0040) or "
                "Clinical Trial Subject Reading ID (0012,0042) is required"
            )
        
        # Type 1C: Ethics Committee Name required if Ethics Committee Approval Number is present
        has_approval_number = hasattr(dataset, 'ClinicalTrialProtocolEthicsCommitteeApprovalNumber')
        if has_approval_number and not hasattr(dataset, 'ClinicalTrialProtocolEthicsCommitteeName'):
            result["errors"].append(
                "Clinical Trial Protocol Ethics Committee Name (0012,0081) is required when "
                "Clinical Trial Protocol Ethics Committee Approval Number (0012,0082) is present"
            )
        
        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate sequence structure requirements."""
        
        # Other Clinical Trial Protocol IDs Sequence validation
        other_protocol_ids_seq = getattr(dataset, 'OtherClinicalTrialProtocolIDsSequence', [])
        for i, item in enumerate(other_protocol_ids_seq):
            if not item.get('ClinicalTrialProtocolID'):
                result["errors"].append(
                    f"Other Clinical Trial Protocol IDs Sequence item {i}: "
                    "Clinical Trial Protocol ID (0012,0020) is required"
                )
            if not item.get('IssuerOfClinicalTrialProtocolID'):
                result["errors"].append(
                    f"Other Clinical Trial Protocol IDs Sequence item {i}: "
                    "Issuer of Clinical Trial Protocol ID (0012,0022) is required"
                )
        
        return result
