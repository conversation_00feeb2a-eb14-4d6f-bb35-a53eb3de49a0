"""Multi-energy CT Image Module Validator - DICOM PS3.3 C.8.2.2"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ...enums.contrast_ct_enums import MultiEnergySourceTechnique, MultiEnergyDetectorType


class MultiEnergyCTImageValidator(BaseValidator):
    """Validator for Multi-energy CT Image Module (C.8.2.2)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Multi-energy CT Image Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate Type 1 required elements
        result = MultiEnergyCTImageValidator._validate_required_elements(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            result = MultiEnergyCTImageValidator._validate_sequence_structures(dataset, result)
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            result = MultiEnergyCTImageValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = MultiEnergyCTImageValidator._validate_enumerated_values(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate presence of Type 1 required elements."""
        
        # Multi-energy CT Acquisition Sequence (0018,9362) Type 1
        if not hasattr(dataset, 'MultienergyCTAcquisitionSequence'):
            result["errors"].append(
                "Multi-energy CT Acquisition Sequence (0018,9362) is required (Type 1)"
            )
        else:
            acq_seq = getattr(dataset, 'MultienergyCTAcquisitionSequence', [])
            if not acq_seq:
                result["errors"].append(
                    "Multi-energy CT Acquisition Sequence (0018,9362) must contain at least one Item"
                )
        
        return result
    
    @staticmethod
    def _validate_sequence_structures(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate nested sequence structures and their required elements."""
        
        acq_seq = getattr(dataset, 'MultienergyCTAcquisitionSequence', [])
        
        for i, acq_item in enumerate(acq_seq):
            # Validate X-Ray Source Sequence (Type 1)
            source_seq = acq_item.get('MultienergyCTXRaySourceSequence', [])
            if not source_seq:
                result["errors"].append(
                    f"Multi-energy CT X-Ray Source Sequence (0018,9365) is required "
                    f"in acquisition item {i+1} (Type 1)"
                )
            else:
                result = MultiEnergyCTImageValidator._validate_x_ray_source_sequence(
                    source_seq, i+1, result
                )
            
            # Validate X-Ray Detector Sequence (Type 1)
            detector_seq = acq_item.get('MultienergyCTXRayDetectorSequence', [])
            if not detector_seq:
                result["errors"].append(
                    f"Multi-energy CT X-Ray Detector Sequence (0018,936F) is required "
                    f"in acquisition item {i+1} (Type 1)"
                )
            else:
                result = MultiEnergyCTImageValidator._validate_x_ray_detector_sequence(
                    detector_seq, i+1, result
                )
            
            # Validate Path Sequence (Type 1)
            path_seq = acq_item.get('MultienergyCTPathSequence', [])
            if not path_seq:
                result["errors"].append(
                    f"Multi-energy CT Path Sequence (0018,9379) is required "
                    f"in acquisition item {i+1} (Type 1)"
                )
            elif len(path_seq) < 2:
                result["errors"].append(
                    f"Multi-energy CT Path Sequence (0018,9379) must contain "
                    f"two or more Items in acquisition item {i+1}"
                )
            else:
                result = MultiEnergyCTImageValidator._validate_path_sequence(
                    path_seq, source_seq, detector_seq, i+1, result
                )
        
        return result
    
    @staticmethod
    def _validate_x_ray_source_sequence(source_seq: list, acq_index: int, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate X-Ray Source Sequence items."""
        
        for j, source_item in enumerate(source_seq):
            item_desc = f"X-Ray source item {j+1} in acquisition {acq_index}"
            
            # Type 1 required elements
            required_fields = [
                ('XRaySourceIndex', 'X-Ray Source Index (0018,9366)'),
                ('XRaySourceID', 'X-Ray Source ID (0018,9367)'),
                ('MultienergySourceTechnique', 'Multi-energy Source Technique (0018,9368)'),
                ('SourceStartDateTime', 'Source Start DateTime (0018,9369)'),
                ('SourceEndDateTime', 'Source End DateTime (0018,936A)')
            ]
            
            for field_name, display_name in required_fields:
                if field_name not in source_item:
                    result["errors"].append(f"{display_name} is required in {item_desc} (Type 1)")
            
            # Type 1C: Switching Phase Number required if technique is SWITCHING_SOURCE
            technique = source_item.get('MultienergySourceTechnique', '')
            if technique == "SWITCHING_SOURCE":
                if 'SwitchingPhaseNumber' not in source_item:
                    result["errors"].append(
                        f"Switching Phase Number (0018,936B) is required in {item_desc} "
                        f"when Multi-energy Source Technique is SWITCHING_SOURCE (Type 1C)"
                    )
        
        return result
    
    @staticmethod
    def _validate_x_ray_detector_sequence(detector_seq: list, acq_index: int, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate X-Ray Detector Sequence items."""
        
        for j, detector_item in enumerate(detector_seq):
            item_desc = f"X-Ray detector item {j+1} in acquisition {acq_index}"
            
            # Type 1 required elements
            required_fields = [
                ('XRayDetectorIndex', 'X-Ray Detector Index (0018,9370)'),
                ('XRayDetectorID', 'X-Ray Detector ID (0018,9371)'),
                ('MultienergyDetectorType', 'Multi-energy Detector Type (0018,9372)')
            ]
            
            for field_name, display_name in required_fields:
                if field_name not in detector_item:
                    result["errors"].append(f"{display_name} is required in {item_desc} (Type 1)")
            
            # Type 1C: Energy ranges required if detector type is PHOTON_COUNTING
            detector_type = detector_item.get('MultienergyDetectorType', '')
            if detector_type == "PHOTON_COUNTING":
                if 'NominalMaxEnergy' not in detector_item:
                    result["errors"].append(
                        f"Nominal Max Energy (0018,9374) is required in {item_desc} "
                        f"when Multi-energy Detector Type is PHOTON_COUNTING (Type 1C)"
                    )
                if 'NominalMinEnergy' not in detector_item:
                    result["errors"].append(
                        f"Nominal Min Energy (0018,9375) is required in {item_desc} "
                        f"when Multi-energy Detector Type is PHOTON_COUNTING (Type 1C)"
                    )
        
        return result
    
    @staticmethod
    def _validate_path_sequence(path_seq: list, source_seq: list, detector_seq: list, 
                               acq_index: int, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Path Sequence items and their references."""
        
        # Collect valid source and detector indices
        valid_source_indices = {item.get('XRaySourceIndex') for item in source_seq if 'XRaySourceIndex' in item}
        valid_detector_indices = {item.get('XRayDetectorIndex') for item in detector_seq if 'XRayDetectorIndex' in item}
        
        for j, path_item in enumerate(path_seq):
            item_desc = f"Path item {j+1} in acquisition {acq_index}"
            
            # Type 1 required elements
            required_fields = [
                ('MultienergyCTPathIndex', 'Multi-energy CT Path Index (0018,937A)'),
                ('ReferencedXRaySourceIndex', 'Referenced X-Ray Source Index (0018,9377)'),
                ('ReferencedXRayDetectorIndex', 'Referenced X-Ray Detector Index (0018,9376)')
            ]
            
            for field_name, display_name in required_fields:
                if field_name not in path_item:
                    result["errors"].append(f"{display_name} is required in {item_desc} (Type 1)")
            
            # Validate references
            ref_source_index = path_item.get('ReferencedXRaySourceIndex')
            if ref_source_index is not None and ref_source_index not in valid_source_indices:
                result["errors"].append(
                    f"Referenced X-Ray Source Index {ref_source_index} in {item_desc} "
                    f"does not match any X-Ray Source Index in the source sequence"
                )
            
            ref_detector_index = path_item.get('ReferencedXRayDetectorIndex')
            if ref_detector_index is not None and ref_detector_index not in valid_detector_indices:
                result["errors"].append(
                    f"Referenced X-Ray Detector Index {ref_detector_index} in {item_desc} "
                    f"does not match any X-Ray Detector Index in the detector sequence"
                )
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1C conditional requirements."""
        
        # Already validated in sequence structure validation
        # Additional conditional logic can be added here if needed
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values against DICOM standard."""
        
        acq_seq = getattr(dataset, 'MultienergyCTAcquisitionSequence', [])
        
        for i, acq_item in enumerate(acq_seq):
            # Validate source technique enumerated values
            source_seq = acq_item.get('MultienergyCTXRaySourceSequence', [])
            for j, source_item in enumerate(source_seq):
                technique = source_item.get('MultienergySourceTechnique', '')
                if technique:
                    valid_techniques = [technique.value for technique in MultiEnergySourceTechnique]
                    BaseValidator.validate_enumerated_value(
                        technique, valid_techniques,
                        f"Multi-energy Source Technique (0018,9368) in source {j+1}, acquisition {i+1}",
                        result
                    )
            
            # Validate detector type enumerated values
            detector_seq = acq_item.get('MultienergyCTXRayDetectorSequence', [])
            for j, detector_item in enumerate(detector_seq):
                detector_type = detector_item.get('MultienergyDetectorType', '')
                if detector_type:
                    valid_detector_types = [detector.value for detector in MultiEnergyDetectorType]
                    BaseValidator.validate_enumerated_value(
                        detector_type, valid_detector_types,
                        f"Multi-energy Detector Type (0018,9372) in detector {j+1}, acquisition {i+1}",
                        result
                    )
        
        return result
