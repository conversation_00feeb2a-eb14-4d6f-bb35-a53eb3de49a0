"""Modality LUT Module Validator - DICOM PS3.3 C.11.1 validation."""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ...enums.image_enums import ModalityLutType, RescaleType


class ModalityLutValidator(BaseValidator):
    """Validator for Modality LUT Module requirements."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Modality LUT Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate mutual exclusivity requirement
        result = ModalityLutValidator._validate_mutual_exclusivity(dataset, result)
        
        # Validate Modality LUT Sequence if present
        if hasattr(dataset, 'ModalityLUTSequence'):
            result = ModalityLutValidator._validate_modality_lut_sequence(dataset, result, config)
        
        # Validate Rescale parameters if present
        if hasattr(dataset, 'RescaleIntercept'):
            result = ModalityLutValidator._validate_rescale_parameters(dataset, result, config)
        
        return result
    
    @staticmethod
    def _validate_mutual_exclusivity(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate that either Modality LUT Sequence OR Rescale parameters are present, but not both."""
        has_lut_sequence = hasattr(dataset, 'ModalityLUTSequence')
        has_rescale = hasattr(dataset, 'RescaleIntercept')
        
        if has_lut_sequence and has_rescale:
            result["errors"].append(
                "Modality LUT Sequence and Rescale Intercept shall not both be present"
            )
        elif not has_lut_sequence and not has_rescale:
            result["errors"].append(
                "Either Modality LUT Sequence or Rescale Intercept shall be present"
            )
        
        return result
    
    @staticmethod
    def _validate_modality_lut_sequence(dataset: Dataset, result: dict[str, list[str]], config: ValidationConfig) -> dict[str, list[str]]:
        """Validate Modality LUT Sequence requirements."""
        if not hasattr(dataset, 'ModalityLUTSequence'):
            return result
        
        sequence = dataset.ModalityLUTSequence
        
        # Must contain exactly one item
        if len(sequence) != 1:
            result["errors"].append(
                f"Modality LUT Sequence must contain exactly one item, found {len(sequence)}"
            )
            return result
        
        item = sequence[0]
        
        # Validate required elements in sequence item
        required_fields = ['LUTDescriptor', 'ModalityLUTType', 'LUTData']
        for field in required_fields:
            if not hasattr(item, field):
                result["errors"].append(f"Missing required field {field} in Modality LUT Sequence item")
        
        # Validate LUT Descriptor format
        if hasattr(item, 'LUTDescriptor'):
            lut_desc = item.LUTDescriptor
            if not isinstance(lut_desc, (list, tuple)) or len(lut_desc) != 3:
                result["errors"].append("LUT Descriptor must contain exactly 3 values")
            else:
                # Validate third value (bits per entry)
                if lut_desc[2] not in [8, 16]:
                    result["errors"].append("LUT Descriptor third value must be 8 or 16")
        
        # Validate Modality LUT Type enumerated values
        if config.check_enumerated_values and hasattr(item, 'ModalityLUTType'):
            allowed_values = [e.value for e in ModalityLutType]
            result = BaseValidator.validate_enumerated_value(
                item.ModalityLUTType, allowed_values, 'ModalityLUTType', result
            )
        
        return result
    
    @staticmethod
    def _validate_rescale_parameters(dataset: Dataset, result: dict[str, list[str]], config: ValidationConfig) -> dict[str, list[str]]:
        """Validate Rescale parameters requirements."""
        # All three rescale parameters must be present together
        rescale_fields = ['RescaleIntercept', 'RescaleSlope', 'RescaleType']
        missing_fields = [field for field in rescale_fields if not hasattr(dataset, field)]
        
        if missing_fields:
            result["errors"].append(
                f"Missing required rescale fields: {', '.join(missing_fields)}. "
                "All rescale parameters (Intercept, Slope, Type) must be present together."
            )
        
        # Validate Rescale Type enumerated values
        if config.check_enumerated_values and hasattr(dataset, 'RescaleType'):
            allowed_values = [e.value for e in RescaleType]
            result = BaseValidator.validate_enumerated_value(
                dataset.RescaleType, allowed_values, 'RescaleType', result
            )
        
        # Validate numeric values
        if hasattr(dataset, 'RescaleSlope'):
            try:
                slope = float(dataset.RescaleSlope)
                if slope == 0:
                    result["warnings"].append("RescaleSlope of 0 may cause division by zero in calculations")
            except (ValueError, TypeError):
                result["errors"].append("RescaleSlope must be a valid numeric value")
        
        if hasattr(dataset, 'RescaleIntercept'):
            try:
                float(dataset.RescaleIntercept)
            except (ValueError, TypeError):
                result["errors"].append("RescaleIntercept must be a valid numeric value")
        
        return result
