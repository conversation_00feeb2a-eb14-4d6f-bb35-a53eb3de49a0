"""Device Module DICOM validation - PS3.3 C.7.6.12"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ...enums.equipment_enums import DeviceDiameterUnits


class DeviceValidator:
    """Validator for DICOM Device Module (PS3.3 C.7.6.12)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Device Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate Type 1 requirements
        result = DeviceValidator._validate_required_elements(dataset, result)
        
        # Validate Type 2C conditional requirements
        if config.validate_conditional_requirements:
            result = DeviceValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = DeviceValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            result = DeviceValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1 required elements."""
        
        # Type 1: Device Sequence (0050,0010)
        if not hasattr(dataset, 'DeviceSequence'):
            result["errors"].append("Device Sequence (0050,0010) is required (Type 1)")
        elif not dataset.DeviceSequence:
            result["errors"].append("Device Sequence (0050,0010) cannot be empty (Type 1)")
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 2C conditional requirements."""
        
        # Type 2C: Device Diameter Units required if Device Diameter is present
        device_seq = getattr(dataset, 'DeviceSequence', [])
        for i, item in enumerate(device_seq):
            device_diameter = item.get('DeviceDiameter')
            device_diameter_units = item.get('DeviceDiameterUnits')
            
            if device_diameter is not None and not device_diameter_units:
                result["errors"].append(
                    f"Device Sequence item {i}: Device Diameter Units (0050,0017) is required "
                    f"when Device Diameter (0050,0016) is present (Type 2C)"
                )
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values against DICOM specifications."""
        
        # Device Diameter Units (0050,0017)
        device_seq = getattr(dataset, 'DeviceSequence', [])
        for i, item in enumerate(device_seq):
            diameter_units = item.get('DeviceDiameterUnits', '')
            if diameter_units:
                valid_units = [unit.value for unit in DeviceDiameterUnits]
                BaseValidator.validate_enumerated_value(
                    diameter_units, valid_units,
                    f"Device Sequence item {i}: Device Diameter Units (0050,0017)", result
                )
        
        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate sequence structure requirements."""
        
        # Device Sequence - each item needs Code Sequence Macro attributes
        device_seq = getattr(dataset, 'DeviceSequence', [])
        for i, item in enumerate(device_seq):
            # Code Sequence Macro requires CodeValue, CodingSchemeDesignator, and CodeMeaning
            if not getattr(item, 'CodeValue', None):
                result["errors"].append(
                    f"Device Sequence item {i}: Code Value (0008,0100) is required"
                )
            if not getattr(item, 'CodingSchemeDesignator', None):
                result["errors"].append(
                    f"Device Sequence item {i}: Coding Scheme Designator (0008,0102) is required"
                )
            if not getattr(item, 'CodeMeaning', None):
                result["errors"].append(
                    f"Device Sequence item {i}: Code Meaning (0008,0104) is required"
                )
        
        return result
