"""RT Dose Module DICOM validation - PS3.3 C.8.8.3"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ...enums.dose_enums import DoseUnits, DoseType
from ...enums.rt_enums import SpatialTransformOfDose, DoseSummationType, TissueHeterogeneityCorrection
from ...enums.image_enums import PhotometricInterpretation, PixelRepresentation


class RTDoseValidator:
    """Validator for DICOM RT Dose Module (PS3.3 C.8.8.3)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate RT Dose Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            result = RTDoseValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = RTDoseValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            result = RTDoseValidator._validate_sequence_requirements(dataset, result)
        
        # Validate pixel data consistency
        result = RTDoseValidator._validate_pixel_data_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1C and 2C conditional requirements."""
        
        # Type 1C: Pixel data elements required if Pixel Data is present
        has_pixel_data = hasattr(dataset, 'PixelData')
        if has_pixel_data:
            pixel_elements = [
                ('SamplesPerPixel', '0028,0002'),
                ('PhotometricInterpretation', '0028,0004'),
                ('BitsAllocated', '0028,0100'),
                ('BitsStored', '0028,0101'),
                ('HighBit', '0028,0102'),
                ('PixelRepresentation', '0028,0103'),
                ('DoseGridScaling', '3004,000E')
            ]
            
            for element_name, tag in pixel_elements:
                if not hasattr(dataset, element_name):
                    result["errors"].append(
                        f"{element_name} ({tag}) is required when Pixel Data is present"
                    )
        
        # Type 1C: Referenced RT Plan Sequence required for certain dose summation types
        dose_summation_type = getattr(dataset, 'DoseSummationType', '')
        requires_rt_plan = dose_summation_type in [
            'PLAN', 'MULTI_PLAN', 'FRACTION', 'BEAM', 'BRACHY',
            'FRACTION_SESSION', 'BEAM_SESSION', 'BRACHY_SESSION', 'CONTROL_POINT'
        ]
        
        if requires_rt_plan and not hasattr(dataset, 'ReferencedRTPlanSequence'):
            result["errors"].append(
                f"Referenced RT Plan Sequence (300C,0002) is required when "
                f"Dose Summation Type is {dose_summation_type}"
            )
        
        # Type 2C: Referenced Spatial Registration Sequence required if spatial transform is RIGID or NON_RIGID
        spatial_transform = getattr(dataset, 'SpatialTransformOfDose', '')
        if spatial_transform in ['RIGID', 'NON_RIGID']:
            if not hasattr(dataset, 'ReferencedSpatialRegistrationSequence'):
                result["errors"].append(
                    "Referenced Spatial Registration Sequence (0070,0404) is required when "
                    f"Spatial Transform of Dose is {spatial_transform}"
                )
        
        # Type 1C: Grid Frame Offset Vector required if multi-frame pixel data present
        has_multi_frame = hasattr(dataset, 'NumberOfFrames') and getattr(dataset, 'NumberOfFrames', 1) > 1
        if has_multi_frame and has_pixel_data:
            frame_increment_pointer = getattr(dataset, 'FrameIncrementPointer', None)
            if frame_increment_pointer and '3004000C' in str(frame_increment_pointer):
                if not hasattr(dataset, 'GridFrameOffsetVector'):
                    result["errors"].append(
                        "Grid Frame Offset Vector (3004,000C) is required for multi-frame dose data "
                        "when Frame Increment Pointer points to it"
                    )
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values against DICOM specifications."""
        
        # Dose Units (3004,0002)
        dose_units = getattr(dataset, 'DoseUnits', '')
        if dose_units:
            valid_units = [units.value for units in DoseUnits]
            BaseValidator.validate_enumerated_value(
                dose_units, valid_units,
                "Dose Units (3004,0002)", result
            )
        
        # Dose Type (3004,0004)
        dose_type = getattr(dataset, 'DoseType', '')
        if dose_type:
            valid_types = [dtype.value for dtype in DoseType]
            BaseValidator.validate_enumerated_value(
                dose_type, valid_types,
                "Dose Type (3004,0004)", result
            )
        
        # Spatial Transform of Dose (3004,0005)
        spatial_transform = getattr(dataset, 'SpatialTransformOfDose', '')
        if spatial_transform:
            valid_transforms = [transform.value for transform in SpatialTransformOfDose]
            BaseValidator.validate_enumerated_value(
                spatial_transform, valid_transforms,
                "Spatial Transform of Dose (3004,0005)", result
            )
        
        # Dose Summation Type (3004,000A)
        dose_summation_type = getattr(dataset, 'DoseSummationType', '')
        if dose_summation_type:
            valid_summation_types = [stype.value for stype in DoseSummationType]
            BaseValidator.validate_enumerated_value(
                dose_summation_type, valid_summation_types,
                "Dose Summation Type (3004,000A)", result
            )
        
        # Tissue Heterogeneity Correction (3004,0014)
        tissue_correction = getattr(dataset, 'TissueHeterogeneityCorrection', '')
        if tissue_correction:
            valid_corrections = [corr.value for corr in TissueHeterogeneityCorrection]
            BaseValidator.validate_enumerated_value(
                tissue_correction, valid_corrections,
                "Tissue Heterogeneity Correction (3004,0014)", result
            )
        
        # Photometric Interpretation (0028,0004) - should be MONOCHROME2 for RT Dose
        photometric_interp = getattr(dataset, 'PhotometricInterpretation', '')
        if photometric_interp and photometric_interp != 'MONOCHROME2':
            result["warnings"].append(
                "Photometric Interpretation (0028,0004) should be MONOCHROME2 for RT Dose"
            )
        
        # Samples per Pixel (0028,0002) - should be 1 for RT Dose
        samples_per_pixel = getattr(dataset, 'SamplesPerPixel', None)
        if samples_per_pixel is not None and samples_per_pixel != 1:
            result["warnings"].append(
                "Samples per Pixel (0028,0002) should be 1 for RT Dose"
            )
        
        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate sequence structure requirements."""
        
        # Referenced RT Plan Sequence validation
        ref_rt_plan_seq = getattr(dataset, 'ReferencedRTPlanSequence', [])
        for i, plan_item in enumerate(ref_rt_plan_seq):
            if not plan_item.get('ReferencedSOPClassUID'):
                result["errors"].append(
                    f"Referenced RT Plan Sequence item {i}: "
                    "Referenced SOP Class UID (0008,1150) is required"
                )
            if not plan_item.get('ReferencedSOPInstanceUID'):
                result["errors"].append(
                    f"Referenced RT Plan Sequence item {i}: "
                    "Referenced SOP Instance UID (0008,1155) is required"
                )
        
        # Referenced Spatial Registration Sequence validation
        ref_spatial_reg_seq = getattr(dataset, 'ReferencedSpatialRegistrationSequence', [])
        for i, reg_item in enumerate(ref_spatial_reg_seq):
            if not reg_item.get('ReferencedSOPClassUID'):
                result["errors"].append(
                    f"Referenced Spatial Registration Sequence item {i}: "
                    "Referenced SOP Class UID (0008,1150) is required"
                )
            if not reg_item.get('ReferencedSOPInstanceUID'):
                result["errors"].append(
                    f"Referenced Spatial Registration Sequence item {i}: "
                    "Referenced SOP Instance UID (0008,1155) is required"
                )
        
        # Recommended Isodose Level Sequence validation
        isodose_seq = getattr(dataset, 'RecommendedIsodoseLevelSequence', [])
        for i, isodose_item in enumerate(isodose_seq):
            if not isodose_item.get('DoseValue'):
                result["errors"].append(
                    f"Recommended Isodose Level Sequence item {i}: "
                    "Dose Value (3004,0012) is required"
                )
            if not isodose_item.get('RecommendedDisplayCIELabValue'):
                result["errors"].append(
                    f"Recommended Isodose Level Sequence item {i}: "
                    "Recommended Display CIELab Value (0062,000D) is required"
                )
        
        return result
    
    @staticmethod
    def _validate_pixel_data_consistency(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate pixel data consistency and logical constraints."""
        
        # Validate bits allocated/stored/high bit consistency
        bits_allocated = getattr(dataset, 'BitsAllocated', None)
        bits_stored = getattr(dataset, 'BitsStored', None)
        high_bit = getattr(dataset, 'HighBit', None)
        
        if bits_allocated is not None and bits_stored is not None:
            if bits_stored != bits_allocated:
                result["warnings"].append(
                    "For RT Doses, Bits Stored should equal Bits Allocated"
                )
            
            if high_bit is not None and high_bit != bits_stored - 1:
                result["warnings"].append(
                    "For RT Doses, High Bit should be one less than Bits Stored"
                )
        
        # Validate bits allocated values
        if bits_allocated is not None and bits_allocated not in [16, 32]:
            result["warnings"].append(
                "Bits Allocated (0028,0100) should be 16 or 32 for RT Dose"
            )
        
        # Validate pixel representation based on dose type
        dose_type = getattr(dataset, 'DoseType', '')
        pixel_representation = getattr(dataset, 'PixelRepresentation', '')
        
        if dose_type == 'ERROR' and pixel_representation != '0001H':
            result["warnings"].append(
                "Pixel Representation should be 0001H (signed) when Dose Type is ERROR"
            )
        elif dose_type != 'ERROR' and pixel_representation != '0000H':
            result["warnings"].append(
                "Pixel Representation should be 0000H (unsigned) when Dose Type is not ERROR"
            )
        
        # Validate dose grid scaling
        dose_grid_scaling = getattr(dataset, 'DoseGridScaling', None)
        if dose_grid_scaling is not None and dose_grid_scaling <= 0:
            result["warnings"].append(
                "Dose Grid Scaling (3004,000E) should be positive"
            )
        
        return result
