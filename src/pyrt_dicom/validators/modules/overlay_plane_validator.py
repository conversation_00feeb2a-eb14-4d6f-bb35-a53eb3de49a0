"""Overlay Plane Module DICOM validation - PS3.3 C.9.2"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ...enums.image_enums import OverlayType, OverlaySubtype


class OverlayPlaneValidator:
    """Validator for DICOM Overlay Plane Module (PS3.3 C.9.2)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Overlay Plane Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate Type 1 requirements
        result = OverlayPlaneValidator._validate_type1_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = OverlayPlaneValidator._validate_enumerated_values(dataset, result)
        
        # Validate overlay data consistency
        result = OverlayPlaneValidator._validate_overlay_data_consistency(dataset, result)
        
        # Validate ROI statistics consistency
        result = OverlayPlaneValidator._validate_roi_statistics_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_type1_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1 (required) attributes."""
        
        required_attrs = [
            ('OverlayRows', '60xx,0010'),
            ('OverlayColumns', '60xx,0011'),
            ('OverlayType', '60xx,0040'),
            ('OverlayOrigin', '60xx,0050'),
            ('OverlayBitsAllocated', '60xx,0100'),
            ('OverlayBitPosition', '60xx,0102'),
            ('OverlayData', '60xx,3000')
        ]
        
        for attr_name, tag in required_attrs:
            if not hasattr(dataset, attr_name):
                result["errors"].append(f"{attr_name} ({tag}) is required (Type 1)")
        
        # Validate specific Type 1 constraints
        if hasattr(dataset, 'OverlayRows'):
            if not isinstance(dataset.OverlayRows, int) or dataset.OverlayRows < 1:
                result["errors"].append("Overlay Rows (60xx,0010) must be a positive integer")
        
        if hasattr(dataset, 'OverlayColumns'):
            if not isinstance(dataset.OverlayColumns, int) or dataset.OverlayColumns < 1:
                result["errors"].append("Overlay Columns (60xx,0011) must be a positive integer")
        
        if hasattr(dataset, 'OverlayOrigin'):
            if not isinstance(dataset.OverlayOrigin, (list, tuple)) or len(dataset.OverlayOrigin) != 2:
                result["errors"].append("Overlay Origin (60xx,0050) must be a pair of values [row, column]")
            else:
                try:
                    row, col = int(dataset.OverlayOrigin[0]), int(dataset.OverlayOrigin[1])
                    # Note: DICOM allows values < 1 to indicate overlay extends beyond image
                except (ValueError, TypeError):
                    result["errors"].append("Overlay Origin (60xx,0050) values must be integers")
        
        if hasattr(dataset, 'OverlayBitsAllocated'):
            if dataset.OverlayBitsAllocated != 1:
                result["errors"].append("Overlay Bits Allocated (60xx,0100) must be 1")
        
        if hasattr(dataset, 'OverlayBitPosition'):
            if dataset.OverlayBitPosition != 0:
                result["errors"].append("Overlay Bit Position (60xx,0102) must be 0")
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values against DICOM standard."""
        
        # Overlay Type
        if hasattr(dataset, 'OverlayType'):
            valid_values = [e.value for e in OverlayType]
            if dataset.OverlayType not in valid_values:
                result["errors"].append(
                    f"Overlay Type (60xx,0040) has invalid value '{dataset.OverlayType}'. "
                    f"Valid values: {valid_values}"
                )
        
        # Overlay Subtype
        if hasattr(dataset, 'OverlaySubtype'):
            valid_values = [e.value for e in OverlaySubtype]
            if dataset.OverlaySubtype not in valid_values:
                result["errors"].append(
                    f"Overlay Subtype (60xx,0045) has invalid value '{dataset.OverlaySubtype}'. "
                    f"Valid values: {valid_values}"
                )
        
        return result
    
    @staticmethod
    def _validate_overlay_data_consistency(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate overlay data consistency and size."""
        
        if not all(hasattr(dataset, attr) for attr in ['OverlayRows', 'OverlayColumns', 'OverlayData']):
            return result
        
        # Calculate expected data size
        total_pixels = dataset.OverlayRows * dataset.OverlayColumns
        expected_bits = total_pixels
        expected_bytes = (expected_bits + 7) // 8  # Round up to nearest byte
        # DICOM requires even length
        expected_bytes_padded = expected_bytes + (expected_bytes % 2)
        
        actual_bytes = len(dataset.OverlayData)
        
        if actual_bytes < expected_bytes:
            result["errors"].append(
                f"Overlay Data (60xx,3000) size ({actual_bytes} bytes) is less than required "
                f"for {dataset.OverlayRows}x{dataset.OverlayColumns} overlay (minimum {expected_bytes} bytes)"
            )
        elif actual_bytes != expected_bytes_padded:
            result["warnings"].append(
                f"Overlay Data (60xx,3000) size ({actual_bytes} bytes) differs from expected "
                f"padded size ({expected_bytes_padded} bytes) for {dataset.OverlayRows}x{dataset.OverlayColumns} overlay"
            )
        
        return result
    
    @staticmethod
    def _validate_roi_statistics_consistency(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate ROI statistics consistency."""
        
        # ROI statistics should only be present for ROI overlays
        roi_attrs = ['ROIArea', 'ROIMean', 'ROIStandardDeviation']
        has_roi_stats = any(hasattr(dataset, attr) for attr in roi_attrs)
        
        if has_roi_stats:
            if hasattr(dataset, 'OverlayType') and dataset.OverlayType != "R":
                result["warnings"].append(
                    "ROI statistics are present but Overlay Type is not 'R' (ROI). "
                    "ROI statistics are typically only meaningful for ROI overlays."
                )
        
        # Validate individual ROI statistics
        if hasattr(dataset, 'ROIArea'):
            try:
                roi_area = int(dataset.ROIArea)
                if roi_area < 0:
                    result["errors"].append("ROI Area (60xx,1301) must be non-negative")
                
                # Cross-validate with overlay size
                if hasattr(dataset, 'OverlayRows') and hasattr(dataset, 'OverlayColumns'):
                    max_area = dataset.OverlayRows * dataset.OverlayColumns
                    if roi_area > max_area:
                        result["warnings"].append(
                            f"ROI Area ({roi_area}) exceeds total overlay area ({max_area})"
                        )
            except (ValueError, TypeError):
                result["errors"].append("ROI Area (60xx,1301) must be an integer")
        
        if hasattr(dataset, 'ROIMean'):
            try:
                roi_mean = float(dataset.ROIMean)
                # No specific constraints on ROI mean value
            except (ValueError, TypeError):
                result["errors"].append("ROI Mean (60xx,1302) must be numeric")
        
        if hasattr(dataset, 'ROIStandardDeviation'):
            try:
                roi_std = float(dataset.ROIStandardDeviation)
                if roi_std < 0:
                    result["errors"].append("ROI Standard Deviation (60xx,1303) must be non-negative")
            except (ValueError, TypeError):
                result["errors"].append("ROI Standard Deviation (60xx,1303) must be numeric")
        
        return result
