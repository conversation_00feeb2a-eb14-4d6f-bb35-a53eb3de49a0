"""RT Brachy Application Setups Module DICOM validation - PS3.3 C.8.8.15"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig


class RTBrachyApplicationSetupsValidator:
    """Validator for DICOM RT Brachy Application Setups Module (PS3.3 C.8.8.15)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate RT Brachy Application Setups Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate sequence structures
        if config.validate_sequences:
            result = RTBrachyApplicationSetupsValidator._validate_sequence_requirements(dataset, result)
        
        # Validate brachy setup parameter consistency
        result = RTBrachyApplicationSetupsValidator._validate_brachy_setup_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate sequence structure requirements."""

        # Brachy Treatment Technique (300A,0200) is Type 1
        if not hasattr(dataset, 'BrachyTreatmentTechnique') or not dataset.BrachyTreatmentTechnique:
            result["errors"].append(
                "Brachy Treatment Technique (300A,0200) is required (Type 1)"
            )

        # Brachy Treatment Type (300A,0202) is Type 1
        if not hasattr(dataset, 'BrachyTreatmentType') or not dataset.BrachyTreatmentType:
            result["errors"].append(
                "Brachy Treatment Type (300A,0202) is required (Type 1)"
            )

        # Treatment Machine Sequence (300A,0206) is Type 1
        treatment_machine_seq = getattr(dataset, 'TreatmentMachineSequence', [])
        if not treatment_machine_seq:
            result["errors"].append(
                "Treatment Machine Sequence (300A,0206) is required (Type 1)"
            )
        else:
            for i, machine_item in enumerate(treatment_machine_seq):
                # Treatment Machine Name is Type 2
                if 'TreatmentMachineName' not in machine_item:
                    result["errors"].append(
                        f"Treatment Machine Sequence item {i}: "
                        "Treatment Machine Name (300A,00B2) is required (Type 2)"
                    )

        # Source Sequence (300A,0210) is Type 1
        source_seq = getattr(dataset, 'SourceSequence', [])
        if not source_seq:
            result["errors"].append(
                "Source Sequence (300A,0210) is required (Type 1)"
            )
        else:
            for i, source_item in enumerate(source_seq):
                # Source Number is Type 1
                if not source_item.get('SourceNumber'):
                    result["errors"].append(
                        f"Source Sequence item {i}: "
                        "Source Number (300A,0212) is required"
                    )

                # Source Type is Type 1
                if not source_item.get('SourceType'):
                    result["errors"].append(
                        f"Source Sequence item {i}: "
                        "Source Type (300A,0214) is required"
                    )

                # Source Isotope Name is Type 1
                if not source_item.get('SourceIsotopeName'):
                    result["errors"].append(
                        f"Source Sequence item {i}: "
                        "Source Isotope Name (300A,0226) is required"
                    )

                # Source Isotope Half Life is Type 1
                if source_item.get('SourceIsotopeHalfLife') is None:
                    result["errors"].append(
                        f"Source Sequence item {i}: "
                        "Source Isotope Half Life (300A,0228) is required"
                    )

                # Reference Air Kerma Rate is Type 1
                if source_item.get('ReferenceAirKermaRate') is None:
                    result["errors"].append(
                        f"Source Sequence item {i}: "
                        "Reference Air Kerma Rate (300A,022A) is required"
                    )

                # Source Strength Reference Date is Type 1
                if not source_item.get('SourceStrengthReferenceDate'):
                    result["errors"].append(
                        f"Source Sequence item {i}: "
                        "Source Strength Reference Date (300A,022C) is required"
                    )

                # Source Strength Reference Time is Type 1
                if not source_item.get('SourceStrengthReferenceTime'):
                    result["errors"].append(
                        f"Source Sequence item {i}: "
                        "Source Strength Reference Time (300A,022E) is required"
                    )

        # Application Setup Sequence validation (Type 1)
        brachy_setup_seq = getattr(dataset, 'ApplicationSetupSequence', [])
        if not brachy_setup_seq:
            result["errors"].append(
                "Application Setup Sequence (300A,0230) is required (Type 1)"
            )
        
        for i, setup_item in enumerate(brachy_setup_seq):
            # Brachy Application Setup Number is Type 1
            if not setup_item.get('BrachyApplicationSetupNumber'):
                result["errors"].append(
                    f"Brachy Application Setup Sequence item {i}: "
                    "Brachy Application Setup Number (300A,0234) is required"
                )
            
            # Brachy Application Setup Name is Type 2
            if 'BrachyApplicationSetupName' not in setup_item:
                result["errors"].append(
                    f"Brachy Application Setup Sequence item {i}: "
                    "Brachy Application Setup Name (300A,0236) is required (Type 2)"
                )
            
            # Brachy Application Setup Type is Type 2
            if 'BrachyApplicationSetupType' not in setup_item:
                result["errors"].append(
                    f"Brachy Application Setup Sequence item {i}: "
                    "Brachy Application Setup Type (300A,0238) is required (Type 2)"
                )
            
            # Validate Source Sequence
            source_seq = setup_item.get('SourceSequence', [])
            for j, source_item in enumerate(source_seq):
                if not source_item.get('SourceNumber'):
                    result["errors"].append(
                        f"Source Sequence item {j} in Setup {i}: "
                        "Source Number (300A,0212) is required"
                    )
                if not source_item.get('SourceIsotopeName'):
                    result["errors"].append(
                        f"Source Sequence item {j} in Setup {i}: "
                        "Source Isotope Name (300A,0226) is required"
                    )
                if not source_item.get('SourceIsotopeHalfLife'):
                    result["errors"].append(
                        f"Source Sequence item {j} in Setup {i}: "
                        "Source Isotope Half Life (300A,0228) is required"
                    )
                if not source_item.get('ReferenceAirKermaRate'):
                    result["errors"].append(
                        f"Source Sequence item {j} in Setup {i}: "
                        "Reference Air Kerma Rate (300A,022A) is required"
                    )
            
            # Validate Application Setup Sequence
            app_setup_seq = setup_item.get('ApplicationSetupSequence', [])
            for j, app_item in enumerate(app_setup_seq):
                if not app_item.get('ApplicationSetupType'):
                    result["errors"].append(
                        f"Application Setup Sequence item {j} in Setup {i}: "
                        "Application Setup Type (300A,0242) is required"
                    )
                if not app_item.get('ApplicationSetupNumber'):
                    result["errors"].append(
                        f"Application Setup Sequence item {j} in Setup {i}: "
                        "Application Setup Number (300A,0244) is required"
                    )
        
        # Validate uniqueness of brachy application setup numbers
        setup_numbers = []
        for i, setup_item in enumerate(brachy_setup_seq):
            setup_number = setup_item.get('BrachyApplicationSetupNumber')
            if setup_number is not None:
                if setup_number in setup_numbers:
                    result["errors"].append(
                        f"Brachy Application Setup Sequence item {i}: "
                        f"Brachy Application Setup Number ({setup_number}) must be unique within the RT Plan"
                    )
                else:
                    setup_numbers.append(setup_number)
        
        return result
    
    @staticmethod
    def _validate_brachy_setup_consistency(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate brachy setup parameter consistency and logical constraints."""
        
        brachy_setup_seq = getattr(dataset, 'BrachyApplicationSetupSequence', [])
        for i, setup_item in enumerate(brachy_setup_seq):
            # Validate Source Sequence consistency
            source_seq = setup_item.get('SourceSequence', [])
            source_numbers = []
            
            for j, source_item in enumerate(source_seq):
                # Check source number uniqueness within setup
                source_number = source_item.get('SourceNumber')
                if source_number is not None:
                    if source_number in source_numbers:
                        result["warnings"].append(
                            f"Source Sequence item {j} in Setup {i}: "
                            f"Source Number ({source_number}) should be unique within the setup"
                        )
                    else:
                        source_numbers.append(source_number)
                
                # Validate isotope half-life is positive
                half_life = source_item.get('SourceIsotopeHalfLife')
                if half_life is not None and half_life <= 0:
                    result["warnings"].append(
                        f"Source Sequence item {j} in Setup {i}: "
                        f"Source Isotope Half Life ({half_life}) should be positive"
                    )
                
                # Validate reference air kerma rate is positive
                kerma_rate = source_item.get('ReferenceAirKermaRate')
                if kerma_rate is not None and kerma_rate <= 0:
                    result["warnings"].append(
                        f"Source Sequence item {j} in Setup {i}: "
                        f"Reference Air Kerma Rate ({kerma_rate}) should be positive"
                    )
                
                # Validate encapsulation transmission is between 0 and 1
                transmission = source_item.get('SourceEncapsulationNominalTransmission')
                if transmission is not None and (transmission < 0 or transmission > 1):
                    result["warnings"].append(
                        f"Source Sequence item {j} in Setup {i}: "
                        f"Source Encapsulation Nominal Transmission ({transmission}) should be between 0 and 1"
                    )
                
                # Validate nominal activity is positive
                activity = source_item.get('SourceIsotopeNominalActivity')
                if activity is not None and activity <= 0:
                    result["warnings"].append(
                        f"Source Sequence item {j} in Setup {i}: "
                        f"Source Isotope Nominal Activity ({activity}) should be positive"
                    )
            
            # Validate Application Setup Sequence consistency
            app_setup_seq = setup_item.get('ApplicationSetupSequence', [])
            app_setup_numbers = []
            
            for j, app_item in enumerate(app_setup_seq):
                # Check application setup number uniqueness within brachy setup
                app_number = app_item.get('ApplicationSetupNumber')
                if app_number is not None:
                    if app_number in app_setup_numbers:
                        result["warnings"].append(
                            f"Application Setup Sequence item {j} in Setup {i}: "
                            f"Application Setup Number ({app_number}) should be unique within the brachy setup"
                        )
                    else:
                        app_setup_numbers.append(app_number)
                
                # Validate total reference air kerma is positive
                total_kerma = app_item.get('TotalReferenceAirKerma')
                if total_kerma is not None and total_kerma <= 0:
                    result["warnings"].append(
                        f"Application Setup Sequence item {j} in Setup {i}: "
                        f"Total Reference Air Kerma ({total_kerma}) should be positive"
                    )
        
        return result
