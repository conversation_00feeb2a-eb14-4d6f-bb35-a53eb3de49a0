"""Approval Module Validator - DICOM PS3.3 C.8.8.16 validation."""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ...enums.approval_enums import ApprovalStatus


class ApprovalValidator(BaseValidator):
    """Validator for Approval Module requirements."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Approval Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate Type 1 requirements
        result = ApprovalValidator._validate_required_elements(dataset, result)
        
        # Validate Type 2C conditional requirements
        if config.validate_conditional_requirements:
            result = ApprovalValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = ApprovalValidator._validate_enumerated_values(dataset, result)
        
        # Validate date/time formats
        result = ApprovalValidator._validate_date_time_formats(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1 required elements."""
        if not hasattr(dataset, 'ApprovalStatus'):
            result["errors"].append("Missing required element: ApprovalStatus (300E,0002)")
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 2C conditional requirements."""
        if not hasattr(dataset, 'ApprovalStatus'):
            return result
        
        approval_status = str(dataset.ApprovalStatus)
        requires_review_info = approval_status in [ApprovalStatus.APPROVED.value, ApprovalStatus.REJECTED.value]
        
        if requires_review_info:
            # Review Date, Review Time, and Reviewer Name are required
            required_fields = ['ReviewDate', 'ReviewTime', 'ReviewerName']
            missing_fields = [field for field in required_fields if not hasattr(dataset, field)]
            
            if missing_fields:
                result["errors"].append(
                    f"Missing required review information for {approval_status} status: {', '.join(missing_fields)}"
                )
        else:
            # For UNAPPROVED status, review information should not be present
            if approval_status == ApprovalStatus.UNAPPROVED.value:
                review_fields = ['ReviewDate', 'ReviewTime', 'ReviewerName']
                present_fields = [field for field in review_fields if hasattr(dataset, field)]
                
                if present_fields:
                    result["warnings"].append(
                        f"Review information present for UNAPPROVED status: {', '.join(present_fields)}. "
                        "This may be inconsistent."
                    )
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values."""
        if hasattr(dataset, 'ApprovalStatus'):
            allowed_values = [e.value for e in ApprovalStatus]
            result = BaseValidator.validate_enumerated_value(
                dataset.ApprovalStatus, allowed_values, 'ApprovalStatus', result
            )
        
        return result
    
    @staticmethod
    def _validate_date_time_formats(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate date and time format requirements."""
        # Validate Review Date format (YYYYMMDD)
        if hasattr(dataset, 'ReviewDate'):
            review_date = str(dataset.ReviewDate)
            if not ApprovalValidator._is_valid_date_format(review_date):
                result["errors"].append(
                    f"ReviewDate '{review_date}' must be in YYYYMMDD format"
                )
        
        # Validate Review Time format (HHMMSS or HHMMSS.FFFFFF)
        if hasattr(dataset, 'ReviewTime'):
            review_time = str(dataset.ReviewTime)
            if not ApprovalValidator._is_valid_time_format(review_time):
                result["errors"].append(
                    f"ReviewTime '{review_time}' must be in HHMMSS or HHMMSS.FFFFFF format"
                )
        
        return result
    
    @staticmethod
    def _is_valid_date_format(date_str: str) -> bool:
        """Check if date string is in valid DICOM DA format (YYYYMMDD)."""
        if len(date_str) != 8:
            return False
        
        try:
            year = int(date_str[:4])
            month = int(date_str[4:6])
            day = int(date_str[6:8])
            
            # Basic range checks
            if not (1900 <= year <= 9999):
                return False
            if not (1 <= month <= 12):
                return False
            if not (1 <= day <= 31):
                return False
            
            return True
        except ValueError:
            return False
    
    @staticmethod
    def _is_valid_time_format(time_str: str) -> bool:
        """Check if time string is in valid DICOM TM format (HHMMSS or HHMMSS.FFFFFF)."""
        # Remove fractional seconds if present
        if '.' in time_str:
            base_time, fraction = time_str.split('.', 1)
            if len(fraction) > 6:  # Max 6 digits for microseconds
                return False
        else:
            base_time = time_str
        
        if len(base_time) != 6:
            return False
        
        try:
            hour = int(base_time[:2])
            minute = int(base_time[2:4])
            second = int(base_time[4:6])
            
            # Basic range checks
            if not (0 <= hour <= 23):
                return False
            if not (0 <= minute <= 59):
                return False
            if not (0 <= second <= 59):
                return False
            
            return True
        except ValueError:
            return False
