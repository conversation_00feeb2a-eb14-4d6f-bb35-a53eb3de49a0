"""Base validation framework for DICOM modules."""

from dataclasses import dataclass, field
from pydicom import Dataset


@dataclass
class ValidationConfig:
    """Configuration for DICOM validation behavior."""
    strict_mode: bool = False
    check_enumerated_values: bool = True
    validate_sequences: bool = True
    validate_conditional_requirements: bool = True


class BaseValidator:
    """Base validation utilities for DICOM modules."""
    
    @staticmethod
    def validate_enumerated_value(
        value: any, 
        allowed_values: list[str], 
        field_name: str,
        result: dict[str, list[str]]
    ) -> dict[str, list[str]]:
        """Validate enumerated values against allowed list.
        
        Args:
            value: Value to validate
            allowed_values: List of allowed string values
            field_name: DICOM field name for error reporting
            result: Existing validation result to update
            
        Returns:
            Updated validation result
        """
        if value and str(value) not in allowed_values:
            result["warnings"].append(
                f"{field_name} value '{value}' should be one of: {', '.join(allowed_values)}"
            )
        return result
    
    @staticmethod
    def validate_conditional_requirement(
        condition: bool,
        required_fields: list[str],
        dataset: Dataset,
        error_message: str,
        result: dict[str, list[str]]
    ) -> dict[str, list[str]]:
        """Validate Type 1C conditional requirements.
        
        Args:
            condition: Whether the condition is met
            required_fields: List of field names that should be present
            dataset: Dataset to check for field presence
            error_message: Error message if requirement not met
            result: Existing validation result to update
            
        Returns:
            Updated validation result
        """
        if condition:
            missing_fields = [field for field in required_fields if not hasattr(dataset, field)]
            if len(missing_fields) == len(required_fields):
                result["errors"].append(error_message)
        return result
    
    @staticmethod
    def validate_either_or_requirement(
        condition: bool,
        field_a: str,
        field_b: str,
        dataset: Dataset,
        error_message: str,
        result: dict[str, list[str]]
    ) -> dict[str, list[str]]:
        """Validate either/or conditional requirements.
        
        Args:
            condition: Whether the condition is met
            field_a: First alternative field name
            field_b: Second alternative field name  
            dataset: Dataset to check
            error_message: Error message if neither field present
            result: Existing validation result to update
            
        Returns:
            Updated validation result
        """
        if condition:
            has_a = hasattr(dataset, field_a) and getattr(dataset, field_a, None)
            has_b = hasattr(dataset, field_b) and getattr(dataset, field_b, None)
            if not has_a and not has_b:
                result["errors"].append(error_message)
        return result
    
    @staticmethod
    def format_validation_result(errors: list[str], warnings: list[str]) -> dict[str, list[str]]:
        """Format validation results consistently.
        
        Args:
            errors: List of validation errors
            warnings: List of validation warnings
            
        Returns:
            Formatted validation result dictionary
        """
        return {"errors": errors, "warnings": warnings}