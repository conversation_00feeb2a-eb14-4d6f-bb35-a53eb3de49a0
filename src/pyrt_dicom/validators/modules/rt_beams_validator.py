"""RT Beams Module DICOM validation - PS3.3 C.8.8.14"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ...enums.rt_enums import RTBeamLimitingDeviceType, PrimaryDosimeterUnit, EnhancedRTBeamLimitingDeviceDefinitionFlag, BeamType, RadiationType


class RTBeamsValidator:
    """Validator for DICOM RT Beams Module (PS3.3 C.8.8.14)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate RT Beams Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            result = RTBeamsValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = RTBeamsValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            result = RTBeamsValidator._validate_sequence_requirements(dataset, result)
        
        # Validate beam parameter consistency
        result = RTBeamsValidator._validate_beam_parameter_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1C conditional requirements."""
        
        beam_sequence = getattr(dataset, 'BeamSequence', [])
        for i, beam_item in enumerate(beam_sequence):
            enhanced_flag = beam_item.get('EnhancedRTBeamLimitingDeviceDefinitionFlag', '')
            
            # Type 1C: Beam Limiting Device Sequence required if Enhanced flag is absent or NO
            if enhanced_flag != 'YES':
                if not beam_item.get('BeamLimitingDeviceSequence'):
                    result["errors"].append(
                        f"Beam Sequence item {i}: "
                        "Beam Limiting Device Sequence (300A,00B6) is required when "
                        "Enhanced RT Beam Limiting Device Definition Flag is absent or NO"
                    )
            
            # Type 1C: Enhanced RT Beam Limiting Device Sequence required if Enhanced flag is YES
            if enhanced_flag == 'YES':
                if not beam_item.get('EnhancedRTBeamLimitingDeviceSequence'):
                    result["errors"].append(
                        f"Beam Sequence item {i}: "
                        "Enhanced RT Beam Limiting Device Sequence (3008,00A1) is required when "
                        "Enhanced RT Beam Limiting Device Definition Flag is YES"
                    )
            
            # Validate Beam Limiting Device Sequence conditional requirements
            bld_sequence = beam_item.get('BeamLimitingDeviceSequence', [])
            for j, bld_item in enumerate(bld_sequence):
                device_type = bld_item.get('RTBeamLimitingDeviceType', '')
                leaf_boundaries = bld_item.get('LeafPositionBoundaries')
                
                # Type 2C: Leaf Position Boundaries required if device type is MLCX or MLCY
                if device_type in ['MLCX', 'MLCY'] and not leaf_boundaries:
                    result["errors"].append(
                        f"Beam Limiting Device Sequence item {j} in Beam {i}: "
                        f"Leaf Position Boundaries (300A,00BE) is required when "
                        f"RT Beam Limiting Device Type is {device_type}"
                    )
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values against DICOM specifications."""
        
        beam_sequence = getattr(dataset, 'BeamSequence', [])
        for i, beam_item in enumerate(beam_sequence):
            # Beam Type (300A,00C4)
            beam_type = beam_item.get('BeamType', '')
            if beam_type:
                valid_beam_types = [btype.value for btype in BeamType]
                BaseValidator.validate_enumerated_value(
                    beam_type, valid_beam_types,
                    f"Beam Type (300A,00C4) in Beam {i}", result
                )
            
            # Radiation Type (300A,00C6)
            radiation_type = beam_item.get('RadiationType', '')
            if radiation_type:
                valid_radiation_types = [rtype.value for rtype in RadiationType]
                BaseValidator.validate_enumerated_value(
                    radiation_type, valid_radiation_types,
                    f"Radiation Type (300A,00C6) in Beam {i}", result
                )
            
            # Primary Dosimeter Unit (300A,00B3)
            dosimeter_unit = beam_item.get('PrimaryDosimeterUnit', '')
            if dosimeter_unit:
                valid_dosimeter_units = [unit.value for unit in PrimaryDosimeterUnit]
                BaseValidator.validate_enumerated_value(
                    dosimeter_unit, valid_dosimeter_units,
                    f"Primary Dosimeter Unit (300A,00B3) in Beam {i}", result
                )
            
            # Enhanced RT Beam Limiting Device Definition Flag (3008,00A3)
            enhanced_flag = beam_item.get('EnhancedRTBeamLimitingDeviceDefinitionFlag', '')
            if enhanced_flag:
                valid_flags = [flag.value for flag in EnhancedRTBeamLimitingDeviceDefinitionFlag]
                BaseValidator.validate_enumerated_value(
                    enhanced_flag, valid_flags,
                    f"Enhanced RT Beam Limiting Device Definition Flag (3008,00A3) in Beam {i}", result
                )
            
            # Validate Beam Limiting Device Sequence enumerated values
            bld_sequence = beam_item.get('BeamLimitingDeviceSequence', [])
            for j, bld_item in enumerate(bld_sequence):
                device_type = bld_item.get('RTBeamLimitingDeviceType', '')
                if device_type:
                    valid_device_types = [dtype.value for dtype in RTBeamLimitingDeviceType]
                    BaseValidator.validate_enumerated_value(
                        device_type, valid_device_types,
                        f"RT Beam Limiting Device Type (300A,00B8) in Device {j}, Beam {i}", result
                    )
        
        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate sequence structure requirements."""
        
        # Beam Sequence validation (Type 1)
        beam_sequence = getattr(dataset, 'BeamSequence', [])
        if not beam_sequence:
            result["errors"].append(
                "Beam Sequence (300A,00B0) is required (Type 1)"
            )
        
        for i, beam_item in enumerate(beam_sequence):
            # Beam Number is Type 1
            if not beam_item.get('BeamNumber'):
                result["errors"].append(
                    f"Beam Sequence item {i}: "
                    "Beam Number (300A,00C0) is required"
                )
            
            # Beam Type is Type 1
            if not beam_item.get('BeamType'):
                result["errors"].append(
                    f"Beam Sequence item {i}: "
                    "Beam Type (300A,00C4) is required"
                )
            
            # Treatment Machine Name is Type 2
            if 'TreatmentMachineName' not in beam_item:
                result["errors"].append(
                    f"Beam Sequence item {i}: "
                    "Treatment Machine Name (300A,00B2) is required (Type 2)"
                )
            
            # Radiation Type is Type 2
            if 'RadiationType' not in beam_item:
                result["errors"].append(
                    f"Beam Sequence item {i}: "
                    "Radiation Type (300A,00C6) is required (Type 2)"
                )
            
            # Validate Beam Limiting Device Sequence
            bld_sequence = beam_item.get('BeamLimitingDeviceSequence', [])
            for j, bld_item in enumerate(bld_sequence):
                if not bld_item.get('RTBeamLimitingDeviceType'):
                    result["errors"].append(
                        f"Beam Limiting Device Sequence item {j} in Beam {i}: "
                        "RT Beam Limiting Device Type (300A,00B8) is required"
                    )
                if not bld_item.get('NumberOfLeafJawPairs'):
                    result["errors"].append(
                        f"Beam Limiting Device Sequence item {j} in Beam {i}: "
                        "Number of Leaf/Jaw Pairs (300A,00BC) is required"
                    )
        
        # Validate uniqueness of beam numbers
        beam_numbers = []
        for i, beam_item in enumerate(beam_sequence):
            beam_number = beam_item.get('BeamNumber')
            if beam_number is not None:
                if beam_number in beam_numbers:
                    result["errors"].append(
                        f"Beam Sequence item {i}: "
                        f"Beam Number ({beam_number}) must be unique within the RT Plan"
                    )
                else:
                    beam_numbers.append(beam_number)
        
        return result
    
    @staticmethod
    def _validate_beam_parameter_consistency(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate beam parameter consistency and logical constraints."""
        
        beam_sequence = getattr(dataset, 'BeamSequence', [])
        for i, beam_item in enumerate(beam_sequence):
            # Validate Source Axis Distance is positive
            source_axis_distance = beam_item.get('SourceAxisDistance')
            if source_axis_distance is not None and source_axis_distance <= 0:
                result["warnings"].append(
                    f"Beam Sequence item {i}: "
                    f"Source Axis Distance ({source_axis_distance}) should be positive"
                )
            
            # Validate Beam Limiting Device Sequence consistency
            bld_sequence = beam_item.get('BeamLimitingDeviceSequence', [])
            for j, bld_item in enumerate(bld_sequence):
                # Validate Source to Beam Limiting Device Distance is positive
                source_to_bld_distance = bld_item.get('SourceToBeamLimitingDeviceDistance')
                if source_to_bld_distance is not None and source_to_bld_distance <= 0:
                    result["warnings"].append(
                        f"Beam Limiting Device Sequence item {j} in Beam {i}: "
                        f"Source to Beam Limiting Device Distance ({source_to_bld_distance}) should be positive"
                    )
                
                # Validate Number of Leaf/Jaw Pairs is positive
                num_pairs = bld_item.get('NumberOfLeafJawPairs', 0)
                if num_pairs <= 0:
                    result["warnings"].append(
                        f"Beam Limiting Device Sequence item {j} in Beam {i}: "
                        f"Number of Leaf/Jaw Pairs ({num_pairs}) should be positive"
                    )
                
                # Validate Leaf Position Boundaries consistency
                leaf_boundaries = bld_item.get('LeafPositionBoundaries', [])
                if leaf_boundaries:
                    expected_length = num_pairs + 1
                    if len(leaf_boundaries) != expected_length:
                        result["errors"].append(
                            f"Beam Limiting Device Sequence item {j} in Beam {i}: "
                            f"Leaf Position Boundaries length ({len(leaf_boundaries)}) should be "
                            f"Number of Leaf/Jaw Pairs + 1 ({expected_length})"
                        )
                    
                    # Check that boundaries are in ascending order
                    if len(leaf_boundaries) > 1:
                        for k in range(1, len(leaf_boundaries)):
                            if leaf_boundaries[k] <= leaf_boundaries[k-1]:
                                result["warnings"].append(
                                    f"Beam Limiting Device Sequence item {j} in Beam {i}: "
                                    f"Leaf Position Boundaries should be in ascending order"
                                )
                                break

            # Validate Control Point Sequence
            control_point_seq = beam_item.get('ControlPointSequence', [])
            if not control_point_seq:
                result["errors"].append(
                    f"Beam Sequence item {i}: "
                    "Control Point Sequence (300A,0111) is required"
                )
            else:
                # Validate control point indices are sequential
                for j, cp_item in enumerate(control_point_seq):
                    cp_index = cp_item.get('ControlPointIndex')
                    if cp_index is None:
                        result["errors"].append(
                            f"Control Point Sequence item {j} in Beam {i}: "
                            "Control Point Index (300A,0112) is required"
                        )
                    elif cp_index != j:
                        result["errors"].append(
                            f"Control Point Sequence item {j} in Beam {i}: "
                            f"Control Point Index ({cp_index}) should be {j} (sequential)"
                        )

                    # Validate cumulative meterset weight
                    if cp_item.get('CumulativeMetersetWeight') is None:
                        result["errors"].append(
                            f"Control Point Sequence item {j} in Beam {i}: "
                            "Cumulative Meterset Weight (300A,0134) is required"
                        )

                # Validate final cumulative meterset weight consistency
                final_weight = beam_item.get('FinalCumulativeMetersetWeight')
                if final_weight is not None and control_point_seq:
                    last_cp_weight = control_point_seq[-1].get('CumulativeMetersetWeight')
                    if last_cp_weight is not None and abs(final_weight - last_cp_weight) > 0.001:
                        result["warnings"].append(
                            f"Beam Sequence item {i}: "
                            f"Final Cumulative Meterset Weight ({final_weight}) should match "
                            f"last control point weight ({last_cp_weight})"
                        )

        return result
