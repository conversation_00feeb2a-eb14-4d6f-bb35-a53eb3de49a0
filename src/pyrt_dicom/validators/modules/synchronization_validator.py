"""Synchronization Module DICOM validation - PS3.3 C.7.4.2"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig


class SynchronizationValidator:
    """Validator for DICOM Synchronization Module (PS3.3 C.7.4.2)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Synchronization Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate Type 1 requirements
        result = SynchronizationValidator._validate_required_elements(dataset, result)
        
        # Validate conditional requirements
        if config.validate_conditional_requirements:
            result = SynchronizationValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = SynchronizationValidator._validate_enumerated_values(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1 required elements."""
        
        # Synchronization Frame of Reference UID (0020,0200) - Type 1
        if not hasattr(dataset, 'SynchronizationFrameOfReferenceUID') or not dataset.SynchronizationFrameOfReferenceUID:
            result["errors"].append("Synchronization Frame of Reference UID (0020,0200) is required (Type 1)")
        
        # Synchronization Trigger (0018,106A) - Type 1
        if not hasattr(dataset, 'SynchronizationTrigger') or not dataset.SynchronizationTrigger:
            result["errors"].append("Synchronization Trigger (0018,106A) is required (Type 1)")
        
        # Acquisition Time Synchronized (0018,1800) - Type 1
        if not hasattr(dataset, 'AcquisitionTimeSynchronized') or not dataset.AcquisitionTimeSynchronized:
            result["errors"].append("Acquisition Time Synchronized (0018,1800) is required (Type 1)")
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1C and 2C conditional requirements."""
        
        # Synchronization Channel (0018,106C) - Type 1C
        # Required if synchronization channel or trigger is encoded in a waveform in this SOP Instance
        # Note: We cannot determine the condition without additional context about waveform presence
        # This would need to be validated at the IOD level where waveform sequences are known
        
        # For now, we'll just validate the format if present
        if hasattr(dataset, 'SynchronizationChannel'):
            sync_channel = dataset.SynchronizationChannel
            if not isinstance(sync_channel, (list, tuple)) or len(sync_channel) != 2:
                result["errors"].append(
                    "Synchronization Channel (0018,106C) must be specified as [M,C] pair with exactly 2 values"
                )
            elif not all(isinstance(x, int) for x in sync_channel):
                result["errors"].append(
                    "Synchronization Channel (0018,106C) values must be integers"
                )
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values against DICOM specifications."""
        
        # Synchronization Trigger (0018,106A) enumerated values
        sync_trigger = getattr(dataset, 'SynchronizationTrigger', '')
        if sync_trigger:
            BaseValidator.validate_enumerated_value(
                sync_trigger, 
                ["SOURCE", "EXTERNAL", "PASSTHRU", "NO TRIGGER"],
                "Synchronization Trigger (0018,106A)", 
                result
            )
        
        # Acquisition Time Synchronized (0018,1800) enumerated values
        time_sync = getattr(dataset, 'AcquisitionTimeSynchronized', '')
        if time_sync:
            BaseValidator.validate_enumerated_value(
                time_sync, 
                ["Y", "N"],
                "Acquisition Time Synchronized (0018,1800)", 
                result
            )
        
        # Time Distribution Protocol (0018,1802) enumerated values
        time_protocol = getattr(dataset, 'TimeDistributionProtocol', '')
        if time_protocol:
            BaseValidator.validate_enumerated_value(
                time_protocol, 
                ["NTP", "IRIG", "GPS", "SNTP", "PTP"],
                "Time Distribution Protocol (0018,1802)", 
                result
            )
        
        # NTP Source Address (0018,1803) format validation
        ntp_address = getattr(dataset, 'NTPSourceAddress', '')
        if ntp_address:
            # Basic validation for IPv4 (dotted decimal) or IPv6 (colon separated hex)
            if not SynchronizationValidator._is_valid_ip_address(ntp_address):
                result["warnings"].append(
                    f"NTP Source Address (0018,1803) '{ntp_address}' does not appear to be "
                    "a valid IPv4 (dotted decimal) or IPv6 (colon separated hex) address"
                )
        
        return result
    
    @staticmethod
    def _is_valid_ip_address(address: str) -> bool:
        """Basic validation for IP address format.
        
        Args:
            address: IP address string to validate
            
        Returns:
            bool: True if address appears to be valid IPv4 or IPv6 format
        """
        # Basic IPv4 validation (dotted decimal)
        if '.' in address:
            parts = address.split('.')
            if len(parts) == 4:
                try:
                    return all(0 <= int(part) <= 255 for part in parts)
                except ValueError:
                    return False
        
        # Basic IPv6 validation (colon separated hex)
        elif ':' in address:
            parts = address.split(':')
            if 2 <= len(parts) <= 8:  # IPv6 can have compressed notation
                try:
                    for part in parts:
                        if part:  # Allow empty parts for compressed notation
                            int(part, 16)  # Validate as hexadecimal
                    return True
                except ValueError:
                    return False
        
        return False
