"""Common Instance Reference Module DICOM validation - PS3.3 C.12.2"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig


class CommonInstanceReferenceValidator:
    """Validator for DICOM Common Instance Reference Module (PS3.3 C.12.2)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Common Instance Reference Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate sequence structures
        if config.validate_sequences:
            result = CommonInstanceReferenceValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate sequence structure requirements."""
        
        # Referenced Series Sequence - each item needs Series Instance UID and Referenced Instance Sequence
        ref_series_seq = getattr(dataset, 'ReferencedSeriesSequence', [])
        for i, item in enumerate(ref_series_seq):
            if not item.get('SeriesInstanceUID'):
                result["errors"].append(
                    f"Referenced Series Sequence item {i}: Series Instance UID (0020,000E) is required"
                )
            
            # Referenced Instance Sequence is Type 1 within Referenced Series Sequence
            if not item.get('ReferencedInstanceSequence'):
                result["errors"].append(
                    f"Referenced Series Sequence item {i}: Referenced Instance Sequence (0008,114A) is required"
                )
            else:
                # Validate each referenced instance
                ref_instance_seq = item.get('ReferencedInstanceSequence', [])
                for j, ref_item in enumerate(ref_instance_seq):
                    if not ref_item.get('ReferencedSOPClassUID'):
                        result["errors"].append(
                            f"Referenced Series Sequence item {i}, Referenced Instance Sequence item {j}: "
                            f"Referenced SOP Class UID (0008,1150) is required"
                        )
                    if not ref_item.get('ReferencedSOPInstanceUID'):
                        result["errors"].append(
                            f"Referenced Series Sequence item {i}, Referenced Instance Sequence item {j}: "
                            f"Referenced SOP Instance UID (0008,1155) is required"
                        )
        
        # Studies Containing Other Referenced Instances Sequence - each item needs Study Instance UID
        other_studies_seq = getattr(dataset, 'StudiesContainingOtherReferencedInstancesSequence', [])
        for i, item in enumerate(other_studies_seq):
            if not item.get('StudyInstanceUID'):
                result["errors"].append(
                    f"Studies Containing Other Referenced Instances Sequence item {i}: "
                    f"Study Instance UID (0020,000D) is required"
                )
            
            # If Series and Instance Reference Macro is present, validate it
            if 'ReferencedSeriesSequence' in item:
                ref_series_seq = item.get('ReferencedSeriesSequence', [])
                for j, series_item in enumerate(ref_series_seq):
                    if not series_item.get('SeriesInstanceUID'):
                        result["errors"].append(
                            f"Studies Containing Other Referenced Instances Sequence item {i}, "
                            f"Referenced Series Sequence item {j}: Series Instance UID (0020,000E) is required"
                        )
                    
                    if 'ReferencedInstanceSequence' in series_item:
                        ref_instance_seq = series_item.get('ReferencedInstanceSequence', [])
                        for k, ref_item in enumerate(ref_instance_seq):
                            if not ref_item.get('ReferencedSOPClassUID'):
                                result["errors"].append(
                                    f"Studies Containing Other Referenced Instances Sequence item {i}, "
                                    f"Referenced Series Sequence item {j}, Referenced Instance Sequence item {k}: "
                                    f"Referenced SOP Class UID (0008,1150) is required"
                                )
                            if not ref_item.get('ReferencedSOPInstanceUID'):
                                result["errors"].append(
                                    f"Studies Containing Other Referenced Instances Sequence item {i}, "
                                    f"Referenced Series Sequence item {j}, Referenced Instance Sequence item {k}: "
                                    f"Referenced SOP Instance UID (0008,1155) is required"
                                )
        
        return result
