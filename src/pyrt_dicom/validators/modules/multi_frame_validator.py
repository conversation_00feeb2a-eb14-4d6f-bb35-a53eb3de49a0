"""Multi-frame Module DICOM validation - PS3.3 C.7.6.6"""

from pydicom import Dataset
from pydicom.tag import BaseTag
from .base_validator import BaseValidator, ValidationConfig
from ...enums.image_enums import StereoPairsPresent


class MultiFrameValidator:
    """Validator for DICOM Multi-frame Module (PS3.3 C.7.6.6)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Multi-frame Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate Type 1 requirements
        result = MultiFrameValidator._validate_type1_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = MultiFrameValidator._validate_enumerated_values(dataset, result)
        
        # Validate frame increment pointer consistency
        result = MultiFrameValidator._validate_frame_increment_consistency(dataset, result)
        
        # Validate stereo pairs consistency
        result = MultiFrameValidator._validate_stereo_pairs_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_type1_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1 (required) attributes."""
        
        # Number of Frames (0028,0008) Type 1
        if not hasattr(dataset, 'NumberOfFrames'):
            result["errors"].append("Number of Frames (0028,0008) is required (Type 1)")
        else:
            if not isinstance(dataset.NumberOfFrames, int) or dataset.NumberOfFrames < 1:
                result["errors"].append("Number of Frames (0028,0008) must be a positive integer")
        
        # Frame Increment Pointer (0028,0009) Type 1
        if not hasattr(dataset, 'FrameIncrementPointer'):
            result["errors"].append("Frame Increment Pointer (0028,0009) is required (Type 1)")
        else:
            fip = dataset.FrameIncrementPointer
            # Handle both single tag and list of tags (pydicom behavior)
            if isinstance(fip, BaseTag):
                tag_list = [fip]
            elif isinstance(fip, (list, tuple)):
                if len(fip) == 0:
                    result["errors"].append(
                        "Frame Increment Pointer (0028,0009) must contain at least one DICOM tag"
                    )
                    return result
                tag_list = fip
            else:
                result["errors"].append(
                    "Frame Increment Pointer (0028,0009) must be a DICOM tag or list of DICOM tags"
                )
                return result
                
            # Validate tag format - now expecting BaseTag objects
            for i, tag in enumerate(tag_list):
                if not isinstance(tag, BaseTag):
                    result["errors"].append(
                        f"Frame Increment Pointer (0028,0009) value {i+1} must be a DICOM Tag object"
                    )
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values against DICOM standard."""
        
        # Stereo Pairs Present
        if hasattr(dataset, 'StereoPairsPresent'):
            valid_values = [e.value for e in StereoPairsPresent]
            if dataset.StereoPairsPresent not in valid_values:
                result["errors"].append(
                    f"Stereo Pairs Present (0022,0028) has invalid value '{dataset.StereoPairsPresent}'. "
                    f"Valid values: {valid_values}"
                )
        
        return result
    
    @staticmethod
    def _validate_frame_increment_consistency(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate frame increment pointer consistency."""
        
        if not hasattr(dataset, 'FrameIncrementPointer'):
            return result
        
        # Check if referenced attributes are present in dataset
        fip = dataset.FrameIncrementPointer
        # Handle both single tag and list of tags
        if isinstance(fip, (list, tuple)):
            tag_list = fip
        else:
            tag_list = [fip]
            
        for tag in tag_list:
            if isinstance(tag, BaseTag):
                attr_name = MultiFrameValidator._tag_to_attribute_name(tag)
                if attr_name and not hasattr(dataset, attr_name):
                    result["warnings"].append(
                        f"Frame Increment Pointer (0028,0009) references {tag} ({attr_name}) "
                        f"but this attribute is not present in the dataset"
                    )
        
        # Validate specific frame increment scenarios
        from pydicom.tag import Tag
        frame_time_tag = Tag(0x0018, 0x1063)
        frame_time_vector_tag = Tag(0x0018, 0x1065)
        
        # Check if both frame time tags are present (use the same format as properties)
        fip = dataset.FrameIncrementPointer
        if isinstance(fip, (list, tuple)):
            has_frame_time = frame_time_tag in fip
            has_frame_time_vector = frame_time_vector_tag in fip
        else:
            has_frame_time = fip == frame_time_tag
            has_frame_time_vector = fip == frame_time_vector_tag
        
        if has_frame_time and has_frame_time_vector:
            result["warnings"].append(
                "Frame Increment Pointer (0028,0009) references both Frame Time (0018,1063) "
                "and Frame Time Vector (0018,1065). Typically only one should be used."
            )
        
        return result
    
    @staticmethod
    def _validate_stereo_pairs_consistency(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate stereo pairs consistency."""
        
        if hasattr(dataset, 'StereoPairsPresent') and dataset.StereoPairsPresent == "YES":
            if hasattr(dataset, 'NumberOfFrames'):
                if dataset.NumberOfFrames % 2 != 0:
                    result["warnings"].append(
                        f"Stereo Pairs Present is YES but Number of Frames ({dataset.NumberOfFrames}) "
                        "is odd. Stereo pairs should come in even numbers (left/right pairs)."
                    )
                
                if dataset.NumberOfFrames < 2:
                    result["errors"].append(
                        "Stereo Pairs Present is YES but Number of Frames is less than 2. "
                        "At least one stereo pair (2 frames) is required."
                    )
        
        return result
    
    @staticmethod
    def _is_valid_dicom_tag(tag: str) -> bool:
        """Check if string is a valid DICOM tag format.
        
        Args:
            tag (str): Tag string to validate
            
        Returns:
            bool: True if valid DICOM tag format
        """
        if not isinstance(tag, str):
            return False
        
        # Check for format "GGGG,EEEE" or "GGGGEEEE"
        if ',' in tag:
            parts = tag.split(',')
            if len(parts) != 2:
                return False
            group, element = parts
        else:
            if len(tag) != 8:
                return False
            group, element = tag[:4], tag[4:]
        
        # Check if both parts are valid hex
        try:
            int(group, 16)
            int(element, 16)
            return len(group) == 4 and len(element) == 4
        except ValueError:
            return False
    
    @staticmethod
    def _tag_to_attribute_name(tag: BaseTag) -> str | None:
        """Convert DICOM tag to likely attribute name.
        
        Args:
            tag (BaseTag): DICOM tag object
            
        Returns:
            str | None: Attribute name if known, None otherwise
        """
        # Map of common tags to attribute names
        from pydicom.tag import Tag
        tag_map = {
            Tag(0x0018, 0x1063): "FrameTime",
            Tag(0x0018, 0x1065): "FrameTimeVector",
            Tag(0x5200, 0x9230): "PerFrameFunctionalGroupsSequence",
            Tag(0x0008, 0x0023): "ContentDate",
            Tag(0x0008, 0x0033): "ContentTime",
            Tag(0x0020, 0x0013): "InstanceNumber"
        }
        
        return tag_map.get(tag)
