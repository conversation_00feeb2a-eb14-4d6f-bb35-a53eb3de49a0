"""
Test StructureSetModule functionality.

StructureSetModule implements DICOM PS3.3 C.8.8.5 Structure Set Module.
Defines a set of areas of significance associated with Frame of Reference and images.
"""

from datetime import datetime
from pyrt_dicom.modules import StructureSetModule
from pyrt_dicom.enums.rt_enums import ROIGenerationAlgorithm


class TestStructureSetModule:
    """Test StructureSetModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Primary Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )
        
        assert structure_set.StructureSetLabel == "Primary Struct"
        assert structure_set.StructureSetDate == "20240101"
        assert structure_set.StructureSetTime == "120000"
    
    def test_required_elements_with_defaults(self):
        """Test creation with default/empty values for Type 2 elements."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="",  # Type 2 - can be empty
            structure_set_time=""   # Type 2 - can be empty
        )
        
        assert structure_set.StructureSetLabel == "Test Struct"
        assert structure_set.StructureSetDate == ""
        assert structure_set.StructureSetTime == ""
    
    def test_datetime_formatting(self):
        """Test date and time formatting from datetime objects."""
        test_datetime = datetime(2024, 1, 15, 14, 30, 45)
        
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date=test_datetime,
            structure_set_time=test_datetime
        )
        
        # Verify date and time were formatted correctly
        assert structure_set.StructureSetDate == "20240115"
        assert structure_set.StructureSetTime == "143045"
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        ).with_optional_elements(
            structure_set_name="Lung Cancer Structures",
            structure_set_description="Primary target and organs at risk",
            instance_number="1"
        )
        
        assert hasattr(structure_set, 'StructureSetName')
        assert hasattr(structure_set, 'StructureSetDescription')
        assert hasattr(structure_set, 'InstanceNumber')
        assert structure_set.StructureSetName == "Lung Cancer Structures"
        assert structure_set.StructureSetDescription == "Primary target and organs at risk"
        assert structure_set.InstanceNumber == "1"
    
    def test_with_frame_of_reference(self):
        """Test adding frame of reference information."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )
        
        structure_set.with_frame_of_reference(
            frame_of_reference_uid="*******.*******.9"
        )
        
        assert structure_set.has_frame_references
        assert hasattr(structure_set, 'ReferencedFrameOfReferenceSequence')
        assert len(structure_set.ReferencedFrameOfReferenceSequence) == 1
        assert structure_set.ReferencedFrameOfReferenceSequence[0].FrameOfReferenceUID == "*******.*******.9"
    
    def test_create_rt_referenced_study_item(self):
        """Test creation of RT referenced study item."""
        study_item = StructureSetModule.create_rt_referenced_study_item(
            referenced_sop_class_uid="1.2.840.10008.3.1.2.3.1",
            referenced_sop_instance_uid="*******.*******.10"
        )
        
        assert hasattr(study_item, 'ReferencedSOPClassUID')
        assert hasattr(study_item, 'ReferencedSOPInstanceUID')
        assert study_item.ReferencedSOPClassUID == "1.2.840.10008.3.1.2.3.1"
        assert study_item.ReferencedSOPInstanceUID == "*******.*******.10"
    
    def test_create_structure_set_roi_item_basic(self):
        """Test creation of basic structure set ROI item."""
        roi_item = StructureSetModule.create_structure_set_roi_item(
            roi_number=1,
            referenced_frame_of_reference_uid="*******.*******.9",
            roi_name="PTV",
            roi_generation_algorithm=ROIGenerationAlgorithm.MANUAL
        )
        
        assert hasattr(roi_item, 'ROINumber')
        assert hasattr(roi_item, 'ReferencedFrameOfReferenceUID')
        assert hasattr(roi_item, 'ROIName')
        assert hasattr(roi_item, 'ROIGenerationAlgorithm')
        assert roi_item.ROINumber == 1
        assert roi_item.ReferencedFrameOfReferenceUID == "*******.*******.9"
        assert roi_item.ROIName == "PTV"
        assert roi_item.ROIGenerationAlgorithm == "MANUAL"
    
    def test_with_roi_definitions(self):
        """Test adding ROI definitions."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )
        
        roi_item = StructureSetModule.create_structure_set_roi_item(
            roi_number=1,
            referenced_frame_of_reference_uid="*******.*******.9",
            roi_name="PTV",
            roi_generation_algorithm=ROIGenerationAlgorithm.MANUAL
        )
        
        structure_set.with_roi_definitions([roi_item])
        
        assert structure_set.has_roi_definitions
        assert structure_set.roi_count == 1
        assert hasattr(structure_set, 'StructureSetROISequence')
        assert len(structure_set.StructureSetROISequence) == 1
    
    def test_roi_count_property(self):
        """Test ROI count property."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )
        
        # Initially no ROIs
        assert structure_set.roi_count == 0
        assert not structure_set.has_roi_definitions
        
        # Add ROIs
        roi_items = [
            StructureSetModule.create_structure_set_roi_item(
                roi_number=1,
                referenced_frame_of_reference_uid="*******.*******.9",
                roi_name="PTV",
                roi_generation_algorithm=ROIGenerationAlgorithm.MANUAL
            ),
            StructureSetModule.create_structure_set_roi_item(
                roi_number=2,
                referenced_frame_of_reference_uid="*******.*******.9",
                roi_name="Heart",
                roi_generation_algorithm=ROIGenerationAlgorithm.MANUAL
            )
        ]
        
        structure_set.with_roi_definitions(roi_items)
        assert structure_set.roi_count == 2
        assert structure_set.has_roi_definitions
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )
        
        assert hasattr(structure_set, 'validate')
        assert callable(structure_set.validate)
        
        # Test validation result structure
        validation_result = structure_set.validate()
        assert validation_result is not None
        assert "errors" in validation_result
        assert "warnings" in validation_result
        assert isinstance(validation_result["errors"], list)
        assert isinstance(validation_result["warnings"], list)
    
    def test_create_contour_image_item(self):
        """Test creation of contour image item."""
        contour_image = StructureSetModule.create_contour_image_item(
            referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.2",
            referenced_sop_instance_uid="*******.*******.11"
        )
        
        assert hasattr(contour_image, 'ReferencedSOPClassUID')
        assert hasattr(contour_image, 'ReferencedSOPInstanceUID')
        assert contour_image.ReferencedSOPClassUID == "1.2.840.10008.5.1.4.1.1.2"
        assert contour_image.ReferencedSOPInstanceUID == "*******.*******.11"
    
    def test_create_rt_referenced_series_item(self):
        """Test creation of RT referenced series item."""
        contour_images = [
            StructureSetModule.create_contour_image_item(
                referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.2",
                referenced_sop_instance_uid="*******.*******.11"
            )
        ]
        
        series_item = StructureSetModule.create_rt_referenced_series_item(
            series_instance_uid="*******.*******.12",
            contour_image_sequence=contour_images
        )
        
        assert hasattr(series_item, 'SeriesInstanceUID')
        assert hasattr(series_item, 'ContourImageSequence')
        assert series_item.SeriesInstanceUID == "*******.*******.12"
        assert len(series_item.ContourImageSequence) == 1