"""
Test ImagePlaneModule (C - Conditional) functionality.

ImagePlaneModule implements DICOM PS3.3 C.7.6.2 Image Plane Module.
Required when GeneralImageModule is present for spatial positioning.
"""

import pytest
from pyrt_dicom.modules import ImagePlaneModule


class TestImagePlaneModule:
    """Test ImagePlaneModule (C - Conditional) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 1.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=3.0
        )
        
        assert plane.PixelSpacing == [1.0, 1.0]
        assert plane.ImageOrientationPatient == [1, 0, 0, 0, 1, 0]
        assert plane.ImagePositionPatient == [0.0, 0.0, 0.0]
        assert plane.SliceThickness == 3.0
    
    def test_pixel_spacing_validation(self):
        """Test pixel spacing validation for dose grids."""
        # Common RT dose pixel spacings
        pixel_spacings = [
            [1.0, 1.0],         # 1mm isotropic
            [2.0, 2.0],         # 2mm isotropic
            [2.5, 2.5],         # 2.5mm isotropic
            [1.0, 2.0],         # Anisotropic
            [0.5, 0.5],         # High resolution
            [5.0, 5.0]          # Low resolution
        ]
        
        for spacing in pixel_spacings:
            plane = ImagePlaneModule.from_required_elements(
                pixel_spacing=spacing,
                image_orientation_patient=[1, 0, 0, 0, 1, 0],
                image_position_patient=[0.0, 0.0, 0.0],
                slice_thickness=3.0
            )
            assert plane.PixelSpacing == spacing
    
    def test_image_orientation_patient_validation(self):
        """Test image orientation patient validation."""
        # Standard anatomical orientations
        orientations = [
            [1, 0, 0, 0, 1, 0],     # Axial (standard)
            [0, 1, 0, 0, 0, -1],    # Sagittal
            [1, 0, 0, 0, 0, -1],    # Coronal
            [-1, 0, 0, 0, 1, 0],    # Axial flipped
            [0, -1, 0, 0, 0, -1]    # Sagittal flipped
        ]
        
        for orientation in orientations:
            plane = ImagePlaneModule.from_required_elements(
                pixel_spacing=[2.0, 2.0],
                image_orientation_patient=orientation,
                image_position_patient=[0.0, 0.0, 0.0],
                slice_thickness=3.0
            )
            assert plane.ImageOrientationPatient == orientation
    
    def test_image_position_patient_validation(self):
        """Test image position patient validation."""
        # Various coordinate positions for dose grids
        positions = [
            [0.0, 0.0, 0.0],           # Origin
            [-100.0, -100.0, -50.0],   # Typical dose grid corner
            [150.0, 200.0, -100.0],    # Extended field
            [-200.0, -150.0, 100.0],   # Different quadrant
            [0.5, -0.5, 25.5]          # Sub-millimeter precision
        ]
        
        for position in positions:
            plane = ImagePlaneModule.from_required_elements(
                pixel_spacing=[2.0, 2.0],
                image_orientation_patient=[1, 0, 0, 0, 1, 0],
                image_position_patient=position,
                slice_thickness=3.0
            )
            assert plane.ImagePositionPatient == position
    
    def test_slice_thickness_validation(self):
        """Test slice thickness validation for dose grids."""
        # Common RT dose slice thicknesses
        thicknesses = [1.0, 2.0, 2.5, 3.0, 5.0, 10.0]
        
        for thickness in thicknesses:
            plane = ImagePlaneModule.from_required_elements(
                pixel_spacing=[2.0, 2.0],
                image_orientation_patient=[1, 0, 0, 0, 1, 0],
                image_position_patient=[0.0, 0.0, 0.0],
                slice_thickness=thickness
            )
            assert plane.SliceThickness == thickness
    
    def test_with_optional_elements(self):
        """Test adding optional plane elements."""
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[2.0, 2.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=3.0
        ).with_optional_elements(
            slice_location=0.0,
            spacing_between_slices=3.0,
        )
        
        assert hasattr(plane, 'SliceLocation')
        assert hasattr(plane, 'SpacingBetweenSlices')
    
    def test_slice_location_progression(self):
        """Test slice location progression for multi-slice dose."""
        slice_locations = [-15.0, -12.0, -9.0, -6.0, -3.0, 0.0, 3.0, 6.0, 9.0]
        
        for location in slice_locations:
            plane = ImagePlaneModule.from_required_elements(
                pixel_spacing=[2.0, 2.0],
                image_orientation_patient=[1, 0, 0, 0, 1, 0],
                image_position_patient=[0.0, 0.0, location],
                slice_thickness=3.0
            ).with_optional_elements(
                slice_location=location
            )
            assert plane.SliceLocation == location
    
    def test_spacing_between_slices(self):
        """Test spacing between slices validation."""
        # Spacing should match or exceed slice thickness
        spacing_values = [2.5, 3.0, 3.5, 5.0, 10.0]
        
        for spacing in spacing_values:
            plane = ImagePlaneModule.from_required_elements(
                pixel_spacing=[2.0, 2.0],
                image_orientation_patient=[1, 0, 0, 0, 1, 0],
                image_position_patient=[0.0, 0.0, 0.0],
                slice_thickness=2.5
            ).with_optional_elements(
                spacing_between_slices=spacing
            )
            assert plane.SpacingBetweenSlices == spacing
    
    def test_rt_dose_grid_specifications(self):
        """Test RT dose grid spatial specifications."""
        # Typical RT dose grid setup
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[2.5, 2.5],  # 2.5mm resolution
            image_orientation_patient=[1, 0, 0, 0, 1, 0],  # Axial
            image_position_patient=[-128.75, -128.75, -100.0],  # Grid corner
            slice_thickness=2.5
        ).with_optional_elements(
            slice_location=-100.0,
            spacing_between_slices=2.5
        )
        
        # Verify dose grid characteristics
        assert plane.PixelSpacing == [2.5, 2.5]
        assert plane.SliceThickness == 2.5
        assert plane.SpacingBetweenSlices == 2.5  # Contiguous slices
    
    def test_anisotropic_voxel_spacing(self):
        """Test anisotropic voxel spacing configurations."""
        # Non-isotropic voxel configurations
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[1.0, 2.0],  # Different x,y spacing
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=5.0  # Different z spacing
        )
        
        assert plane.PixelSpacing[0] != plane.PixelSpacing[1]
        assert plane.SliceThickness != plane.PixelSpacing[0]
    
    def test_coordinate_system_consistency(self):
        """Test coordinate system consistency for dose alignment."""
        # Ensure right-handed coordinate system
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[2.0, 2.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],  # Standard axial
            image_position_patient=[-100.0, -100.0, 0.0],
            slice_thickness=3.0
        )
        
        # Verify coordinate system properties
        orientation = plane.ImageOrientationPatient
        assert len(orientation) == 6  # Two 3D vectors
        
        # Row direction (first 3 elements)
        row_direction = orientation[:3]
        assert len(row_direction) == 3
        
        # Column direction (last 3 elements)
        col_direction = orientation[3:]
        assert len(col_direction) == 3
    
    def test_high_resolution_dose_grid(self):
        """Test high resolution dose grid specifications."""
        # High-resolution dose calculation
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[0.5, 0.5],  # Sub-millimeter resolution
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[-64.25, -64.25, -25.0],
            slice_thickness=1.0
        ).with_optional_elements(
            spacing_between_slices=1.0
        )
        
        # Verify high-resolution characteristics
        assert all(spacing < 1.0 for spacing in plane.PixelSpacing)
        assert plane.SliceThickness == 1.0
    
    def test_dependency_on_general_image_module(self):
        """Test that ImagePlaneModule depends on GeneralImageModule."""
        # This dependency should be validated at IOD level
        # Here we test that ImagePlaneModule can be created independently
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[2.0, 2.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=3.0
        )
        
        # Module should be valid on its own
        assert plane.PixelSpacing == [2.0, 2.0]
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        plane = ImagePlaneModule.from_required_elements(
            pixel_spacing=[2.0, 2.0],
            image_orientation_patient=[1, 0, 0, 0, 1, 0],
            image_position_patient=[0.0, 0.0, 0.0],
            slice_thickness=3.0
        )
        
        assert hasattr(plane, 'validate')
        assert callable(plane.validate)
        
        # Test validation returns expected structure
        result = plane.validate()
        assert result is not None
