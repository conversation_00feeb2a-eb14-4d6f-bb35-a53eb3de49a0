"""
Test ClinicalTrialSeriesModule functionality.

ClinicalTrialSeriesModule implements DICOM PS3.3 C.7.3.2 Clinical Trial Series Module.
All attributes are Type 3 (optional).
"""

from pyrt_dicom.modules.clinical_trial_series_module import ClinicalTrialSeriesModule


class TestClinicalTrialSeriesModule:
    """Test ClinicalTrialSeriesModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with no required elements (all Type 3)."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        
        # Module should be created successfully since all elements are optional
        assert trial_series is not None
        assert isinstance(trial_series, ClinicalTrialSeriesModule)
    
    def test_with_optional_elements_coordinating_center(self):
        """Test adding coordinating center information."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        trial_series.with_optional_elements(
            clinical_trial_coordinating_center_name="Research Center A"
        )
        
        assert hasattr(trial_series, 'ClinicalTrialCoordinatingCenterName')
        assert trial_series.ClinicalTrialCoordinatingCenterName == "Research Center A"
    
    def test_with_optional_elements_series_id(self):
        """Test adding series identification information."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        trial_series.with_optional_elements(
            clinical_trial_series_id="SERIES001",
            clinical_trial_series_description="Baseline CT imaging"
        )
        
        assert hasattr(trial_series, 'ClinicalTrialSeriesID')
        assert hasattr(trial_series, 'ClinicalTrialSeriesDescription')
        assert trial_series.ClinicalTrialSeriesID == "SERIES001"
        assert trial_series.ClinicalTrialSeriesDescription == "Baseline CT imaging"
    
    def test_with_optional_elements_issuer_info(self):
        """Test adding issuer information."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        trial_series.with_optional_elements(
            clinical_trial_series_id="SERIES001",
            issuer_of_clinical_trial_series_id="HOSPITAL_A"
        )
        
        assert hasattr(trial_series, 'ClinicalTrialSeriesID')
        assert hasattr(trial_series, 'IssuerOfClinicalTrialSeriesID')
        assert trial_series.ClinicalTrialSeriesID == "SERIES001"
        assert trial_series.IssuerOfClinicalTrialSeriesID == "HOSPITAL_A"
    
    def test_with_optional_elements_all_fields(self):
        """Test adding all optional elements."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        trial_series.with_optional_elements(
            clinical_trial_coordinating_center_name="National Research Center",
            clinical_trial_series_id="TRIAL_ABC_SERIES_001",
            issuer_of_clinical_trial_series_id="NRC_IMAGING",
            clinical_trial_series_description="Pre-treatment baseline imaging series"
        )
        
        assert trial_series.ClinicalTrialCoordinatingCenterName == "National Research Center"
        assert trial_series.ClinicalTrialSeriesID == "TRIAL_ABC_SERIES_001"
        assert trial_series.IssuerOfClinicalTrialSeriesID == "NRC_IMAGING"
        assert trial_series.ClinicalTrialSeriesDescription == "Pre-treatment baseline imaging series"
    
    def test_none_values_not_set(self):
        """Test that None values are not set as attributes."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        trial_series.with_optional_elements(
            clinical_trial_coordinating_center_name=None,
            clinical_trial_series_id="SERIES001",
            issuer_of_clinical_trial_series_id=None,
            clinical_trial_series_description=None
        )
        
        # Only non-None values should be set
        assert not hasattr(trial_series, 'ClinicalTrialCoordinatingCenterName')
        assert hasattr(trial_series, 'ClinicalTrialSeriesID')
        assert not hasattr(trial_series, 'IssuerOfClinicalTrialSeriesID')
        assert not hasattr(trial_series, 'ClinicalTrialSeriesDescription')
        assert trial_series.ClinicalTrialSeriesID == "SERIES001"
    
    def test_has_coordinating_center_info_property(self):
        """Test has_coordinating_center_info property."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        
        # Initially should be False
        assert not trial_series.has_coordinating_center_info
        
        # After adding coordinating center name
        trial_series.with_optional_elements(
            clinical_trial_coordinating_center_name="Test Center"
        )
        assert trial_series.has_coordinating_center_info
    
    def test_has_series_identification_property(self):
        """Test has_series_identification property."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        
        # Initially should be False
        assert not trial_series.has_series_identification
        
        # After adding series ID
        trial_series.with_optional_elements(clinical_trial_series_id="SERIES001")
        assert trial_series.has_series_identification
        
        # Reset and test with description only
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        trial_series.with_optional_elements(clinical_trial_series_description="Test description")
        assert trial_series.has_series_identification
    
    def test_has_issuer_info_property(self):
        """Test has_issuer_info property."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        
        # Initially should be False
        assert not trial_series.has_issuer_info
        
        # After adding issuer information
        trial_series.with_optional_elements(
            issuer_of_clinical_trial_series_id="TEST_ISSUER"
        )
        assert trial_series.has_issuer_info
    
    def test_method_chaining(self):
        """Test that with_optional_elements returns self for method chaining."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        
        result = trial_series.with_optional_elements(
            clinical_trial_coordinating_center_name="Test Center"
        )
        
        # Should return self
        assert result is trial_series
        assert trial_series.ClinicalTrialCoordinatingCenterName == "Test Center"
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        trial_series.with_optional_elements(
            clinical_trial_coordinating_center_name="Test Center",
            clinical_trial_series_id="SERIES001"
        )
        
        assert hasattr(trial_series, 'validate')
        assert callable(trial_series.validate)
        
        # Test validation result structure
        validation_result = trial_series.validate()
        assert validation_result is not None
        assert "errors" in validation_result
        assert "warnings" in validation_result
        assert isinstance(validation_result["errors"], list)
        assert isinstance(validation_result["warnings"], list)
    
    def test_empty_module_validation(self):
        """Test validation of empty module (all elements are optional)."""
        trial_series = ClinicalTrialSeriesModule.from_required_elements()
        
        # Empty module should validate successfully since all elements are optional
        validation_result = trial_series.validate()
        assert len(validation_result["errors"]) == 0