"""
Test ContrastBolusModule functionality.

ContrastBolusModule implements DICOM PS3.3 C.7.6.4 Contrast/Bolus Module.
Contains attributes that describe the contrast/bolus agent used in the acquisition.
"""

from pyrt_dicom.modules import ContrastBolusModule
from pyrt_dicom.enums.contrast_ct_enums import ContrastBolusIngredient


class TestContrastBolusModule:
    """Test ContrastBolusModule basic functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        )
        
        assert contrast_bolus.ContrastBolusAgent == "Iodinated contrast agent"
    
    def test_from_required_elements_empty_defaults(self):
        """Test creation with default empty values (Type 2 elements)."""
        contrast_bolus = ContrastBolusModule.from_required_elements()
        
        # Type 2 elements can be empty but must be present
        assert contrast_bolus.ContrastBolusAgent == ""
    
    def test_with_optional_elements_basic(self):
        """Test adding basic optional elements."""
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        ).with_optional_elements(
            contrast_bolus_route="INTRAVENOUS",
            contrast_bolus_volume=100.0,
            contrast_bolus_total_dose=50.0
        )
        
        assert contrast_bolus.ContrastBolusRoute == "INTRAVENOUS"
        assert contrast_bolus.ContrastBolusVolume == 100.0
        assert contrast_bolus.ContrastBolusTotalDose == 50.0
    
    def test_with_optional_elements_timing(self):
        """Test adding timing-related optional elements."""
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        ).with_optional_elements(
            contrast_bolus_start_time="120000",
            contrast_bolus_stop_time="120030"
        )
        
        assert contrast_bolus.ContrastBolusStartTime == "120000"
        assert contrast_bolus.ContrastBolusStopTime == "120030"
    
    def test_with_optional_elements_flow_data(self):
        """Test adding flow rate and duration data."""
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        ).with_optional_elements(
            contrast_flow_rate=[5.0, 3.0],
            contrast_flow_duration=[20.0, 10.0]
        )
        
        assert contrast_bolus.ContrastFlowRate == [5.0, 3.0]
        assert contrast_bolus.ContrastFlowDuration == [20.0, 10.0]
    
    def test_with_optional_elements_ingredient(self):
        """Test adding ingredient information with enum."""
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        ).with_optional_elements(
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE,
            contrast_bolus_ingredient_concentration=370.0
        )
        
        assert contrast_bolus.ContrastBolusIngredient == "IODINE"
        assert contrast_bolus.ContrastBolusIngredientConcentration == 370.0
    
    def test_create_contrast_agent_code_item(self):
        """Test creating contrast agent code item."""
        code_item = ContrastBolusModule.create_contrast_agent_code_item(
            code_value="C-B0322",
            coding_scheme_designator="SRT",
            code_meaning="Iodinated contrast agent"
        )
        
        assert code_item.CodeValue == "C-B0322"
        assert code_item.CodingSchemeDesignator == "SRT"
        assert code_item.CodeMeaning == "Iodinated contrast agent"
    
    def test_create_administration_route_code_item(self):
        """Test creating administration route code item."""
        code_item = ContrastBolusModule.create_administration_route_code_item(
            code_value="47625008",
            coding_scheme_designator="SCT",
            code_meaning="Intravenous route"
        )
        
        assert code_item.CodeValue == "47625008"
        assert code_item.CodingSchemeDesignator == "SCT"
        assert code_item.CodeMeaning == "Intravenous route"
    
    def test_create_additional_drug_code_item(self):
        """Test creating additional drug code item."""
        code_item = ContrastBolusModule.create_additional_drug_code_item(
            code_value="387467008",
            coding_scheme_designator="SCT",
            code_meaning="Saline"
        )
        
        assert code_item.CodeValue == "387467008"
        assert code_item.CodingSchemeDesignator == "SCT"
        assert code_item.CodeMeaning == "Saline"
    
    def test_property_has_contrast_agent(self):
        """Test has_contrast_agent property."""
        # With agent
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        )
        assert contrast_bolus.has_contrast_agent is True
        
        # Without agent (empty)
        contrast_bolus_empty = ContrastBolusModule.from_required_elements()
        assert contrast_bolus_empty.has_contrast_agent is False
    
    def test_property_has_timing_information(self):
        """Test has_timing_information property."""
        # Without timing info
        contrast_bolus = ContrastBolusModule.from_required_elements()
        assert contrast_bolus.has_timing_information is False
        
        # With start time
        contrast_bolus.with_optional_elements(contrast_bolus_start_time="120000")
        assert contrast_bolus.has_timing_information is True
    
    def test_property_has_volume_information(self):
        """Test has_volume_information property."""
        # Without volume info
        contrast_bolus = ContrastBolusModule.from_required_elements()
        assert contrast_bolus.has_volume_information is False
        
        # With volume
        contrast_bolus.with_optional_elements(contrast_bolus_volume=100.0)
        assert contrast_bolus.has_volume_information is True
    
    def test_property_has_ingredient_information(self):
        """Test has_ingredient_information property."""
        # Without ingredient info
        contrast_bolus = ContrastBolusModule.from_required_elements()
        assert contrast_bolus.has_ingredient_information is False
        
        # With ingredient
        contrast_bolus.with_optional_elements(
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE
        )
        assert contrast_bolus.has_ingredient_information is True
    
    def test_property_is_iodine_based(self):
        """Test is_iodine_based property."""
        contrast_bolus = ContrastBolusModule.from_required_elements().with_optional_elements(
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE
        )
        assert contrast_bolus.is_iodine_based is True
        
        contrast_bolus_gd = ContrastBolusModule.from_required_elements().with_optional_elements(
            contrast_bolus_ingredient=ContrastBolusIngredient.GADOLINIUM
        )
        assert contrast_bolus_gd.is_iodine_based is False
    
    def test_property_is_gadolinium_based(self):
        """Test is_gadolinium_based property."""
        contrast_bolus = ContrastBolusModule.from_required_elements().with_optional_elements(
            contrast_bolus_ingredient=ContrastBolusIngredient.GADOLINIUM
        )
        assert contrast_bolus.is_gadolinium_based is True
        
        contrast_bolus_iodine = ContrastBolusModule.from_required_elements().with_optional_elements(
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE
        )
        assert contrast_bolus_iodine.is_gadolinium_based is False
    
    def test_flow_rate_and_duration_counts(self):
        """Test flow rate and duration counting methods."""
        contrast_bolus = ContrastBolusModule.from_required_elements().with_optional_elements(
            contrast_flow_rate=[5.0, 3.0, 2.0],
            contrast_flow_duration=[20.0, 10.0, 15.0]
        )
        
        assert contrast_bolus.get_flow_rate_count() == 3
        assert contrast_bolus.get_flow_duration_count() == 3
        assert contrast_bolus.validate_flow_consistency() is True
    
    def test_flow_consistency_validation(self):
        """Test flow rate and duration consistency validation."""
        # Matching counts - should be valid
        contrast_bolus = ContrastBolusModule.from_required_elements().with_optional_elements(
            contrast_flow_rate=[5.0, 3.0],
            contrast_flow_duration=[20.0, 10.0]
        )
        assert contrast_bolus.validate_flow_consistency() is True
        
        # Only one present - should be valid (note: single values are stored as lists)
        contrast_bolus_single = ContrastBolusModule.from_required_elements().with_optional_elements(
            contrast_flow_rate=[5.0, 3.0]  # Use multiple values to ensure it's handled as a list
        )
        assert contrast_bolus_single.validate_flow_consistency() is True
    
    def test_validate_method_exists(self):
        """Test that validate method exists and returns expected structure."""
        contrast_bolus = ContrastBolusModule.from_required_elements()
        result = contrast_bolus.validate()
        
        assert isinstance(result, dict)
        assert 'errors' in result
        assert 'warnings' in result
        assert isinstance(result['errors'], list)
        assert isinstance(result['warnings'], list)