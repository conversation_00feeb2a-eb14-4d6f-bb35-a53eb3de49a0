"""
Test RTGeneralPlanModule functionality.

RTGeneralPlanModule implements DICOM PS3.3 C.8.8.9 RT General Plan Module.
Contains general information about the RT Plan.
"""

from pyrt_dicom.modules import RTGeneralPlanModule
from pyrt_dicom.enums import PlanIntent, RTplanGeometry, RTPlanRelationship


class TestRTGeneralPlanModule:
    """Test RTGeneralPlanModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Treatment Plan 1",
            rt_plan_date="20240101",
            rt_plan_time="120000",
            rt_plan_geometry=RTplanGeometry.PATIENT
        )
        
        assert plan.RTPlanLabel == "Treatment Plan 1"
        assert plan.RTPlanDate == "20240101"
        assert plan.RTPlanTime == "120000"
        assert plan.RTPlanGeometry == "PATIENT"
    
    def test_required_elements_with_defaults(self):
        """Test creation with default empty values for Type 2 elements."""
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Test Plan"
        )
        
        # Type 1 element should be set
        assert plan.RTPlanLabel == "Test Plan"
        
        # Type 2 elements can be empty
        assert plan.RTPlanDate == ""
        assert plan.RTPlanTime == ""
        assert plan.RTPlanGeometry == ""
    
    def test_geometry_enum_values(self):
        """Test RT Plan Geometry enum values."""
        # Test PATIENT geometry
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Patient Plan",
            rt_plan_geometry=RTplanGeometry.PATIENT
        )
        assert plan.RTPlanGeometry == "PATIENT"
        assert plan.is_patient_based is True
        
        # Test TREATMENT_DEVICE geometry
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Device Plan",
            rt_plan_geometry=RTplanGeometry.TREATMENT_DEVICE
        )
        assert plan.RTPlanGeometry == "TREATMENT_DEVICE"
        assert plan.is_patient_based is False
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Test Plan",
            rt_plan_geometry=RTplanGeometry.PATIENT
        ).with_optional_elements(
            rt_plan_name="Primary Treatment Plan",
            rt_plan_description="Curative treatment for lung cancer",
            instance_number="1",
            treatment_protocols="RTOG 0617",
            plan_intent=PlanIntent.CURATIVE,
            treatment_site="Lung"
        )
        
        assert hasattr(plan, 'RTPlanName')
        assert hasattr(plan, 'RTPlanDescription')
        assert hasattr(plan, 'InstanceNumber')
        assert hasattr(plan, 'TreatmentProtocols')
        assert hasattr(plan, 'PlanIntent')
        assert hasattr(plan, 'TreatmentSite')
        
        assert plan.RTPlanName == "Primary Treatment Plan"
        assert plan.RTPlanDescription == "Curative treatment for lung cancer"
        assert plan.InstanceNumber == "1"
        assert plan.TreatmentProtocols == "RTOG 0617"
        assert plan.PlanIntent == "CURATIVE"
        assert plan.TreatmentSite == "Lung"
    
    def test_plan_intent_enum_values(self):
        """Test all valid plan intent enum values."""
        intent_values = [
            PlanIntent.CURATIVE,
            PlanIntent.PALLIATIVE,
            PlanIntent.PROPHYLACTIC,
            PlanIntent.VERIFICATION
        ]
        
        for intent in intent_values:
            plan = RTGeneralPlanModule.from_required_elements(
                rt_plan_label="Test Plan"
            ).with_optional_elements(
                plan_intent=intent
            )
            assert plan.PlanIntent == intent.value
    
    def test_structure_set_reference(self):
        """Test structure set reference for patient-based plans."""
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Patient Plan",
            rt_plan_geometry=RTplanGeometry.PATIENT
        )
        
        # Add structure set reference
        plan.with_structure_set_reference(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9"
        )
        
        assert plan.has_structure_set_reference is True
        assert hasattr(plan, 'ReferencedStructureSetSequence')
        assert len(plan.ReferencedStructureSetSequence) == 1
        
        ref_item = plan.ReferencedStructureSetSequence[0]
        assert ref_item.ReferencedSOPClassUID == "1.2.840.10008.*******.1.481.3"
        assert ref_item.ReferencedSOPInstanceUID == "*******.*******.9"
    
    def test_treatment_site_code_creation(self):
        """Test treatment site code sequence item creation."""
        code_item = RTGeneralPlanModule.create_treatment_site_code_item(
            code_value="39607008",
            coding_scheme_designator="SCT",
            code_meaning="Lung structure"
        )
        
        assert code_item['CodeValue'] == "39607008"
        assert code_item['CodingSchemeDesignator'] == "SCT"
        assert code_item['CodeMeaning'] == "Lung structure"
    
    def test_referenced_rt_plan_creation(self):
        """Test referenced RT plan sequence item creation."""
        ref_plan = RTGeneralPlanModule.create_referenced_rt_plan_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.5",
            referenced_sop_instance_uid="*******.*******.10",
            rt_plan_relationship=RTPlanRelationship.PRIOR
        )
        
        assert ref_plan['ReferencedSOPClassUID'] == "1.2.840.10008.*******.1.481.5"
        assert ref_plan['ReferencedSOPInstanceUID'] == "*******.*******.10"
        assert ref_plan['RTPlanRelationship'] == "PRIOR"
    
    def test_property_methods(self):
        """Test utility property methods."""
        # Test patient-based plan
        patient_plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Patient Plan",
            rt_plan_geometry=RTplanGeometry.PATIENT
        )
        assert patient_plan.is_patient_based is True
        assert patient_plan.has_structure_set_reference is False
        assert patient_plan.has_treatment_site_info is False
        
        # Test treatment device plan
        device_plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Device Plan",
            rt_plan_geometry=RTplanGeometry.TREATMENT_DEVICE
        )
        assert device_plan.is_patient_based is False
        
        # Test with treatment site info
        plan_with_site = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Site Plan"
        ).with_optional_elements(
            treatment_site="Lung"
        )
        assert plan_with_site.has_treatment_site_info is True
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        plan = RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Test Plan",
            rt_plan_geometry=RTplanGeometry.PATIENT
        )
        
        assert hasattr(plan, 'validate')
        assert callable(plan.validate)
        
        # Test validation result structure
        validation_result = plan.validate()
        assert validation_result is not None
        assert "errors" in validation_result
        assert "warnings" in validation_result
        assert isinstance(validation_result["errors"], list)
        assert isinstance(validation_result["warnings"], list)
    
    def test_method_chaining(self):
        """Test that methods support chaining."""
        plan = (RTGeneralPlanModule.from_required_elements(
            rt_plan_label="Chained Plan",
            rt_plan_geometry=RTplanGeometry.PATIENT
        )
        .with_optional_elements(
            rt_plan_name="Chained Name",
            plan_intent=PlanIntent.CURATIVE
        )
        .with_structure_set_reference(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9"
        ))
        
        assert plan.RTPlanLabel == "Chained Plan"
        assert plan.RTPlanName == "Chained Name"
        assert plan.PlanIntent == "CURATIVE"
        assert plan.has_structure_set_reference is True