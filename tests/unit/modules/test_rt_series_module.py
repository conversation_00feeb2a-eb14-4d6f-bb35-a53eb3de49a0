"""
Test RTSeriesModule (M - Mandatory) functionality.

RTSeriesModule implements DICOM PS3.3 C.8.8.1 RT Series Module.
Required for all RTDoseIOD instances.
"""

import pytest
from pydicom.uid import generate_uid
from pyrt_dicom.modules import RTSeriesModule


class TestRTSeriesModule:
    """Test RTSeriesModule (M - Mandatory) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        series_uid = generate_uid()
        series = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=series_uid,
            series_number="1"
        )
        
        assert series.Modality == "RTDOSE"
        assert series.SeriesInstanceUID == series_uid
        assert series.SeriesNumber == "1"
    
    def test_modality_validation(self):
        """Test modality validation for RT Series."""
        series = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            series_number="1"
        )
        
        assert series.Modality == "RTDOSE"
    
    def test_series_number_validation(self):
        """Test series number validation."""
        series = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            series_number="123"
        )
        
        assert series.SeriesNumber == "123"
    
    def test_various_rt_modalities(self):
        """Test various RT modality values."""
        rt_modalities = ["RTDOSE", "RTPLAN", "RTSTRUCT", "RTIMAGE", "RTRECORD"]
        
        for modality in rt_modalities:
            series = RTSeriesModule.from_required_elements(
                modality=modality,
                series_instance_uid=generate_uid(),
                series_number="1"
            )
            assert series.Modality == modality
    
    def test_series_uid_uniqueness(self):
        """Test that series UIDs are unique."""
        series1 = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            series_number="1"
        )
        series2 = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            series_number="2"
        )
        
        assert series1.SeriesInstanceUID != series2.SeriesInstanceUID
    
    def test_with_optional_elements(self):
        """Test adding optional series information."""
        series = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            series_number="1"
        ).with_optional_elements(
            series_description="RT Dose Distribution",
            series_date="20240101",
            series_time="120000",
            operators_name="Physicist^Medical"
        )
        
        assert hasattr(series, 'SeriesDescription')
        assert hasattr(series, 'SeriesDate')
        assert hasattr(series, 'SeriesTime')
        assert hasattr(series, 'OperatorsName')
    
    def test_series_number_string_values(self):
        """Test various valid series number string values."""
        valid_series_numbers = ["1", "001", "123", "999"]
        
        for number in valid_series_numbers:
            series = RTSeriesModule.from_required_elements(
                modality="RTDOSE",
                series_instance_uid=generate_uid(),
                series_number=number
            )
            assert series.SeriesNumber == number
    
    def test_series_number_invalid_values_raise_error(self):
        """Test that non-numeric series number values raise ValueError."""
        import warnings
        invalid_series_numbers = ["A1", "DOSE"]
        
        warnings.simplefilter("always")
        with warnings.catch_warnings(record=True) as w:
            for number in invalid_series_numbers:
                with pytest.raises(ValueError):
                    RTSeriesModule.from_required_elements(
                        modality="RTDOSE",
                        series_instance_uid=generate_uid(),
                        series_number=number
                    )
            # Should have generated at least one warning
            assert len(w) > 0
            assert any("Invalid value for VR IS" in str(warning.message) for warning in w)
    
    def test_series_number_float_values_generate_warnings(self):
        """Test that float series number values generate warnings but work."""
        import warnings
        float_series_numbers = ["1.23", "1.00"]
        
        warnings.simplefilter("always")
        with warnings.catch_warnings(record=True) as w:
            for number in float_series_numbers:
                series = RTSeriesModule.from_required_elements(
                    modality="RTDOSE",
                    series_instance_uid=generate_uid(),
                    series_number=number
                )
                # Should create the series but generate warnings
                assert series.SeriesNumber is not None
                # Should have generated at least one warning
                assert len(w) > 0
                assert any("Invalid value for VR IS" in str(warning.message) for warning in w)
    
    def test_empty_series_number_allowed(self):
        """Test that empty series number is allowed (Type 2)."""
        series = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            series_number=""  # Empty but not None
        )
        
        assert series.SeriesNumber == ""
    
    def test_rt_dose_specific_modality(self):
        """Test RT Dose specific modality requirements."""
        series = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            series_number="1"
        )
        
        # Verify modality is correctly set for RT Dose
        assert series.Modality == "RTDOSE"
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        series = RTSeriesModule.from_required_elements(
            modality="RTDOSE",
            series_instance_uid=generate_uid(),
            series_number="1"
        )
        
        assert hasattr(series, 'validate')
        assert callable(series.validate)
        
        # Test validation returns expected structure
        result = series.validate()
        assert result is not None