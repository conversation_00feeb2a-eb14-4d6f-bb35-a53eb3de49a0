"""
Test EnhancedPatientOrientationModule functionality.

EnhancedPatientOrientationModule implements DICOM PS3.3 C.7.6.30 Enhanced Patient Orientation Module.
Describes patient orientation with respect to gravity and equipment using three sequence attributes.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.modules import EnhancedPatientOrientationModule


class TestEnhancedPatientOrientationModule:
    """Test EnhancedPatientOrientationModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT", 
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        
        assert hasattr(orientation, 'PatientOrientationCodeSequence')
        assert hasattr(orientation, 'PatientOrientationModifierCodeSequence')
        assert hasattr(orientation, 'PatientEquipmentRelationshipCodeSequence')
        assert len(orientation.PatientOrientationCodeSequence) == 1
        assert len(orientation.PatientOrientationModifierCodeSequence) == 1
        assert len(orientation.PatientEquipmentRelationshipCodeSequence) == 1
    
    def test_with_optional_elements_no_arguments(self):
        """Test with_optional_elements with no arguments (valid)."""
        orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT", 
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        
        # Should not raise any errors
        result = orientation.with_optional_elements()
        assert result is orientation  # Should return self
    
    def test_with_optional_elements_raises_error_with_arguments(self):
        """Test with_optional_elements raises error when arguments provided."""
        orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT", 
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        
        with pytest.raises(ValueError) as exc_info:
            orientation.with_optional_elements(invalid_param="value")
        
        assert "has no optional elements" in str(exc_info.value)
        assert "invalid_param" in str(exc_info.value)
    
    def test_create_code_sequence_item_required_only(self):
        """Test creating code sequence item with required attributes only."""
        item = EnhancedPatientOrientationModule.create_code_sequence_item(
            code_value="102538003",
            coding_scheme_designator="SCT",
            code_meaning="recumbent"
        )
        
        assert isinstance(item, Dataset)
        assert item.CodeValue == "102538003"
        assert item.CodingSchemeDesignator == "SCT"
        assert item.CodeMeaning == "recumbent"
    
    def test_create_code_sequence_item_with_optional(self):
        """Test creating code sequence item with optional attributes."""
        item = EnhancedPatientOrientationModule.create_code_sequence_item(
            code_value="102538003",
            coding_scheme_designator="SCT",
            code_meaning="recumbent",
            coding_scheme_version="2023-01",
            context_identifier="123",
            context_uid="1.2.3.4"
        )
        
        assert isinstance(item, Dataset)
        assert item.CodeValue == "102538003"
        assert item.CodingSchemeDesignator == "SCT"
        assert item.CodeMeaning == "recumbent"
        assert item.CodingSchemeVersion == "2023-01"
        assert item.ContextIdentifier == "123"
        assert item.ContextUID == "1.2.3.4"
    
    def test_has_orientation_data_property(self):
        """Test has_orientation_data property."""
        # Test with incomplete data
        incomplete_orientation = EnhancedPatientOrientationModule()
        assert not incomplete_orientation.has_orientation_data
        
        # Test with complete data
        complete_orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT", 
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        assert complete_orientation.has_orientation_data
    
    def test_is_recumbent_property(self):
        """Test is_recumbent property."""
        # Test with recumbent orientation
        recumbent_orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT", 
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        assert recumbent_orientation.is_recumbent
        
        # Test with different orientation
        other_orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="C86043",
                    coding_scheme_designator="NCIt", 
                    code_meaning="erect"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        assert not other_orientation.is_recumbent
    
    def test_is_erect_property(self):
        """Test is_erect property."""
        # Test with erect orientation
        erect_orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="C86043",
                    coding_scheme_designator="NCIt", 
                    code_meaning="erect"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        assert erect_orientation.is_erect
        
        # Test with recumbent orientation
        recumbent_orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT", 
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        assert not recumbent_orientation.is_erect
    
    def test_is_headfirst_property(self):
        """Test is_headfirst property."""
        # Test with headfirst orientation
        headfirst_orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT", 
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        assert headfirst_orientation.is_headfirst
        
        # Test with feet-first orientation
        feetfirst_orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT", 
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102541007", 
                    coding_scheme_designator="SCT",
                    code_meaning="feetfirst"
                )
            ]
        )
        assert not feetfirst_orientation.is_headfirst
    
    def test_validate_method(self):
        """Test validate method calls validator."""
        orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT", 
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        
        result = orientation.validate()
        assert isinstance(result, dict)
        assert 'errors' in result
        assert 'warnings' in result