"""
Test SpecimenModule functionality.

SpecimenModule implements DICOM PS3.3 C.7.6.22 Specimen Module.
Contains attributes that identify one or more Specimens being imaged.
"""

from pyrt_dicom.modules import SpecimenModule
from pyrt_dicom.enums import ContainerComponentMaterial


class TestSpecimenModule:
    """Test SpecimenModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        issuer_seq = [SpecimenModule.create_hierarchic_designator_item(
            local_namespace_entity_id="LAB_001"
        )]
        
        container_type_seq = [SpecimenModule.create_code_sequence_item(
            code_value="433466003",
            coding_scheme_designator="SCT",
            code_meaning="Microscope slide"
        )]
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc],
            issuer_of_container_identifier_sequence=issuer_seq,
            container_type_code_sequence=container_type_seq
        )
        
        assert specimen.ContainerIdentifier == "SLIDE_001"
        assert hasattr(specimen, 'SpecimenDescriptionSequence')
        assert hasattr(specimen, 'IssuerOfContainerIdentifierSequence')
        assert hasattr(specimen, 'ContainerTypeCodeSequence')
        assert len(specimen.SpecimenDescriptionSequence) == 1
    
    def test_required_elements_with_empty_type2(self):
        """Test creation with empty Type 2 elements."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc],
            issuer_of_container_identifier_sequence=None,  # Type 2 - can be empty
            container_type_code_sequence=None  # Type 2 - can be empty
        )
        
        assert specimen.ContainerIdentifier == "SLIDE_001"
        assert len(specimen.SpecimenDescriptionSequence) == 1
        assert specimen.IssuerOfContainerIdentifierSequence == []
        assert specimen.ContainerTypeCodeSequence == []
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        alt_container = SpecimenModule.create_alternate_container_item(
            container_identifier="ALT_SLIDE_001"
        )
        
        container_component = SpecimenModule.create_container_component_item(
            container_component_type_code_sequence=[
                SpecimenModule.create_code_sequence_item(
                    code_value="12345",
                    coding_scheme_designator="TEST",
                    code_meaning="Test Component"
                )
            ],
            container_component_material=ContainerComponentMaterial.GLASS
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        ).with_optional_elements(
            container_description="Standard microscope slide",
            alternate_container_identifier_sequence=[alt_container],
            container_component_sequence=[container_component]
        )
        
        assert hasattr(specimen, 'ContainerDescription')
        assert hasattr(specimen, 'AlternateContainerIdentifierSequence')
        assert hasattr(specimen, 'ContainerComponentSequence')
        assert specimen.ContainerDescription == "Standard microscope slide"
    
    def test_specimen_description_item_creation(self):
        """Test specimen description sequence item creation."""
        item = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10",
            specimen_short_description="Test specimen",
            specimen_detailed_description="Detailed description of test specimen"
        )
        
        assert item.SpecimenIdentifier == "SPEC_001"
        assert item.SpecimenUID == "*******.*******.9.10"
        assert item.SpecimenShortDescription == "Test specimen"
        assert item.SpecimenDetailedDescription == "Detailed description of test specimen"
        assert item.IssuerOfSpecimenIdentifierSequence == []
        assert item.SpecimenPreparationSequence == []
    
    def test_code_sequence_item_creation(self):
        """Test code sequence item creation."""
        item = SpecimenModule.create_code_sequence_item(
            code_value="433466003",
            coding_scheme_designator="SCT",
            code_meaning="Microscope slide",
            coding_scheme_version="2023-01"
        )
        
        assert item.CodeValue == "433466003"
        assert item.CodingSchemeDesignator == "SCT"
        assert item.CodeMeaning == "Microscope slide"
        assert item.CodingSchemeVersion == "2023-01"
    
    def test_hierarchic_designator_item_creation(self):
        """Test hierarchic designator item creation."""
        item = SpecimenModule.create_hierarchic_designator_item(
            local_namespace_entity_id="LAB_001",
            universal_entity_id="*******.5",
            universal_entity_id_type="ISO"
        )
        
        assert item.LocalNamespaceEntityID == "LAB_001"
        assert item.UniversalEntityID == "*******.5"
        assert item.UniversalEntityIDType == "ISO"
    
    def test_container_component_item_creation(self):
        """Test container component sequence item creation."""
        component_type = [SpecimenModule.create_code_sequence_item(
            code_value="12345",
            coding_scheme_designator="TEST",
            code_meaning="Test Component"
        )]
        
        item = SpecimenModule.create_container_component_item(
            container_component_type_code_sequence=component_type,
            manufacturer="TestCorp",
            container_component_material=ContainerComponentMaterial.GLASS,
            container_component_length=25.0,
            container_component_width=75.0
        )
        
        assert item.ContainerComponentTypeCodeSequence == component_type
        assert item.Manufacturer == "TestCorp"
        assert item.ContainerComponentMaterial == "GLASS"
        assert item.ContainerComponentLength == 25.0
        assert item.ContainerComponentWidth == 75.0
    
    def test_container_component_material_enum(self):
        """Test container component material enum handling."""
        component_type = [SpecimenModule.create_code_sequence_item(
            code_value="12345",
            coding_scheme_designator="TEST",
            code_meaning="Test Component"
        )]
        
        # Test with enum value
        item = SpecimenModule.create_container_component_item(
            container_component_type_code_sequence=component_type,
            container_component_material=ContainerComponentMaterial.PLASTIC
        )
        assert item.ContainerComponentMaterial == "PLASTIC"
        
        # Test with string value
        item = SpecimenModule.create_container_component_item(
            container_component_type_code_sequence=component_type,
            container_component_material="METAL"
        )
        assert item.ContainerComponentMaterial == "METAL"
    
    def test_properties(self):
        """Test specimen module properties."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        assert specimen.has_specimen_data is True
        assert specimen.specimen_count == 1
        assert specimen.has_multiple_specimens is False
        assert specimen.has_container_components is False
        assert specimen.has_alternate_identifiers is False
        
        # Test with multiple specimens
        specimen_desc2 = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_002",
            specimen_uid="*******.*******.9.11"
        )
        
        specimen.SpecimenDescriptionSequence.append(specimen_desc2)
        assert specimen.specimen_count == 2
        assert specimen.has_multiple_specimens is True
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        assert hasattr(specimen, 'validate')
        assert callable(specimen.validate)
        
        # Test validation result structure
        validation_result = specimen.validate()
        assert validation_result is not None
        assert "errors" in validation_result
        assert "warnings" in validation_result
        assert isinstance(validation_result["errors"], list)
        assert isinstance(validation_result["warnings"], list)