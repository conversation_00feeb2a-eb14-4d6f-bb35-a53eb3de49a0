"""
Test ApprovalModule (U - Optional) functionality.

ApprovalModule implements DICOM PS3.3 C.8.8.16 Approval Module.
Optional module for dose approval and authorization tracking.
"""

from datetime import datetime
from pyrt_dicom.modules import ApprovalModule
from pyrt_dicom.enums import ApprovalStatus


class TestApprovalModule:
    """Test ApprovalModule (U - Optional) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        )
        
        assert approval.ApprovalStatus == ApprovalStatus.APPROVED.value
    
    def test_approval_status_validation(self):
        """Test approval status enumeration validation."""
        for status in ApprovalStatus:
            approval = ApprovalModule.from_required_elements(
                approval_status=status
            )
            assert approval.ApprovalStatus == status.value
    
    def test_with_optional_elements(self):
        """Test adding optional approval elements."""
        current_time = datetime.now()
        review_date = current_time.strftime("%Y%m%d")
        review_time = current_time.strftime("%H%M%S.%f")[:-3]
        
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_optional_elements(
            review_date=review_date,
            review_time=review_time,
            reviewer_name="Dr. Smith^John^MD^^"
        )
        
        assert hasattr(approval, 'ReviewDate')
        assert hasattr(approval, 'ReviewTime')
        assert hasattr(approval, 'ReviewerName')
        # ApprovalComment, ApprovalDate, ApprovalTime removed - not valid DICOM tags
    
    def test_reviewer_name_formatting(self):
        """Test reviewer name DICOM Person Name formatting."""
        reviewer_names = [
            "Smith^John^MD^^",
            "Johnson^Mary^PhD^Dr.^",
            "Brown^Robert^^^III",
            "Wilson^Sarah^RO^^",
        ]
        
        for name in reviewer_names:
            approval = ApprovalModule.from_required_elements(
                approval_status=ApprovalStatus.APPROVED
            ).with_optional_elements(
                reviewer_name=name
            )
            assert approval.ReviewerName == name
    
    def test_approval_workflow_statuses(self):
        """Test different approval workflow statuses."""
        workflow_scenarios = [
            (ApprovalStatus.APPROVED, "Final approval for treatment"),
            (ApprovalStatus.REJECTED, "Dose distribution needs revision"),
            (ApprovalStatus.UNAPPROVED, "Not yet submitted for approval")
        ]
        
        for status, comment in workflow_scenarios:
            approval = ApprovalModule.from_required_elements(
                approval_status=status
            )
            
            assert approval.ApprovalStatus == status.value
    
    def test_review_date_time_validation(self):
        """Test review date and time validation."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_optional_elements(
            review_date="20240315",
            review_time="143022.123456"
        )
        
        assert approval.ReviewDate == "20240315"
        assert approval.ReviewTime == "143022.123456"
    
    def test_approval_authority_information(self):
        """Test approval authority and institutional information."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_optional_elements(
            reviewer_name="Wilson^James^MD^Dr.^"
        )
        
        assert approval.ReviewerName == "Wilson^James^MD^Dr.^"
    
    def test_dose_specific_approval_comments(self):
        """Test dose calculation specific approval comments."""
        dose_approval_comments = [
            "Dose calculation verified and approved",
            "DVH analysis confirms plan acceptability",
            "Isodose distribution meets clinical requirements",
            "Monte Carlo calculation approved for treatment",
            "Dose constraints satisfied for all OARs",
        ]
        
        for comment in dose_approval_comments:
            approval = ApprovalModule.from_required_elements(
                approval_status=ApprovalStatus.APPROVED
            )
    
    def test_rejection_with_feedback(self):
        """Test rejection status with detailed feedback."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.REJECTED
        ).with_optional_elements(
            reviewer_name="Thompson^Lisa^MD^Dr.^",
            review_date="20240320",
            review_time="091500"
        )
        
        assert approval.ApprovalStatus == ApprovalStatus.REJECTED.value
        assert approval.ReviewerName == "Thompson^Lisa^MD^Dr.^"
    
    def test_unapproved_workflow(self):
        """Test unapproved workflow status."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.UNAPPROVED
        ).with_optional_elements(
        )
        
        assert approval.ApprovalStatus == ApprovalStatus.UNAPPROVED.value
    
    def test_approval_sequence_tracking(self):
        """Test approval with review information."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_optional_elements(
            reviewer_name="Smith^John^MD^^",
            review_date="20240320",
            review_time="100000"
        )
        
        assert approval.ReviewerName == "Smith^John^MD^^"
    
    def test_approval_digital_signature(self):
        """Test basic approval tracking functionality."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_optional_elements(
            reviewer_name="System^Automated^Sig^^",
            review_date="20240320"
        )
        
        assert approval.ReviewerName == "System^Automated^Sig^^"
    
    def test_approval_version_tracking(self):
        """Test approval version and modification tracking."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_optional_elements(
            review_date="20240320",
            review_time="143000.000"
        )
        
    
    def test_qa_approval_workflow(self):
        """Test quality assurance approval workflow."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_optional_elements(
            reviewer_name="Davis^Michael^PhD^Prof.^",
        )
        
        assert approval.ReviewerName == "Davis^Michael^PhD^Prof.^"
    
    def test_unapproved_initial_state(self):
        """Test unapproved initial state for new dose calculations."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.UNAPPROVED
        )
        
        assert approval.ApprovalStatus == ApprovalStatus.UNAPPROVED.value
    
    def test_approval_audit_trail(self):
        """Test approval audit trail elements."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_optional_elements(
            review_date="20240322",
            reviewer_name="Smith^John^MD^^"
        )
        
        assert approval.ReviewDate == "20240322"
        assert approval.ReviewerName == "Smith^John^MD^^"
    
    def test_optional_module_behavior(self):
        """Test that ApprovalModule behaves as optional enhancement."""
        # Approval module should not be required for basic dose functionality
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.UNAPPROVED
        )
        
        # Module should function independently
        assert approval.ApprovalStatus == ApprovalStatus.UNAPPROVED.value
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        )
        
        assert hasattr(approval, 'validate')
        assert callable(approval.validate)
        
        # Test validation returns expected structure
        result = approval.validate()
        assert result is not None
    
    def test_approval_with_multiple_reviewers(self):
        """Test approval workflow with multiple reviewers."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_optional_elements(
            reviewer_name="Wilson^James^MD^Dr.^",
        )
        
        assert approval.ReviewerName == "Wilson^James^MD^Dr.^"
    
    def test_treatment_machine_approval(self):
        """Test approval specific to treatment machine compatibility."""
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        )
        
        assert approval.ApprovalStatus == ApprovalStatus.APPROVED.value
        
