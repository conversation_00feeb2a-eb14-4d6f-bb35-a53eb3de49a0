"""
Test SOPCommonModule (M - Mandatory) functionality.

SOPCommonModule implements DICOM PS3.3 C.12.1 SOP Common Module.
Required for all RTDoseIOD instances.
"""

import pytest
from datetime import datetime
from pydicom.uid import generate_uid
from pyrt_dicom.modules import SOPCommonModule


class TestSOPCommonModule:
    """Test SOPCommonModule (M - Mandatory) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        sop_instance_uid = generate_uid()
        sop_class_uid = "1.2.840.10008.*******.1.481.2"  # RT Dose Storage
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid=sop_class_uid,
            sop_instance_uid=sop_instance_uid
        )
        
        assert sop.SOPClassUID == sop_class_uid
        assert sop.SOPInstanceUID == sop_instance_uid
    
    def test_rt_dose_sop_class_uid(self):
        """Test RT Dose specific SOP Class UID."""
        rt_dose_sop_class = "1.2.840.10008.*******.1.481.2"
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid=rt_dose_sop_class,
            sop_instance_uid=generate_uid()
        )
        
        assert sop.SOPClassUID == rt_dose_sop_class
    
    def test_sop_instance_uid_uniqueness(self):
        """Test that SOP Instance UIDs are unique."""
        sop_class = "1.2.840.10008.*******.1.481.2"
        
        sop1 = SOPCommonModule.from_required_elements(
            sop_class_uid=sop_class,
            sop_instance_uid=generate_uid()
        )
        sop2 = SOPCommonModule.from_required_elements(
            sop_class_uid=sop_class,
            sop_instance_uid=generate_uid()
        )
        
        assert sop1.SOPInstanceUID != sop2.SOPInstanceUID
    
    def test_various_sop_class_uids(self):
        """Test various RT-related SOP Class UIDs."""
        rt_sop_classes = [
            "1.2.840.10008.*******.1.481.2",   # RT Dose Storage
            "1.2.840.10008.*******.1.481.5",   # RT Plan Storage
            "1.2.840.10008.*******.1.481.3",   # RT Structure Set Storage
            "1.2.840.10008.*******.1.481.4"    # RT Beams Treatment Record Storage
        ]
        
        for sop_class in rt_sop_classes:
            sop = SOPCommonModule.from_required_elements(
                sop_class_uid=sop_class,
                sop_instance_uid=generate_uid()
            )
            assert sop.SOPClassUID == sop_class
    
    def test_with_optional_elements(self):
        """Test adding optional SOP Common elements."""
        current_time = datetime.now()
        instance_creation_date = current_time.strftime("%Y%m%d")
        instance_creation_time = current_time.strftime("%H%M%S.%f")[:-3]  # Microseconds to milliseconds
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_specific_character_set(
            "ISO_IR 100"
        ).with_optional_elements(
            instance_creation_date=instance_creation_date,
            instance_creation_time=instance_creation_time,
            instance_creator_uid=generate_uid(),
            instance_number=1
        )
        
        assert hasattr(sop, 'SpecificCharacterSet')
        assert hasattr(sop, 'InstanceCreationDate')
        assert hasattr(sop, 'InstanceCreationTime')
        assert hasattr(sop, 'InstanceCreatorUID')
        assert hasattr(sop, 'InstanceNumber')
    
    def test_instance_creation_datetime(self):
        """Test instance creation date and time handling."""
        creation_date = "20240101"
        creation_time = "120000.123"
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            instance_creation_date=creation_date,
            instance_creation_time=creation_time
        )
        
        assert sop.InstanceCreationDate == creation_date
        assert sop.InstanceCreationTime == creation_time
    
    def test_specific_character_set_values(self):
        """Test various specific character set values."""
        character_sets = [
            "ISO_IR 100",    # Latin alphabet No. 1
            "ISO_IR 101",    # Latin alphabet No. 2
            "ISO_IR 109",    # Latin alphabet No. 3
            "ISO_IR 110",    # Latin alphabet No. 4
            "ISO_IR 144",    # Cyrillic
            "ISO_IR 127",    # Arabic
            "ISO_IR 126",    # Greek
            "ISO_IR 138",    # Hebrew
            "ISO_IR 148",    # Latin alphabet No. 5
            "ISO_IR 192"     # UTF-8
        ]
        
        for charset in character_sets:
            sop = SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.2",
                sop_instance_uid=generate_uid()
            ).with_specific_character_set(
                charset
            )
            assert sop.SpecificCharacterSet == charset
    
    def test_instance_number_formats(self):
        """Test various instance number formats."""
        # DICOM Instance Number (IS VR) must be valid integers
        instance_numbers = [1, 123, 999]
        
        for instance_num in instance_numbers:
            sop = SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.2",
                sop_instance_uid=generate_uid()
            ).with_optional_elements(
                instance_number=instance_num
            )
            assert sop.InstanceNumber == instance_num
    
    def test_instance_creator_uid_validation(self):
        """Test instance creator UID validation."""
        creator_uid = generate_uid()
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            instance_creator_uid=creator_uid
        )
        
        assert sop.InstanceCreatorUID == creator_uid
    
    def test_sop_authorization_elements(self):
        """Test SOP authorization related elements."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            sop_authorization_comment="Authorized for clinical use",
            authorization_equipment_certification_number="CERT123456"
        )
        
        assert hasattr(sop, 'SOPAuthorizationComment')
        assert hasattr(sop, 'AuthorizationEquipmentCertificationNumber')
    
    def test_timezone_offset_from_utc(self):
        """Test timezone offset from UTC handling."""
        timezone_offsets = ["+0000", "-0500", "+0900", "+0530", "-0800"]
        
        for offset in timezone_offsets:
            sop = SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.2",
                sop_instance_uid=generate_uid()
            ).with_optional_elements(
                timezone_offset_from_utc=offset
            )
            assert sop.TimezoneOffsetFromUTC == offset
    
    def test_digital_signatures_elements(self):
        """Test digital signature related elements."""
        # Digital signatures are not implemented in the current SOPCommonModule
        # This test verifies the module can be created and validated without these elements
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        )
        
        # Verify basic SOP Common elements are present
        assert hasattr(sop, 'SOPClassUID')
        assert hasattr(sop, 'SOPInstanceUID')
        
        # Validate the module
        result = sop.validate()
        assert result is not None
    
    def test_rt_dose_sop_requirements(self):
        """Test RT Dose specific SOP Common requirements."""
        rt_dose_sop_class = "1.2.840.10008.*******.1.481.2"
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid=rt_dose_sop_class,
            sop_instance_uid=generate_uid()
        )
        
        # Verify RT Dose specific SOP requirements
        assert sop.SOPClassUID == rt_dose_sop_class
        assert sop.SOPInstanceUID is not None
        assert len(sop.SOPInstanceUID) > 0
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        )
        
        assert hasattr(sop, 'validate')
        assert callable(sop.validate)
        
        # Test validation returns expected structure
        result = sop.validate()
        assert result is not None
    
    def test_sop_class_uid_validation(self):
        """Test SOP Class UID format validation."""
        # Test valid UID format
        valid_uid = "1.2.840.10008.*******.1.481.2"
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid=valid_uid,
            sop_instance_uid=generate_uid()
        )
        
        assert sop.SOPClassUID == valid_uid
    
    def test_sop_instance_uid_format(self):
        """Test SOP Instance UID format requirements."""
        # Generate UID and verify it follows DICOM UID rules
        instance_uid = generate_uid()
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=instance_uid
        )
        
        # Verify UID characteristics
        assert sop.SOPInstanceUID == instance_uid
        assert "." in sop.SOPInstanceUID  # UIDs contain dots
        assert len(sop.SOPInstanceUID) <= 64  # Max UID length