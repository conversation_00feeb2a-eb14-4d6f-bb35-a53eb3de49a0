"""
Test ModalityLutModule functionality.

ModalityLutModule implements DICOM PS3.3 C.11.1 Modality LUT Module.
Describes the Modality LUT transformation that converts manufacturer dependent
pixel values to pixel values which are meaningful for the application.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.modules import ModalityLutModule
from pyrt_dicom.enums.image_enums import ModalityLutType, RescaleType


class TestModalityLutModule:
    """Test ModalityLutModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with no required elements."""
        # ModalityLutModule has no Type 1 or Type 2 elements
        modality_lut = ModalityLutModule.from_required_elements()
        
        assert isinstance(modality_lut, ModalityLutModule)
        assert not modality_lut.is_configured  # No data added yet
    
    def test_with_optional_elements_empty(self):
        """Test that with_optional_elements rejects any arguments."""
        modality_lut = ModalityLutModule.from_required_elements()
        
        # Should work with no arguments
        result = modality_lut.with_optional_elements()
        assert result is modality_lut
        
        # Should raise error with any arguments
        with pytest.raises(ValueError, match="has no optional elements"):
            modality_lut.with_optional_elements(unexpected_param="value")
    
    def test_with_rescale_parameters_success(self):
        """Test successful creation with rescale parameters."""
        modality_lut = ModalityLutModule.from_required_elements().with_rescale_parameters(
            rescale_intercept=-1024.0,
            rescale_slope=1.0,
            rescale_type=RescaleType.HU
        )
        
        assert modality_lut.RescaleIntercept == "-1024.0"
        assert modality_lut.RescaleSlope == "1.0"
        assert modality_lut.RescaleType == "HU"
        assert modality_lut.has_rescale_parameters
        assert modality_lut.is_configured
    
    def test_with_rescale_parameters_string_enum(self):
        """Test rescale parameters with string enum value."""
        modality_lut = ModalityLutModule.from_required_elements().with_rescale_parameters(
            rescale_intercept=0.0,
            rescale_slope=2.5,
            rescale_type="US"  # String instead of enum
        )
        
        assert modality_lut.RescaleIntercept == "0.0"
        assert modality_lut.RescaleSlope == "2.5"
        assert modality_lut.RescaleType == "US"
        assert modality_lut.has_rescale_parameters
    
    def test_with_modality_lut_sequence_success(self):
        """Test successful creation with modality LUT sequence."""
        lut_item = ModalityLutModule.create_modality_lut_item(
            lut_descriptor=[256, 0, 8],
            modality_lut_type=ModalityLutType.HU,
            lut_data=[i for i in range(256)]  # Example LUT data
        )
        
        modality_lut = ModalityLutModule.from_required_elements().with_modality_lut_sequence(
            modality_lut_sequence=[lut_item]
        )
        
        assert hasattr(modality_lut, 'ModalityLUTSequence')
        assert len(modality_lut.ModalityLUTSequence) == 1
        assert modality_lut.has_modality_lut_sequence
        assert modality_lut.is_configured
    
    def test_modality_lut_sequence_single_item_only(self):
        """Test that modality LUT sequence must contain exactly one item."""
        lut_item = ModalityLutModule.create_modality_lut_item(
            lut_descriptor=[256, 0, 8],
            modality_lut_type=ModalityLutType.HU,
            lut_data=[0, 1, 2, 3]
        )
        
        modality_lut = ModalityLutModule.from_required_elements()
        
        # Should fail with empty list
        with pytest.raises(ValueError, match="exactly one item"):
            modality_lut.with_modality_lut_sequence(modality_lut_sequence=[])
        
        # Should fail with multiple items
        with pytest.raises(ValueError, match="exactly one item"):
            modality_lut.with_modality_lut_sequence(modality_lut_sequence=[lut_item, lut_item])
    
    def test_create_modality_lut_item_basic(self):
        """Test creating basic modality LUT item."""
        lut_item = ModalityLutModule.create_modality_lut_item(
            lut_descriptor=[4, 0, 8],
            modality_lut_type=ModalityLutType.HU,
            lut_data=[0, 256, 512, 768]
        )
        
        assert isinstance(lut_item, Dataset)
        assert lut_item.LUTDescriptor == [4, 0, 8]
        assert lut_item.ModalityLUTType == "HU"
        assert lut_item.LUTData == [0, 256, 512, 768]
        assert not hasattr(lut_item, 'LUTExplanation')
    
    def test_create_modality_lut_item_with_explanation(self):
        """Test creating modality LUT item with explanation."""
        lut_item = ModalityLutModule.create_modality_lut_item(
            lut_descriptor=[4, 0, 8],
            modality_lut_type=ModalityLutType.HU,
            lut_data=[0, 256, 512, 768],
            lut_explanation="Test Hounsfield conversion"
        )
        
        assert lut_item.LUTExplanation == "Test Hounsfield conversion"
    
    def test_create_modality_lut_item_string_type(self):
        """Test creating modality LUT item with string type."""
        lut_item = ModalityLutModule.create_modality_lut_item(
            lut_descriptor=[4, 0, 8],
            modality_lut_type="US",  # String instead of enum
            lut_data=[0, 1, 2, 3]
        )
        
        assert lut_item.ModalityLUTType == "US"
    
    def test_rescale_type_enum_values(self):
        """Test different rescale type enum values."""
        rescale_types = [
            RescaleType.HU,
            RescaleType.OD,
            RescaleType.US,
            RescaleType.MGML,
            RescaleType.Z_EFF,
            RescaleType.ED,
            RescaleType.EDW,
            RescaleType.HU_MOD,
            RescaleType.PCT
        ]
        
        for rescale_type in rescale_types:
            modality_lut = ModalityLutModule.from_required_elements().with_rescale_parameters(
                rescale_intercept=0.0,
                rescale_slope=1.0,
                rescale_type=rescale_type
            )
            assert modality_lut.RescaleType == rescale_type.value
    
    def test_modality_lut_type_enum_values(self):
        """Test different modality LUT type enum values."""
        modality_lut_types = [
            ModalityLutType.HU,
            ModalityLutType.OD,
            ModalityLutType.US,
            ModalityLutType.MGML,
            ModalityLutType.Z_EFF,
            ModalityLutType.ED,
            ModalityLutType.EDW,
            ModalityLutType.HU_MOD,
            ModalityLutType.PCT
        ]
        
        for modality_lut_type in modality_lut_types:
            lut_item = ModalityLutModule.create_modality_lut_item(
                lut_descriptor=[4, 0, 8],
                modality_lut_type=modality_lut_type,
                lut_data=[0, 1, 2, 3]
            )
            assert lut_item.ModalityLUTType == modality_lut_type.value
    
    def test_property_checks(self):
        """Test property check methods."""
        # Empty module
        modality_lut = ModalityLutModule.from_required_elements()
        assert not modality_lut.has_rescale_parameters
        assert not modality_lut.has_modality_lut_sequence
        assert not modality_lut.is_configured
        
        # With rescale parameters
        modality_lut_rescale = ModalityLutModule.from_required_elements().with_rescale_parameters(
            rescale_intercept=0.0,
            rescale_slope=1.0,
            rescale_type=RescaleType.HU
        )
        assert modality_lut_rescale.has_rescale_parameters
        assert not modality_lut_rescale.has_modality_lut_sequence
        assert modality_lut_rescale.is_configured
        
        # With LUT sequence
        lut_item = ModalityLutModule.create_modality_lut_item(
            lut_descriptor=[4, 0, 8],
            modality_lut_type=ModalityLutType.HU,
            lut_data=[0, 1, 2, 3]
        )
        modality_lut_sequence = ModalityLutModule.from_required_elements().with_modality_lut_sequence(
            modality_lut_sequence=[lut_item]
        )
        assert not modality_lut_sequence.has_rescale_parameters
        assert modality_lut_sequence.has_modality_lut_sequence
        assert modality_lut_sequence.is_configured
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        modality_lut = ModalityLutModule.from_required_elements()
        
        assert hasattr(modality_lut, 'validate')
        assert callable(modality_lut.validate)
        
        # Test validation returns expected structure
        result = modality_lut.validate()
        assert result is not None
        assert isinstance(result, dict)
    
    def test_mutually_exclusive_configuration(self):
        """Test that LUT sequence and rescale parameters are mutually exclusive."""
        # Module should be configured with either LUT sequence OR rescale parameters
        # This is a business rule that should be validated, but basic creation should work
        
        lut_item = ModalityLutModule.create_modality_lut_item(
            lut_descriptor=[4, 0, 8],
            modality_lut_type=ModalityLutType.HU,
            lut_data=[0, 1, 2, 3]
        )
        
        # Create module with both (should be detected during validation)
        modality_lut = ModalityLutModule.from_required_elements()
        modality_lut.with_rescale_parameters(
            rescale_intercept=0.0,
            rescale_slope=1.0,
            rescale_type=RescaleType.HU
        )
        modality_lut.with_modality_lut_sequence(modality_lut_sequence=[lut_item])
        
        # Both should be present (validation should catch this conflict)
        assert modality_lut.has_rescale_parameters
        assert modality_lut.has_modality_lut_sequence
        assert modality_lut.is_configured