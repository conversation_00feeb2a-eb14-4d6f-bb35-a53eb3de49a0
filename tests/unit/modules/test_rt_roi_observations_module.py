"""
Test RTROIObservationsModule functionality.

RTROIObservationsModule implements DICOM PS3.3 C.8.8.8 RT ROI Observations Module.
Specifies identification and interpretation of ROIs.
"""

from datetime import datetime
from pyrt_dicom.modules import RTROIObservationsModule
from pyrt_dicom.enums.rt_enums import RTROIInterpretedType, RTROIRelationship, ROIPhysicalProperty


class TestRTROIObservationsModule:
    """Test RTROIObservationsModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with no required elements."""
        # This module has no required elements at the top level
        roi_obs = RTROIObservationsModule.from_required_elements()
        
        # Should be able to create without any arguments
        assert roi_obs is not None
        assert not hasattr(roi_obs, 'RTROIObservationsSequence')
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        roi_obs = RTROIObservationsModule.from_required_elements()
        
        # Create a simple observation item
        obs_item = RTROIObservationsModule.create_rt_roi_observations_item(
            observation_number=1,
            referenced_roi_number=1,
            rt_roi_interpreted_type=RTROIInterpretedType.PTV,
            roi_interpreter="Dr. Smith"
        )
        
        roi_obs = roi_obs.with_optional_elements(
            rt_roi_observations_sequence=[obs_item]
        )
        
        assert hasattr(roi_obs, 'RTROIObservationsSequence')
        assert len(roi_obs.RTROIObservationsSequence) == 1
        assert roi_obs.RTROIObservationsSequence[0].ObservationNumber == 1
        assert roi_obs.RTROIObservationsSequence[0].ReferencedROINumber == 1
        assert roi_obs.RTROIObservationsSequence[0].RTROIInterpretedType == "PTV"
        assert roi_obs.RTROIObservationsSequence[0].ROIInterpreter == "Dr. Smith"
    
    def test_create_rt_roi_observations_item(self):
        """Test creating RT ROI observations sequence item."""
        obs_item = RTROIObservationsModule.create_rt_roi_observations_item(
            observation_number=1,
            referenced_roi_number=5,
            rt_roi_interpreted_type=RTROIInterpretedType.OAR,
            roi_interpreter="Dr. Jones"
        )
        
        assert obs_item.ObservationNumber == 1
        assert obs_item.ReferencedROINumber == 5
        assert obs_item.RTROIInterpretedType == "OAR"
        assert obs_item.ROIInterpreter == "Dr. Jones"
    
    def test_create_rt_roi_observations_item_with_datetime(self):
        """Test creating ROI observations item with datetime."""
        test_datetime = datetime(2023, 5, 15, 10, 30, 45)
        
        obs_item = RTROIObservationsModule.create_rt_roi_observations_item(
            observation_number=2,
            referenced_roi_number=3,
            rt_roi_interpreted_type=RTROIInterpretedType.CTV,
            roi_interpreter="Dr. Brown",
            roi_observation_datetime=test_datetime
        )
        
        assert obs_item.ROIObservationDateTime == "20230515103045"
    
    def test_create_rt_roi_observations_item_with_string_datetime(self):
        """Test creating ROI observations item with string datetime."""
        obs_item = RTROIObservationsModule.create_rt_roi_observations_item(
            observation_number=3,
            referenced_roi_number=4,
            rt_roi_interpreted_type="GTV",
            roi_interpreter="Dr. Wilson",
            roi_observation_datetime="20230515103045"
        )
        
        assert obs_item.ROIObservationDateTime == "20230515103045"
    
    def test_create_rt_related_roi_item(self):
        """Test creating RT related ROI sequence item."""
        related_item = RTROIObservationsModule.create_rt_related_roi_item(
            referenced_roi_number=10,
            rt_roi_relationship=RTROIRelationship.ENCLOSED
        )
        
        assert related_item.ReferencedROINumber == 10
        assert related_item.RTROIRelationship == "ENCLOSED"
    
    def test_create_roi_physical_properties_item(self):
        """Test creating ROI physical properties sequence item."""
        prop_item = RTROIObservationsModule.create_roi_physical_properties_item(
            roi_physical_property=ROIPhysicalProperty.REL_MASS_DENSITY,
            roi_physical_property_value=1.05
        )
        
        assert prop_item.ROIPhysicalProperty == "REL_MASS_DENSITY"
        assert prop_item.ROIPhysicalPropertyValue == 1.05
    
    def test_create_roi_physical_properties_item_elem_fraction(self):
        """Test creating ROI physical properties item with elemental composition."""
        elem_comp = [RTROIObservationsModule.create_roi_elemental_composition_item(6, 0.2)]
        
        prop_item = RTROIObservationsModule.create_roi_physical_properties_item(
            roi_physical_property=ROIPhysicalProperty.ELEM_FRACTION,
            roi_physical_property_value=1.0,
            roi_elemental_composition_sequence=elem_comp
        )
        
        assert prop_item.ROIPhysicalProperty == "ELEM_FRACTION"
        assert hasattr(prop_item, 'ROIElementalCompositionSequence')
    
    def test_create_roi_elemental_composition_item(self):
        """Test creating ROI elemental composition sequence item."""
        elem_item = RTROIObservationsModule.create_roi_elemental_composition_item(
            roi_elemental_composition_atomic_number=6,
            roi_elemental_composition_atomic_mass_fraction=0.18
        )
        
        assert elem_item.ROIElementalCompositionAtomicNumber == 6
        assert elem_item.ROIElementalCompositionAtomicMassFraction == 0.18
    
    def test_create_related_rt_roi_observations_item(self):
        """Test creating related RT ROI observations sequence item."""
        related_obs_item = RTROIObservationsModule.create_related_rt_roi_observations_item(
            observation_number=5
        )
        
        assert related_obs_item.ObservationNumber == 5
    
    def test_create_roi_interpreter_item(self):
        """Test creating ROI interpreter sequence item."""
        interpreter_item = RTROIObservationsModule.create_roi_interpreter_item(
            roi_interpreter="Dr. Garcia",
            roi_interpreter_role="Senior Radiation Oncologist"
        )
        
        assert interpreter_item.ROIInterpreter == "Dr. Garcia"
        assert interpreter_item.ROIInterpreterRole == "Senior Radiation Oncologist"
    
    def test_create_code_sequence_item(self):
        """Test creating code sequence item."""
        code_item = RTROIObservationsModule.create_code_sequence_item(
            code_value="123456",
            coding_scheme_designator="SNM3",
            code_meaning="Lung"
        )
        
        assert code_item.CodeValue == "123456"
        assert code_item.CodingSchemeDesignator == "SNM3"
        assert code_item.CodeMeaning == "Lung"
    
    def test_has_observations_property(self):
        """Test has_observations property."""
        roi_obs = RTROIObservationsModule.from_required_elements()
        
        # Initially should not have observations
        assert not roi_obs.has_observations
        
        # Add observations
        obs_item = RTROIObservationsModule.create_rt_roi_observations_item(
            observation_number=1,
            referenced_roi_number=1,
            rt_roi_interpreted_type=RTROIInterpretedType.PTV,
            roi_interpreter="Dr. Test"
        )
        
        roi_obs.with_optional_elements(rt_roi_observations_sequence=[obs_item])
        
        # Now should have observations
        assert roi_obs.has_observations
    
    def test_observation_count_property(self):
        """Test observation_count property."""
        roi_obs = RTROIObservationsModule.from_required_elements()
        
        # Initially should be 0
        assert roi_obs.observation_count == 0
        
        # Add multiple observations
        obs_items = [
            RTROIObservationsModule.create_rt_roi_observations_item(
                observation_number=i,
                referenced_roi_number=i,
                rt_roi_interpreted_type=RTROIInterpretedType.PTV,
                roi_interpreter=f"Dr. Test {i}"
            ) for i in range(1, 4)
        ]
        
        roi_obs.with_optional_elements(rt_roi_observations_sequence=obs_items)
        
        # Should count 3 observations
        assert roi_obs.observation_count == 3
    
    def test_get_interpreted_types(self):
        """Test get_interpreted_types method."""
        roi_obs = RTROIObservationsModule.from_required_elements()
        
        # Initially should be empty
        assert roi_obs.get_interpreted_types() == []
        
        # Add observations with different types
        obs_items = [
            RTROIObservationsModule.create_rt_roi_observations_item(
                observation_number=1,
                referenced_roi_number=1,
                rt_roi_interpreted_type=RTROIInterpretedType.PTV,
                roi_interpreter="Dr. Test1"
            ),
            RTROIObservationsModule.create_rt_roi_observations_item(
                observation_number=2,
                referenced_roi_number=2,
                rt_roi_interpreted_type=RTROIInterpretedType.OAR,
                roi_interpreter="Dr. Test2"
            ),
            RTROIObservationsModule.create_rt_roi_observations_item(
                observation_number=3,
                referenced_roi_number=3,
                rt_roi_interpreted_type=RTROIInterpretedType.PTV,  # Duplicate
                roi_interpreter="Dr. Test3"
            )
        ]
        
        roi_obs.with_optional_elements(rt_roi_observations_sequence=obs_items)
        
        interpreted_types = roi_obs.get_interpreted_types()
        assert len(interpreted_types) == 2  # Should be unique
        assert "PTV" in interpreted_types
        assert "OAR" in interpreted_types
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        roi_obs = RTROIObservationsModule.from_required_elements()
        
        assert hasattr(roi_obs, 'validate')
        assert callable(roi_obs.validate)
        
        # Test validation result structure
        validation_result = roi_obs.validate()
        assert validation_result is not None
        assert "errors" in validation_result
        assert "warnings" in validation_result
        assert isinstance(validation_result["errors"], list)
        assert isinstance(validation_result["warnings"], list)