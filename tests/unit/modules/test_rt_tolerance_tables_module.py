"""
Test RTToleranceTablesModule functionality.

RTToleranceTablesModule implements DICOM PS3.3 C.8.8.11 RT Tolerance Tables Module.
Contains tolerance tables for comparing planned with delivered machine parameters.
"""

from pydicom import Dataset
from pyrt_dicom.modules import RTToleranceTablesModule
from pyrt_dicom.enums import RTBeamLimitingDeviceType


class TestRTToleranceTablesModule:
    """Test RTToleranceTablesModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements (none for this module)."""
        tolerance_tables = RTToleranceTablesModule.from_required_elements()
        
        # Module should be created successfully with no required elements
        assert tolerance_tables is not None
        assert isinstance(tolerance_tables, RTToleranceTablesModule)
    
    def test_with_optional_elements_basic(self):
        """Test adding basic optional elements."""
        tolerance_tables = RTToleranceTablesModule.from_required_elements()
        
        # Test adding empty tolerance table sequence
        tolerance_tables.with_optional_elements(
            tolerance_table_sequence=[]
        )
        
        assert hasattr(tolerance_tables, 'ToleranceTableSequence')
        assert tolerance_tables.ToleranceTableSequence == []
    
    def test_create_tolerance_table_item_basic(self):
        """Test creating basic tolerance table item."""
        table_item = RTToleranceTablesModule.create_tolerance_table_item(
            tolerance_table_number=1
        )
        
        assert isinstance(table_item, Dataset)
        assert table_item.ToleranceTableNumber == 1
    
    def test_create_tolerance_table_item_with_optional_fields(self):
        """Test creating tolerance table item with optional fields."""
        table_item = RTToleranceTablesModule.create_tolerance_table_item(
            tolerance_table_number=1,
            tolerance_table_label="Test Tolerances",
            gantry_angle_tolerance=1.0,
            patient_support_angle_tolerance=2.0
        )
        
        assert table_item.ToleranceTableNumber == 1
        assert table_item.ToleranceTableLabel == "Test Tolerances"
        assert table_item.GantryAngleTolerance == 1.0
        assert table_item.PatientSupportAngleTolerance == 2.0
    
    def test_create_beam_limiting_device_tolerance_item(self):
        """Test creating beam limiting device tolerance item."""
        # Test with enum
        bld_item = RTToleranceTablesModule.create_beam_limiting_device_tolerance_item(
            rt_beam_limiting_device_type=RTBeamLimitingDeviceType.X,
            beam_limiting_device_position_tolerance=2.0
        )
        
        assert isinstance(bld_item, Dataset)
        assert bld_item.RTBeamLimitingDeviceType == "X"
        assert bld_item.BeamLimitingDevicePositionTolerance == 2.0
        
        # Test with string
        bld_item_str = RTToleranceTablesModule.create_beam_limiting_device_tolerance_item(
            rt_beam_limiting_device_type="Y",
            beam_limiting_device_position_tolerance=1.5
        )
        
        assert isinstance(bld_item_str, Dataset)
        assert bld_item_str.RTBeamLimitingDeviceType == "Y"
        assert bld_item_str.BeamLimitingDevicePositionTolerance == 1.5
    
    def test_has_tolerance_tables_property(self):
        """Test has_tolerance_tables property."""
        tolerance_tables = RTToleranceTablesModule.from_required_elements()
        
        # Initially should not have tolerance tables
        assert not tolerance_tables.has_tolerance_tables
        
        # Add tolerance table sequence
        tolerance_tables.with_optional_elements(
            tolerance_table_sequence=[
                RTToleranceTablesModule.create_tolerance_table_item(
                    tolerance_table_number=1
                )
            ]
        )
        
        # Now should have tolerance tables
        assert tolerance_tables.has_tolerance_tables
    
    def test_tolerance_table_count_property(self):
        """Test tolerance_table_count property."""
        tolerance_tables = RTToleranceTablesModule.from_required_elements()
        
        # Initially should have 0 tables
        assert tolerance_tables.tolerance_table_count == 0
        
        # Add tolerance tables
        table_items = [
            RTToleranceTablesModule.create_tolerance_table_item(tolerance_table_number=1),
            RTToleranceTablesModule.create_tolerance_table_item(tolerance_table_number=2)
        ]
        
        tolerance_tables.with_optional_elements(tolerance_table_sequence=table_items)
        
        # Should have 2 tables
        assert tolerance_tables.tolerance_table_count == 2
    
    def test_get_tolerance_table_numbers(self):
        """Test get_tolerance_table_numbers method."""
        tolerance_tables = RTToleranceTablesModule.from_required_elements()
        
        # Initially should return empty list
        assert tolerance_tables.get_tolerance_table_numbers() == []
        
        # Add tolerance tables with different numbers
        table_items = [
            RTToleranceTablesModule.create_tolerance_table_item(tolerance_table_number=1),
            RTToleranceTablesModule.create_tolerance_table_item(tolerance_table_number=5),
            RTToleranceTablesModule.create_tolerance_table_item(tolerance_table_number=3)
        ]
        
        tolerance_tables.with_optional_elements(tolerance_table_sequence=table_items)
        
        table_numbers = tolerance_tables.get_tolerance_table_numbers()
        assert set(table_numbers) == {1, 3, 5}
        assert len(table_numbers) == 3
    
    def test_get_tolerance_table_by_number(self):
        """Test get_tolerance_table_by_number method."""
        tolerance_tables = RTToleranceTablesModule.from_required_elements()
        
        # Should return None when no tables exist
        assert tolerance_tables.get_tolerance_table_by_number(1) is None
        
        # Add tolerance tables
        table_items = [
            RTToleranceTablesModule.create_tolerance_table_item(
                tolerance_table_number=1,
                tolerance_table_label="Table 1"
            ),
            RTToleranceTablesModule.create_tolerance_table_item(
                tolerance_table_number=2,
                tolerance_table_label="Table 2"
            )
        ]
        
        tolerance_tables.with_optional_elements(tolerance_table_sequence=table_items)
        
        # Test finding existing table
        table_1 = tolerance_tables.get_tolerance_table_by_number(1)
        assert table_1 is not None
        assert isinstance(table_1, Dataset)
        assert table_1.ToleranceTableNumber == 1
        assert table_1.ToleranceTableLabel == "Table 1"
        
        # Test finding non-existent table
        assert tolerance_tables.get_tolerance_table_by_number(99) is None
    
    def test_has_angular_tolerances(self):
        """Test has_angular_tolerances method."""
        tolerance_tables = RTToleranceTablesModule.from_required_elements()
        
        # Initially should be False
        assert not tolerance_tables.has_angular_tolerances()
        
        # Add table without angular tolerances
        table_items = [
            RTToleranceTablesModule.create_tolerance_table_item(
                tolerance_table_number=1,
                table_top_vertical_position_tolerance=1.0  # This is positional, not angular
            )
        ]
        tolerance_tables.with_optional_elements(tolerance_table_sequence=table_items)
        assert not tolerance_tables.has_angular_tolerances()
        
        # Add table with angular tolerance
        table_items.append(
            RTToleranceTablesModule.create_tolerance_table_item(
                tolerance_table_number=2,
                gantry_angle_tolerance=1.0  # This is angular
            )
        )
        tolerance_tables.with_optional_elements(tolerance_table_sequence=table_items)
        assert tolerance_tables.has_angular_tolerances()
    
    def test_has_positional_tolerances(self):
        """Test has_positional_tolerances method."""
        tolerance_tables = RTToleranceTablesModule.from_required_elements()
        
        # Initially should be False
        assert not tolerance_tables.has_positional_tolerances()
        
        # Add table with positional tolerance
        table_items = [
            RTToleranceTablesModule.create_tolerance_table_item(
                tolerance_table_number=1,
                table_top_vertical_position_tolerance=1.0
            )
        ]
        tolerance_tables.with_optional_elements(tolerance_table_sequence=table_items)
        assert tolerance_tables.has_positional_tolerances()
    
    def test_validate_basic(self):
        """Test basic validation functionality."""
        tolerance_tables = RTToleranceTablesModule.from_required_elements()
        
        # Should validate successfully with empty module
        validation_result = tolerance_tables.validate()
        assert 'errors' in validation_result
        assert 'warnings' in validation_result
        assert isinstance(validation_result['errors'], list)
        assert isinstance(validation_result['warnings'], list)