"""
Test GeneralReferenceModule functionality.

GeneralReferenceModule implements DICOM PS3.3 C.12.4 General Reference Module.
References source and other related Instances and describes the manner of derivation.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.modules import GeneralReferenceModule
from pyrt_dicom.enums.common_enums import SpatialLocationsPreserved


class TestGeneralReferenceModule:
    """Test GeneralReferenceModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with no required elements (all Type 3)."""
        reference = GeneralReferenceModule.from_required_elements()
        
        # Module should be created successfully with no attributes initially
        assert isinstance(reference, GeneralReferenceModule)
        assert isinstance(reference, Dataset)
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        # Create sequence using the helper method to get proper Dataset objects
        ref_image_seq = [GeneralReferenceModule.create_referenced_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9"
        )]
        
        reference = GeneralReferenceModule.from_required_elements().with_optional_elements(
            derivation_description="Contrast enhanced image",
            referenced_image_sequence=ref_image_seq
        )
        
        assert hasattr(reference, 'DerivationDescription')
        assert reference.DerivationDescription == "Contrast enhanced image"
        assert hasattr(reference, 'ReferencedImageSequence')
        assert len(reference.ReferencedImageSequence) == 1
    
    def test_create_referenced_image_item_basic(self):
        """Test basic referenced image item creation."""
        item = GeneralReferenceModule.create_referenced_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9"
        )
        
        assert isinstance(item, Dataset)
        assert item.ReferencedSOPClassUID == "1.2.840.10008.*******.1.2"
        assert item.ReferencedSOPInstanceUID == "*******.*******.9"
        assert not hasattr(item, 'ReferencedFrameNumber')
        assert not hasattr(item, 'PurposeOfReferenceCodeSequence')
    
    def test_create_referenced_image_item_with_frames(self):
        """Test referenced image item creation with frame numbers."""
        item = GeneralReferenceModule.create_referenced_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9",
            referenced_frame_number=[1, 2, 3]
        )
        
        assert item.ReferencedFrameNumber == [1, 2, 3]
    
    def test_create_source_image_item_basic(self):
        """Test basic source image item creation."""
        item = GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9"
        )
        
        assert isinstance(item, Dataset)
        assert item.ReferencedSOPClassUID == "1.2.840.10008.*******.1.2"
        assert item.ReferencedSOPInstanceUID == "*******.*******.9"
    
    def test_create_source_image_item_with_spatial_locations(self):
        """Test source image item creation with spatial locations preserved."""
        item = GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9",
            spatial_locations_preserved=SpatialLocationsPreserved.YES
        )
        
        assert item.SpatialLocationsPreserved == "YES"
    
    def test_create_source_image_item_with_patient_orientation(self):
        """Test source image item creation with patient orientation."""
        item = GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9",
            patient_orientation=["L", "P"]
        )
        
        assert item.PatientOrientation == ["L", "P"]
    
    def test_create_referenced_instance_item(self):
        """Test referenced instance item creation."""
        # Create proper Dataset for purpose code sequence
        purpose_code_item = Dataset()
        purpose_code_item.CodeValue = "121322"
        purpose_code_item.CodingSchemeDesignator = "DCM"
        purpose_code_item.CodeMeaning = "Source image for image processing operation"
        purpose_code = [purpose_code_item]
        
        item = GeneralReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9",
            purpose_of_reference_code_sequence=purpose_code
        )
        
        assert isinstance(item, Dataset)
        assert item.ReferencedSOPClassUID == "1.2.840.10008.*******.1.481.3"
        assert item.ReferencedSOPInstanceUID == "*******.*******.9"
        assert len(item.PurposeOfReferenceCodeSequence) == 1
        assert item.PurposeOfReferenceCodeSequence[0].CodeValue == "121322"
    
    def test_has_referenced_images_property(self):
        """Test has_referenced_images property."""
        reference = GeneralReferenceModule.from_required_elements()
        
        # Initially no referenced images
        assert not reference.has_referenced_images
        
        # Add referenced images using helper method
        ref_image_seq = [GeneralReferenceModule.create_referenced_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9"
        )]
        reference.with_optional_elements(referenced_image_sequence=ref_image_seq)
        
        assert reference.has_referenced_images
    
    def test_has_derivation_info_property(self):
        """Test has_derivation_info property."""
        reference = GeneralReferenceModule.from_required_elements()
        
        # Initially no derivation info
        assert not reference.has_derivation_info
        
        # Add derivation description
        reference.with_optional_elements(
            derivation_description="Test derivation"
        )
        
        assert reference.has_derivation_info
    
    def test_has_source_images_property(self):
        """Test has_source_images property."""
        reference = GeneralReferenceModule.from_required_elements()
        
        # Initially no source images
        assert not reference.has_source_images
        
        # Add source images using helper method
        source_image_seq = [GeneralReferenceModule.create_source_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9"
        )]
        reference.with_optional_elements(source_image_sequence=source_image_seq)
        
        assert reference.has_source_images
    
    def test_all_optional_elements(self):
        """Test setting all optional elements."""
        # Create reference sequences using helper methods
        reference_seq = [GeneralReferenceModule.create_referenced_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.9"
        )]
        
        # Create derivation code sequence with proper Dataset
        derivation_code_item = Dataset()
        derivation_code_item.CodeValue = "113076"
        derivation_code_item.CodingSchemeDesignator = "DCM"
        derivation_code_item.CodeMeaning = "Segmentation"
        derivation_code = [derivation_code_item]
        
        # Create instance sequence using helper method
        purpose_code_item = Dataset()
        purpose_code_item.CodeValue = "121322"
        purpose_code_item.CodingSchemeDesignator = "DCM"
        purpose_code_item.CodeMeaning = "Source image for image processing operation"
        instance_seq = [GeneralReferenceModule.create_referenced_instance_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.481.3",
            referenced_sop_instance_uid="*******.*******.9",
            purpose_of_reference_code_sequence=[purpose_code_item]
        )]
        
        reference = GeneralReferenceModule.from_required_elements().with_optional_elements(
            referenced_image_sequence=reference_seq,
            referenced_instance_sequence=instance_seq,
            derivation_description="Image derived from source",
            derivation_code_sequence=derivation_code,
            source_image_sequence=reference_seq,
            source_instance_sequence=instance_seq
        )
        
        assert hasattr(reference, 'ReferencedImageSequence')
        assert hasattr(reference, 'ReferencedInstanceSequence')
        assert hasattr(reference, 'DerivationDescription')
        assert hasattr(reference, 'DerivationCodeSequence')
        assert hasattr(reference, 'SourceImageSequence')
        assert hasattr(reference, 'SourceInstanceSequence')
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        reference = GeneralReferenceModule.from_required_elements()
        
        assert hasattr(reference, 'validate')
        assert callable(reference.validate)
        
        # Test validation returns expected structure
        result = reference.validate()
        assert result is not None
        assert isinstance(result, dict)
        assert 'errors' in result
        assert 'warnings' in result
    
    def test_none_values_not_set(self):
        """Test that None values are not set as attributes."""
        reference = GeneralReferenceModule.from_required_elements().with_optional_elements(
            derivation_description=None,
            referenced_image_sequence=None
        )
        
        # None values should not create attributes
        assert not hasattr(reference, 'DerivationDescription')
        assert not hasattr(reference, 'ReferencedImageSequence')