#!/usr/bin/env python3
"""HTML to Markdown converter utility.

This script converts HTML files to Markdown format, specifically designed
for DICOM documentation files.
"""

import argparse
import re
import sys
from pathlib import Path
from typing import Dict, List, Optional


class HTMLToMarkdownConverter:
    """Converts HTML content to Markdown format."""

    def __init__(self):
        """Initialize the converter with tag mappings."""
        self.tag_mappings = {
            'h1': '#',
            'h2': '##',
            'h3': '###',
            'h4': '####',
            'h5': '#####',
            'h6': '######',
        }

    def convert(self, html_content: str) -> str:
        """Convert HTML content to Markdown.
        
        Args:
            html_content: The HTML content to convert
            
        Returns:
            The converted Markdown content
        """
        markdown = html_content
        
        # Remove anchor tags with IDs but preserve content
        markdown = re.sub(r'<a[^>]*id="[^"]*"[^>]*>\s*</a>', '', markdown)
        
        # Convert cross-references to simple text, strip newlines
        def replace_xref(match):
            content = match.group(1).strip()
            content = re.sub(r'\s+', ' ', content)
            return content
        
        markdown = re.sub(r'<a[^>]*class="xref"[^>]*>([^<]+)</a>', replace_xref, markdown)
        
        # Convert external links, strip newlines
        def replace_link(match):
            url = match.group(1).strip()
            text = match.group(2).strip()
            text = re.sub(r'\s+', ' ', text)
            return f'[{text}]({url})'
        
        markdown = re.sub(r'<a[^>]*href="([^"]+)"[^>]*>([^<]+)</a>', replace_link, markdown)
        
        # Convert headers
        for tag, prefix in self.tag_mappings.items():
            # Handle headers with attributes, strip newlines from content
            def replace_header(match):
                content = match.group(1).strip()
                content = re.sub(r'\s+', ' ', content)
                return f'{prefix} {content}'
            
            markdown = re.sub(
                rf'<{tag}[^>]*>(.+?)</{tag}>',
                replace_header,
                markdown,
                flags=re.DOTALL
            )
        
        # Convert strong tags to markdown bold
        def replace_strong(match):
            content = match.group(1).strip()
            content = re.sub(r'\s+', ' ', content)
            return f'**{content}**'
            
        markdown = re.sub(r'<strong[^>]*>(.+?)</strong>', replace_strong, markdown, flags=re.DOTALL)
        
        # Convert paragraphs - clean up and join text properly
        def replace_paragraph(match):
            content = match.group(1).strip()
            # Remove extra whitespace and newlines within paragraphs
            content = re.sub(r'\s+', ' ', content)
            return content + '\n\n'
        
        markdown = re.sub(r'<p[^>]*>\s*<a[^>]*>\s*</a>\s*(.+?)</p>', replace_paragraph, markdown, flags=re.DOTALL)
        markdown = re.sub(r'<p[^>]*>(.+?)</p>', replace_paragraph, markdown, flags=re.DOTALL)
        
        # Convert lists
        markdown = self._convert_lists(markdown)
        
        # Convert tables
        markdown = self._convert_tables(markdown)
        
        # Clean up table formatting
        # markdown = re.sub(r'Table [A-Z0-9.-]+\. (.+)', r'**\1**\n', markdown)
        
        # Convert notes/divs with class="note"
        def replace_note(match):
            note_content = match.group(1)
            # Process the note content to clean up lists
            note_content = self._convert_lists(note_content)
            note_content = re.sub(r'<[^>]+>', '', note_content)
            note_content = re.sub(r'\n\s*\n', '\n', note_content)
            return f'> **Note**\n>\n{note_content}'
        
        markdown = re.sub(
            r'<div[^>]*class="note"[^>]*>.*?<h3[^>]*>Note</h3>(.*?)</div>',
            replace_note,
            markdown,
            flags=re.DOTALL
        )
        
        # Remove remaining HTML tags
        markdown = re.sub(r'<[^>]+>', '', markdown)
        
        # Clean up whitespace
        markdown = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown)
        markdown = re.sub(r'^\s+', '', markdown, flags=re.MULTILINE)
        markdown = markdown.strip()
        
        return markdown

    def _convert_lists(self, content: str) -> str:
        """Convert HTML lists to Markdown format."""
        # Convert ordered lists
        content = re.sub(r'<ol[^>]*class="orderedlist"[^>]*>', '', content)
        content = re.sub(r'</ol>', '', content)
        
        # Convert unordered lists
        content = re.sub(r'<ul[^>]*class="itemizedlist"[^>]*>', '', content)
        content = re.sub(r'</ul>', '', content)
        
        # Convert list items - handle content properly
        def replace_list_item(match):
            item_content = match.group(1)
            # Remove nested tags and clean content
            clean_content = re.sub(r'<[^>]+>', '', item_content)
            clean_content = re.sub(r'\s+', ' ', clean_content).strip()
            return f'- {clean_content}\n'
        
        content = re.sub(r'<li[^>]*class="listitem"[^>]*>(.*?)</li>', replace_list_item, content, flags=re.DOTALL)
        
        return content

    def _convert_tables(self, content: str) -> str:
        """Convert HTML tables to Markdown format."""
        # Extract complete table elements
        def replace_table(match):
            return self._process_table(match.group(1))
        
        content = re.sub(r'<table[^>]*>(.*?)</table>', replace_table, content, flags=re.DOTALL)
        
        return content

    def _process_table(self, table_html: str) -> str:
        """Process a single table and convert to Markdown."""
        # Extract headers
        header_pattern = r'<thead>(.*?)</thead>'
        header_match = re.search(header_pattern, table_html, re.DOTALL)
        
        # Extract body
        body_pattern = r'<tbody>(.*?)</tbody>'
        body_match = re.search(body_pattern, table_html, re.DOTALL)
        
        markdown_table = "\n"
        
        if header_match:
            headers = self._extract_table_cells(header_match.group(1), 'th')
            if headers:
                markdown_table += "| " + " | ".join(headers) + " |\n"
                markdown_table += "| " + " | ".join(['---'] * len(headers)) + " |\n"
        
        if body_match:
            rows = self._extract_table_rows_with_rowspan(body_match.group(1))
            for row in rows:
                if row:
                    markdown_table += "| " + " | ".join(row) + " |\n"
        
        markdown_table += "\n"
        return markdown_table

    def _extract_table_cells(self, content: str, tag: str) -> List[str]:
        """Extract table cells from HTML content."""
        pattern = rf'<{tag}[^>]*>(.*?)</{tag}>'
        cells = re.findall(pattern, content, re.DOTALL)
        
        cleaned_cells = []
        for cell in cells:
            # Remove nested tags and clean content
            clean_cell = re.sub(r'<[^>]+>', '', cell)
            clean_cell = re.sub(r'\s+', ' ', clean_cell).strip()
            # Handle empty cells
            if not clean_cell:
                clean_cell = ' '
            cleaned_cells.append(clean_cell)
        
        return cleaned_cells

    def _extract_table_rows_with_rowspan(self, tbody_content: str) -> List[List[str]]:
        """Extract table rows from tbody content, handling rowspan and colspan."""
        row_pattern = r'<tr[^>]*>(.*?)</tr>'
        row_matches = re.findall(row_pattern, tbody_content, re.DOTALL)
        
        processed_rows = []
        rowspan_tracker = {}  # Track cells that span multiple rows
        
        for row_idx, row_html in enumerate(row_matches):
            # Extract cells with their attributes
            cell_pattern = r'<td[^>]*>(.*?)</td>'
            cell_matches = re.finditer(cell_pattern, row_html, re.DOTALL)
            
            cells = []
            for match in cell_matches:
                full_tag = match.group(0)
                content = match.group(1)
                
                # Extract colspan and rowspan from the full tag
                colspan_match = re.search(r'colspan="(\d+)"', full_tag)
                rowspan_match = re.search(r'rowspan="(\d+)"', full_tag)
                
                colspan = int(colspan_match.group(1)) if colspan_match else 1
                rowspan = int(rowspan_match.group(1)) if rowspan_match else 1
                
                cells.append((colspan, rowspan, content))
            
            current_row = []
            col_idx = 0
            
            for colspan, rowspan, content in cells:
                # Skip positions filled by rowspan from above
                while (row_idx, col_idx) in rowspan_tracker:
                    current_row.append(rowspan_tracker[(row_idx, col_idx)])
                    col_idx += 1
                
                # Clean content
                clean_content = re.sub(r'<[^>]+>', '', content)
                clean_content = re.sub(r'\s+', ' ', clean_content).strip()
                if not clean_content:
                    clean_content = ' '
                
                # Handle colspan - add content to multiple columns
                for _ in range(colspan):
                    current_row.append(clean_content if _ == 0 else ' ')
                
                # Handle rowspan - store content for future rows
                if rowspan > 1:
                    for span_row in range(1, rowspan):
                        future_row_idx = row_idx + span_row
                        for span_col in range(colspan):
                            rowspan_tracker[(future_row_idx, col_idx + span_col)] = clean_content if span_col == 0 else ' '
                
                col_idx += colspan
            
            if current_row:
                processed_rows.append(current_row)
        
        return processed_rows

    def convert_file(self, input_path: Path, output_path: Optional[Path] = None) -> Path:
        """Convert an HTML file to Markdown.
        
        Args:
            input_path: Path to the input HTML file
            output_path: Path for the output Markdown file (optional)
            
        Returns:
            Path to the output Markdown file
        """
        if not input_path.exists():
            raise FileNotFoundError(f"Input file not found: {input_path}")
        
        # Read HTML content
        html_content = input_path.read_text(encoding='utf-8')
        
        # Convert to Markdown
        markdown_content = self.convert(html_content)
        
        # Determine output path
        if output_path is None:
            output_path = input_path.with_suffix('.md')
        
        # Write Markdown content
        output_path.write_text(markdown_content, encoding='utf-8')
        
        return output_path


def main():
    """Main entry point for the HTML to Markdown converter."""
    parser = argparse.ArgumentParser(
        description='Convert HTML files to Markdown format'
    )
    parser.add_argument(
        'input_file',
        type=Path,
        help='Path to the input HTML file'
    )
    parser.add_argument(
        '-o', '--output',
        type=Path,
        help='Path to the output Markdown file (default: same name with .md extension)'
    )
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Print the converted content without writing to file'
    )
    
    args = parser.parse_args()
    
    try:
        converter = HTMLToMarkdownConverter()
        
        if args.dry_run:
            html_content = args.input_file.read_text(encoding='utf-8')
            markdown_content = converter.convert(html_content)
            print(markdown_content)
        else:
            output_path = converter.convert_file(args.input_file, args.output)
            print(f"Converted {args.input_file} -> {output_path}")
    
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main()