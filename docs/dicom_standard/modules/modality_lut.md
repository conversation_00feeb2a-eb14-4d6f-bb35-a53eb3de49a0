### C.11.1 Modality LUT Module
Table C.11-1 specifies the Attributes of the Modality LUT Module, which describe the Modality LUT.
Either a Modality LUT Sequence containing a single Item or Rescale Slope and Intercept values shall be present but not both.
### Note
This requirement for only a single transformation makes it possible to unambiguously define the input of succeeding stages of the grayscale pipeline such as the VOI LUT.
**Table C.11-1. Modality LUT Module Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Include Table C.11-1b Modality LUT Macro Attributes |   |   |   |
**Table C.11-1b. Modality LUT Macro Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Modality LUT Sequence | (0028,3000) | 1C | Defines a Sequence of Modality LUTs. Only a single Item shall be included in this Sequence. Shall not be present if Rescale Intercept (0028,1052) is present. |
| &gt;LUT Descriptor | (0028,3002) | 1 | Specifies the format of the LUT Data in this Sequence. See Section C.11.1.1 for further explanation. |
| &gt;LUT Explanation | (0028,3003) | 3 | Free form text explanation of the meaning of the LUT. |
| &gt;Modality LUT Type | (0028,3004) | 1 | Specifies the output values of this Modality LUT. See Section C.******** for further explanation. |
| &gt;LUT Data | (0028,3006) | 1 | LUT Data in this Sequence. |
| Rescale Intercept | (0028,1052) | 1C | The value b in relationship between stored values (SV) and the output units specified in Rescale Type (0028,1054). Output units = m*SV + b. Required if Modality LUT Sequence (0028,3000) is not present. Shall not be present otherwise. |
| Rescale Slope | (0028,1053) | 1C | m in the equation specified by Rescale Intercept (0028,1052). Required if Rescale Intercept is present. |
| Rescale Type | (0028,1054) | 1C | Specifies the output units of Rescale Slope (0028,1053) and Rescale Intercept (0028,1052). See Section C.******** for further explanation. Required if Rescale Intercept is present. |
#### C.11.1.1 Modality LUT Module Attribute Descriptions
##### C.******** LUT Descriptor
The three Values of LUT Descriptor (0028,3002) describe the format of the LUT Data in the corresponding LUT Data (0028,3006).
The first Value is the number of entries in the lookup table. When the number of table entries is equal to 2  16  then this Value shall be 0.
The second Value is the first stored pixel value mapped. The Value Representation of the second Value (US or SS) is specified by Pixel Representation (0028,0103). This stored pixel value is mapped to the first entry in the LUT. All stored pixel values less than the first value mapped are also mapped to the first entry in the LUT Data. A stored pixel value one greater than the first value mapped is mapped to the second entry in the LUT Data. Subsequent stored pixel values are mapped to the subsequent entries in the LUT Data up to a stored pixel value equal to number of entries + first value mapped - 1 that is mapped to the last entry in the LUT Data. Stored pixel values greater than or equal to number of entries + first value mapped are also mapped to the last entry in the LUT Data.
The third Value specifies the number of bits for each entry in the LUT Data. It shall take the Value 8 or 16. The LUT Data shall be stored in a format equivalent to 8 bits allocated when the number of bits for each entry is 8, and 16 bits allocated when the number of bits for each entry is 16, where in both cases the high bit is equal to bits allocated - 1.
### Note
Some implementations have encoded 8 bit entries with 16 bits allocated, padding the high bits; this can be detected by comparing the number of entries specified in the LUT Descriptor with the actual Value Length of the LUT Data entry. The Value Length in bytes should equal the number of entries if bits allocated is 8, and be twice as long if bits allocated is 16.
The third Value also conveys the range of LUT entry values. It shall take the Value 8 or 16, corresponding with the LUT entry value range of 256 or 65536.
### Note
Since LUT Descriptor (0028,3002) is multi-valued, in an Explicit VR Transfer Syntax, only one Value Representation (US or SS) may be specified, even though the first and third values are always by definition interpreted as unsigned. The explicit VR actually used is dictated by the VR needed to represent the second Value, which will be consistent with Pixel Representation (0028,0103).
The LUT Data contains the LUT entry values.
The output range of the Modality LUT Module depends on whether or not Rescale Slope (0028,1053) and Rescale Intercept (0028,1052) or the Modality LUT Sequence (0028,3000) are used.
In the case where Rescale Slope and Rescale Intercept are used, the output ranges from (minimum pixel value*Rescale Slope+Rescale Intercept) to (maximum pixel value*Rescale Slope+Rescale Intercept), where the minimum and maximum pixel values are determined by Bits Stored and Pixel Representation.
### Note
This range may be signed even if Pixel Representation is unsigned.
In the case where the Modality LUT Sequence is used, the output range is from 0 to 2  n  -1 where n is The third Value of LUT Descriptor. This range is always unsigned.
##### C.******** Modality LUT and Rescale Type
Specifies the units of the output of the Modality LUT or rescale operation.
**Defined Terms:**
OD
The number in the LUT represents thousands of optical density. That is, a Value of 2140 represents an optical density of 2.140.
HU
Hounsfield Units (CT)
US
Unspecified
MGML
mg/ml
Z_EFF
Effective Atomic Number (i.e., Effective-Z)
ED
Electron density in 10  23  electrons/ml
EDW
Electron density normalized to water in units of N/Nw where N is number of electrons per unit volume, and Nw is number of electrons in the same unit of water at standard temperature and pressure.
HU_MOD
Modified Hounsfield Unit
PCT
Percentage (%)
Other values are permitted, but are not defined by the DICOM Standard.
##### C.********.1 Recommended Rescale Type Assignments For Multi-energy CT Image
Multi-energy CT Images can have multiple assignments of Rescale Types to Image Type Attributes. These are the recommended assignments for Rescale Type and Real World Value Mapping Attributes.
**Table C.********.1-1. Recommended Rescale Type Assignments for Multi-energy CT Image**
| Multi-energy Image Family | Recmd. Rescale Type (0028,1054) | Image Type (0008,0008) See Note 3. | Rescale Intercept (0028,1052) | Rescale Slope (0028,1053) | Real World Value First Value Mapped (0040,9216) | Real World Value Last Value Mapped (0040,9211) | Real World Value Intercept (0040,9224) | Real World Value Slope (0040,9225) | LUT Label (0040,9210) in Real World Value Mapping Macro | Measurement Units Code Sequence (0040,08EA) in Real World Value Mapping Macro |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| **Objective Image Family** |   |   |   |   |   |   |   |   |   |   |
| Virtual Monoenergetic Image | HU | VMI | -1024 | 1 | 0 | 4095 | -1024 | 1 | VMI | ([hnsf'U], UCUM, "Hounsfield unit") |
| Virtual Monoenergetic Image | HU | VMI | -8192 | 1 | 0 | 65535 | -8192 | 1 |
| Effective AN (Z) Image (see Note 1 ) | 10^-2 Z_EFF | EFF_ATOMIC_NUM | 0 | 1 | 0 | 4000 | 0 | 0.01 | EFF_ATOMIC_NUM | (129320, DCM, "Effective Atomic Number") |
| Electron Density Image | 10^-2ED | ELECTRON_DENSITY | 0 | 1 | 0 | 4000 | 0 | 0.01 | ELECTRON_DENSITY | (10*23/ml, UCUM, "Electron Density") |
| Electron Density Image | 10^-3EDW | ELECTRON_DENSITY | 0 | 1 | 0 | 4000 | 0 | 0.001 | ELECTRON_DENSITY | ({ratio}, UCUM, "ratio") |
| **Material Quantification Family** |   |   |   |   |   |   |   |   |   |   |
| Material-Specific Image | 10^-2MGML | MAT_SPECIFIC | (0) - (-10) | 1 | 0 | 4000 | -3 | 0.01 | MAT_SPECIFIC | (mg/cm3, UCUM, "mg/cm^3") |
| Material-Specific Image | HU | MAT_SPECIFIC | -1024 | 1 | 0 | 4095 | -1024 | 1 | MAT_SPECIFIC | ([hnsf'U], UCUM, "Hounsfield unit") |
| Material-Specific Image | HU | MAT_SPECIFIC | -8192 | 1 | 0 | 65535 | -8192 | 1 |
| Material-Removed Image (see Note 2 ) | HU | MAT_REMOVED | -1024 | 1 | 0 | 4095 | -1024 | 1 | MAT_REMOVED | ([hnsf'U], UCUM, "Hounsfield unit") |
| Material-Removed Image (see Note 2 ) | HU | MAT_REMOVED | -8192 | 1 | 0 | 65535 | -8192 | 1 |
| Material-Removed Image (see Note 2 ) | HU_MOD | MAT_REMOVED | -1024 | 1 | 0 | 4095 | -1024 | 1 | MAT_REMOVED | (129321, DCM, "Modified Hounsfield Unit") |
| Material-Removed Image (see Note 2 ) | HU_MOD | MAT_REMOVED | -8192 | 1 | 0 | 65535 | -8192 | 1 |
| Fractional Map Image | 10^-1 PCT | MAT_FRACTIONAL | 0 | 1 | 0 | 1000 | 0 | 0.1 | MAT_FRACTIONAL | (%, UCUM, "Percent") |
| Value-based Map Image | US | MAT_VALUE_BASED | 0 | 1 | 0 | 100 | 0 | 1 | MAT_VALUE_BASED | ([arb'U], UCUM, "arbitrary unit") |
| **Material Visualization Family** |   |   |   |   |   |   |   |   |   |   |
| Material-Modified Image | HU_MOD | MAT_MODIFIED | -1024 | 1 | 0 | 4095 | -1024 | 1 | MAT_MODIFIED | (129321, DCM, "Modified Hounsfield Unit") |
| Material-Modified Image | HU_MOD | MAT_MODIFIED | -8192 | 1 | 0 | 65535 | -8192 | 1 |
### Note
- This example assumes a scaling of 0.01 for the Effective Atomic Number which would be reasonable for images for which the effective atomic number was not greater than 40 for any pixels.
- The real-world value mapped pixels in the image may have been adjusted to represent the attenuation as if the pixel was filled with the remaining materials to preserve the relationship between the HU value of the pixel and the materials contained (shown as HU in the first row), or they may have not been adjusted (shown as HU_MOD).
- In the CT Image IOD, multi-energy types are encoded in the CT Image Module Image Type (0008,0008) Value 4. In the Enhanced CT Image IOD, multi-energy types are encoded in the Enhanced CT Image Module Image Type (0008,0008) Value 5 and/or the Frame Type (0008,9007) Value 5.
- For HU- and HU_MOD-based table rows, recommended numeric values are provided for both 12-bit and 16-bit representations (as determined by the Value of Bits Stored (0028,0101)). 16-bit representations are increasingly preferable to handle images with a large dynamic range of pixel values, such as low-dose lung scans which can result in HU values below -1000, MTF images with sharp kernels which can result in HU values below -4000, and images containing metals and other dense materials which can result in HU values like 15000 (for Titanium) and higher. The alternative of clipping such data to fit within a 12-bit range can disrupt subsequent image processing and analysis.