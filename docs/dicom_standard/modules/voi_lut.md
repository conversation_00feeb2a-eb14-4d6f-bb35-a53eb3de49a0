### C.11.2 VOI LUT Module
Table C.11-2 specifies the Attributes of the VOI LUT Module, which describe the VOI LUT.
**Table C.11-2. VOI LUT Module Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Include Table C.11-2b VOI LUT Macro Attributes |   |   |   |
**Table C.11-2b. VOI LUT Macro Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| VOI LUT Sequence | (0028,3010) | 1C | Defines a Sequence of VOI LUTs. One or more Items shall be included in this Sequence. Required if Window Center (0028,1050) is not present. May be present otherwise. |
| &gt;LUT Descriptor | (0028,3002) | 1 | Specifies the format of the LUT Data in this Sequence. See Section C.******** for further explanation. |
| &gt;LUT Explanation | (0028,3003) | 3 | Free form text explanation of the meaning of the LUT. |
| &gt;LUT Data | (0028,3006) | 1 | LUT Data in this Sequence. |
| Window Center | (0028,1050) | 1C | Window Center for display. See Section C.******** for further explanation. Required if VOI LUT Sequence (0028,3010) is not present. May be present otherwise. |
| Window Width | (0028,1051) | 1C | Window Width for display. See Section C.******** for further explanation. Required if Window Center (0028,1050) is present. |
| Window Center &amp; Width Explanation | (0028,1055) | 3 | Free form explanation of the meaning of the Window Center and Width. Multiple values correspond to multiple Window Center and Width values. |
| VOI LUT Function | (0028,1056) | 3 | Describes a VOI LUT function to apply to the Values of Window Center (0028,1050) and Window Width (0028,1051). See Section C.******** for further explanation. **Defined Terms:** LINEAR LINEAR_EXACT SIGMOID When this Attribute is not present, the interpretation of the Values of Window Center (0028,1050) and Window Width (0028,1051) is linear as in Section C.********. |
#### C.11.2.1 VOI LUT Module Attribute Descriptions
##### C.******** LUT Descriptor
The three Values of LUT Descriptor (0028,3002) describe the format of the LUT Data in the corresponding LUT Data (0028,3006).
The first Value is the number of entries in the lookup table. When the number of table entries is equal to 2  16  then this Value shall be 0.
The second Value is the first input value mapped. The Value Representation of the second Value (US or SS) depends on the source of the input to the VOI LUT, and shall be:
- the same as specified by Pixel Representation (0028,0103), if there is no Modality LUT or Rescale Slope and Intercept specified;
- SS if the possible output range after application of the Rescale Slope and Intercept may be signed; ### Note This is always the case for the CT Image IOD in which the Rescale Type is specified to be Hounsfield Units, which are always signed.
- US otherwise.
This input value is mapped to the first entry in the LUT. All input values less than the first value mapped are also mapped to the first entry in the LUT Data. An input value one greater than the first value mapped is mapped to the second entry in the LUT Data. Subsequent input values are mapped to the subsequent entries in the LUT Data up to an input value equal to number of entries + first value mapped - 1 that is mapped to the last entry in the LUT Data. Input values greater than or equal to number of entries + first value mapped are also mapped to the last entry in the LUT Data.
The third Value specifies the number of bits for each entry in the LUT Data. If the VOI LUT is included in an Image IOD, The third Value of LUT Descriptor (0028,3002) shall be 8 or 16 bits, unless otherwise specialized. If the VOI LUT is included in a Presentation State IOD, The third Value of LUT Descriptor (0028,3002) shall be between 8 and 16 inclusive. The LUT Data shall be stored in a format equivalent to 8 bits allocated when the number of bits for each entry is 8, and 16 bits allocated when the number of bits for each entry is 16, where in both cases the high bit is equal to bits stored - 1, and where bits stored is The third Value.
### Note
- Since LUT Descriptor (0028,3002) is multi-valued, in an Explicit VR Transfer Syntax, only one Value Representation (US or SS) may be specified, even though the first and third values are always by definition interpreted as unsigned. The explicit VR actually used is dictated by the VR needed to represent the second Value.
- Some implementations have encoded 8 bit entries with 16 bits allocated, padding the high bits; this can be detected by comparing the number of entries specified in the LUT Descriptor with the actual Value Length of the LUT Data entry. The Value Length in bytes should equal the number of entries if bits allocated is 8, and be twice as long if bits allocated is 16.
The LUT Data contains the LUT entry values.
The output range is from 0 to 2  n  -1 where n is The third Value of LUT Descriptor. This range is always unsigned.
##### C.******** Window Center and Window Width
###### C.********.1 Default LINEAR Function
If VOI LUT Function (0028,1056) is absent or has a Value of LINEAR, Window Center (0028,1050) and Window Width (0028,1051) specify a linear conversion from stored pixel values (after any Modality LUT or Rescale Slope and Intercept specified in the IOD have been applied) to values to be displayed. Window Center contains the input value that is the center of the window. Window Width contains the width of the window.
### Note
The terms "window center" and "window width" are not consistently used in practice, nor were they defined in previous releases of the Standard. The definitions here are presented for the purpose of defining consistent meanings for identity and threshold transformations while preserving the common practice of using integer values for center and width.
Window Width (0028,1051) shall always be greater than or equal to 1.
When Window Width (0028,1051) is greater than 1, these Attributes select the range of input values that are to be mapped to the full range of the displayed output.
When Window Width (0028,1051) is equal to 1, they specify a threshold below which input values will be displayed as the minimum output value.
### Note
Whether the minimum output value is rendered as black or white may depend on the Value of Photometric Interpretation (0028,0004) or the presence of a Presentation LUT Module.
These Attributes are applied according to the following pseudo-code, where x is the input value, y is an output value with a range from y  min  to y  max , c is Window Center (0028,1050) and w is Window Width (0028,1051):
- if (x &lt;= c - 0.5 - (w-1) /2), then y = y min
- else if (x &gt; c - 0.5 + (w-1) /2), then y = y max
- else y = ((x - (c - 0.5)) / (w-1) + 0.5) * (y max - y min ) + y min
### Note
- For the purpose of this definition, a floating point calculation without integer truncation is assumed, though the manner of implementation may vary as long as the result is the same.
- The pseudo-code function computes a continuous value over the output range without any discontinuity at the boundaries. The value of 0 for w is expressly forbidden, and the value of 1 for w does not cause division by zero, since the continuous segment of the function will never be reached for that case.
- For example, for an output range 0 to 255: c=2048, w=4096 becomes: if (x &lt;= 0) then y = 0
- else if (x &gt; 4095) then y = 255
- else y = ((x - 2047.5) / 4095 + 0.5) * (255-0) + 0
- c=2048, w=1 becomes: if (x &lt;= 2047.5) then y = 0
- else if (x &gt; 2047.5) then y = 255
- else /* not reached */
- c=0, w=100 becomes: if (x &lt;= -50) then y = 0
- else if (x &gt; 49) then y = 255
- else y = ((x + 0.5) / 99 + 0.5) * (255-0) + 0
- c=0, w=1 becomes: if (x &lt;= -0.5) then y = 0
- else if (x &gt; -0.5) then y = 255
- else /* not reached */
- A Window Center of 2 n-1 and a Window Width of 2 n selects the range of input values from 0 to 2 n -1. This represents a mathematical identity VOI LUT transformation over the possible input values (whether used or not) in the case where no Modality LUT is specified and the stored pixel data are n bit unsigned integers. In the case where x1 is the lowest input value actually used in the Pixel Data and x2 is the highest, a Window Center of (x1+x2+1)/2 and a Window Width of (x2-x1+1) selects the range of input values from x1 to x2, which represents the full range of input values present as opposed to possible. This is distinct from the mathematical identity VOI LUT transformation, which instead selects the full range of input values possible as opposed to those actually used. The mathematical identity and full input range transformations are the same when x1 = 0 and x2 is 2 n -1 and the input values are n bit unsigned integers. See also Note 7.
- A Window Width of 1 is typically used to represent a "threshold" operation in which those integer input values less than the Window Center are represented as the minimum displayed value and those greater than or equal to the Window Center are represented as the maximum displayed value. A Window Width of 2 will have the same result for integral input values.
- The application of Window Center (0028,1050) and Window Width (0028,1051) may select a signed input range. There is no implication that this signed input range is clipped to zero.
- The selected input range may exceed the actual range of the input values, thus effectively "compressing" the contrast range of the displayed data into a narrower band of the available contrast range, and "flattening" the appearance. There are no limits to the maximum value of the window width, or to the minimum or maximum value of window level, both of which may exceed the actual or possible range of input values.
- Input values "below" the window are displayed as the minimum output value and input values "above" the window are displayed as the maximum output value. This is the common usage of the window operation in medical imaging. There is no provision for an alternative approach in which all values "outside" the window are displayed as the minimum output value.
- The output of the Window Center/Width or VOI LUT transformation is either implicitly scaled to the full range of the display device if there is no succeeding transformation defined, or implicitly scaled to the full input range of the succeeding transformation step (such as the Presentation LUT), if present. See Section C.11.6.1.
- Fractional values of Window Center and Window Width are permitted (since the VR of these Attributes is Decimal String), and though they are not often encountered, applications should be prepared to accept them.
###### C.********.2 General Requirements for Window Center and Window Width
The Window Center (0028,1050), Window Width (0028,1051) and VOI LUT Function (0028,1056) shall be used only for Images with Photometric Interpretation (0028,0004) Values of MONOCHROME1 and MONOCHROME2. They have no meaning for other Images.
If multiple values are present in the Window Center (0028,1050) and Window Width (0028,1051), both shall have the same number of Values and shall be considered as pairs. Multiple values indicate that multiple alternative views may be presented.
If any VOI LUT, specified by the VOI LUT Sequence (0028,3010), is included by an Image, a pair of Window Center (0028,1050) and Window Width (0028,1051) Values, or the VOI LUT, but not both at the same time, may be applied to the Image for display. Inclusion of both indicates that multiple alternative views may be presented.
If multiple Items are present in VOI LUT Sequence (0028,3010), only one may be applied to the Image for display. Multiple Items indicate that multiple alternative views may be presented.
If the VOI LUT Module is defined in an IOD and if neither VOI LUT Sequence (0028,3010) nor Window Center (0028,1050) and Window Width (0028,1051) are present, then the VOI LUT stage of the grayscale pipeline (described in [Section N.2 in PS3.17](part04.html#sect_N.2) ) is defined to be an identity transformation.
### Note
- This requirement is specified so that IODs that define a particular output space for the grayscale pipeline, such as P-Values, are not in an undefined state when no VOI LUT Sequence (0028,3010) or Window Center (0028,1050) and Window Width (0028,1051) are present.
- Though the VOI LUT Module, VOI LUT Sequence (0028,3010) and Window Center (0028,1050) and Window Width (0028,1051) may be optional in a particular IOD, implementations that render images are expected to implement and apply these transformations when they are present in the image, unless overridden by the user, a presentation state, or a hanging protocol, and to allow the user to select which transformation to apply when multiple transformations are present.
##### C.******** VOI LUT Function
The VOI LUT Function (0028,1056) specifies a potentially non-linear conversion for the output of the (conceptual) Modality LUT values to the input of the (conceptual) Presentation LUT.
The behavior for the Value LINEAR is defined in Section C.********.1. For all other values, the VOI LUT Function (0028,1056) shall include a unique descriptor of the LUT function to be used. Each descriptor is associated with a bivariate function of Window Center (0028,1050) and Window Width (0028,1051).
If the VOI LUT Function (0028,1056) is present with a Value other than LINEAR, the values provided in Window Center (0028,1050) and Window Width (0028,1051) shall not be interpreted as a linear conversion of the (conceptual) Modality LUT values to the input to the (conceptual) Presentation LUT - but as parameters for the function defined by the VOI LUT Function descriptor in (0028,1056).
When defined, each descriptor must provide the functional relationship between the output of the (conceptual) Modality LUT values to the input of the (conceptual) Presentation LUT.
###### C.********.1 SIGMOID Function
If the Value of VOI LUT Function (0028,1056) is SIGMOID, the function to be used to convert the output of the (conceptual) Modality LUT values to the input of the (conceptual) Presentation LUT is given by
**Equation C.11-1.**
where
x
is the input value of the LUT (i.e., the output of the (conceptual) Modality LUT).
c
is the Window Center defined interactively by the user or by using the values provided in Window Center (0028,1050).
w
is the Window Width defined interactively by the user or by using the values provided in Window Width (0028,1051).
y
is the output value
y
min
is the minimum output value
y
max
is the maximum output value
Window Width (0028,1051) shall always be greater than 0.
### Note
Window Width (0028,1051) is required to be greater than zero to prevent division by zero (quite apart from being meaningless).
###### C.********.2 LINEAR_EXACT Function
If the Value of VOI LUT Function (0028,1056) is LINEAR_EXACT, the function to be used to convert the output of the (conceptual) Modality LUT values to the input of the (conceptual) Presentation LUT is given by the following pseudo-code, where x is the input value, y is an output value with a range from y  min  to y  max , c is Window Center (0028,1050) and w is Window Width (0028,1051):
- if (x &lt;= c - w/2), then y = y min
- else if (x &gt; c + w/2), then y = y max
- else y = ((x - c) / w + 0.5) * (y max - y min ) + y min
Window Width (0028,1051) shall always be greater than 0.
### Note
- For example, given stored unsigned pixel values from 0 to 65535, a Rescale Intercept (0028,1052) of 0 and a Rescale Slope (0028,1053) of 1.0/65535, a Window Width (0028,1051) of 1.0 and a Window Center (0028,1050) of 0.5 would specify the entire range of values (the identity transformation for those rescale values).
- Window Width (0028,1051) is required to be greater than zero to prevent division by zero (quite apart from being meaningless).