#### C.7.2.3 Clinical Trial Study Module
Table C.7-4b specifies the Attributes of the Clinical Trial Study Module, which identify a Study in the context of a clinical trial or research.
**Table C.7-4b. Clinical Trial Study Module Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Clinical Trial Time Point ID | (0012,0050) | 2 | An identifier specifying the one or more Studies that are grouped together as a clinical time point or submission in a clinical trial or research. See Section C.*******.1. |
| Issuer of Clinical Trial Time Point ID | (0012,0055) | 3 | Identifier of the Assigning Authority that issued the Clinical Trial Time Point ID. |
| Clinical Trial Time Point Description | (0012,0051) | 3 | A description of a set of one or more Studies that are grouped together to represent a clinical time point or submission in a clinical trial or research. See Section C.*******.1. |
| Clinical Trial Time Point Type Code Sequence | (0012,0054) | 3 | A pre-defined type of a set of one or more Studies that are grouped together to represent a clinical time point or submission in a clinical trial or research. See Section C.*******.1. One or more Items are permitted in this Sequence. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |   |   | B [CID 6146 Time Point Type](part16.html#sect_CID_6146). |
| Longitudinal Temporal Offset from Event | (0012,0052) | 3 | An offset in days from a particular event of significance. May be fractional. In the context of a clinical trial, this is often the days since enrollment, or the baseline imaging Study. |
| Longitudinal Temporal Event Type | (0012,0053) | 1C | The type of event to which Longitudinal Temporal Offset from Event (0012,0052) is relative. **Defined Terms:** ENROLLMENT Relative to enrollment of the subject in the research activity or clinical trial. BASELINE Relative to the baseline imaging Study. Required if Longitudinal Temporal Offset from Event (0012,0052) is present. |
| Consent for Clinical Trial Use Sequence | (0012,0083) | 3 | A Sequence that conveys information about consent for Clinical Trial or research use of the Composite Instances within this Study. One or more Items are permitted in this Sequence. See Section C.*******.2. |
| &gt;Distribution Type | (0012,0084) | 1C | The type of distribution for which consent to distribute has been granted. **Defined Terms:** NAMED_PROTOCOL RESTRICTED_REUSE PUBLIC_RELEASE See Section C.*******.2. Required if Consent for Distribution Flag (0012,0085) equals YES or WITHDRAWN. |
| &gt;Clinical Trial Protocol ID | (0012,0020) | 1C | The identifier of the protocol for which consent to distribute has been granted. Required if Distribution Type (0012,0084) is NAMED_PROTOCOL and the protocol is not that which is specified in Clinical Trial Protocol ID (0012,0020) in the Clinical Trial Subject Module. |
| &gt;Issuer of Clinical Trial Protocol ID | (0012,0022) | 3 | Identifier of the Assigning Authority that issued the Clinical Trial Protocol ID. |
| &gt;Consent for Distribution Flag | (0012,0085) | 1 | Whether or not consent to distribute has been granted for the purpose described in Distribution Type (0012,0084). **Enumerated Values:** NO YES WITHDRAWN See Section C.*******.2. ### Note Under some circumstances, consent may be withdrawn. The purpose of encoding this is to warn receiving systems that further distribution may not be appropriate, but no semantics are defined by the Standard for what action is appropriate under such circumstances, such as what to do with previously received images that had a Value of YES. |
##### C.******* Clinical Trial Study Module Attribute Descriptions
###### C.*******.1 Clinical Trial Time Point
The Clinical Trial Time Point ID (0012,0050) identifies an imaging Study within the context of an investigational protocol. This Attribute is used to define a set of Studies that are grouped together as a clinical time point or data submission in a clinical trial or research. The Clinical Trial Time Point Description (0012,0051) can be used to give a description of the Clinical Trial Time Point to which the set of Studies belongs. Clinical Trial Time Point Type Code Sequence (0012,0054) can be used to specify one or more pre-defined type of time point from a standard lexicon; more than one type is permitted, e.g., a time point may be "posttreatment" as well as "unscheduled" or "nadir", etc.
###### C.*******.2 Consent For Clinical Trial Use Sequence
For applications such as clinical trials or research, the distribution of Composite Instances in Studies, whether containing identifying information or partially or completely de-identified, may need to be controlled. Permission for distribution is usually granted under the control of the Patient (through informed consent), the ethics committee or institutional review board responsible for the Study, and the sponsor of the Study through contractual means. The Consent for Clinical Trial Use Sequence (0012,0083) is intended to encode the result of the consent process to allow appropriate subsequent handling of the Instances.
The Defined Terms for Distribution Type (0012,0084) mean that consent has been issued to distribute for the following purposes:
**Defined Terms:**
NAMED_PROTOCOL
conducting the protocol named in Clinical Trial Protocol ID (0012,0020)
RESTRICTED_REUSE
re-use for restricted purposes (not specified here) other than those for which the Instances were originally created
PUBLIC_RELEASE
release to the general public for re-use without restriction
### Note
- There is no intent to convey in this Sequence further details of the often complex consent and approval process. Further information about the protocol and ethics committee may be found in the Clinical Trials Modules, if present.
- There is no identification of an individual responsible for the approval or granting consent, since in the case of a clinical trial or research subject granting informed consent, the presence of this information would breach de-identification requirements.
- Multiple Sequence Items may be present; for example a Study may be approved for distribution for conducting multiple explicitly named protocols.
- Whether or not the Instances have been adequately de-identified for any particular purpose of distribution is not defined by the Attributes in the Consent for Clinical Trial Use Sequence (0012,0083). Other Attributes address this, such as Patient Identity Removed (0012,0062), De-identification Method (0012,0063), De-identification Method Code Sequence (0012,0064) and Burned In Annotation (0028,0301). See also [Annex E Attribute Confidentiality Profiles (Normative) in PS3.15](part15.html#chapter_E).
- It is possible that the list of Defined Terms for Distribution Type (0012,0084) may be extended in future for other purposes, not necessarily related to the conduct of clinical trials or research.