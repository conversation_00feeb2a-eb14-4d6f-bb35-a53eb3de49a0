#### C.8.8.11 RT Tolerance Tables Module
**Table C.8-47. RT Tolerance Tables Module Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Tolerance Table Sequence | (300A,0040) | 3 | Sequence of tolerance tables to be used for delivery of treatment plan. One or more Items are permitted in this Sequence. See Note 1. |
| &gt;Tolerance Table Number | (300A,0042) | 1 | Identification number of the Tolerance Table. The Value of Tolerance Table Number (300A,0042) shall be unique within the RT Plan in which it is created. |
| &gt;Tolerance Table Label | (300A,0043) | 3 | User-defined label for Tolerance Table. |
| &gt;Gantry Angle Tolerance | (300A,0044) | 3 | Maximum permitted difference (in degrees) between planned and delivered Gantry Angle. |
| &gt;Gantry Pitch Angle Tolerance | (300A,014E) | 3 | Maximum permitted difference (in degrees) between planned and delivered Gantry Pitch Angle. |
| &gt;Beam Limiting Device Angle Tolerance | (300A,0046) | 3 | Maximum permitted difference (in degrees) between planned and delivered Beam Limiting Device Angle. |
| &gt;Beam Limiting Device Tolerance Sequence | (300A,0048) | 3 | Sequence of beam limiting device (collimator) tolerances. One or more Items are permitted in this Sequence. |
| &gt;&gt;RT Beam Limiting Device Type | (300A,00B8) | 1 | Type of beam limiting device (collimator). **Enumerated Values:** X symmetric jaw pair in IEC X direction Y symmetric jaw pair in IEC Y direction ASYMX asymmetric jaw pair in IEC X direction ASYMY asymmetric jaw pair in IEC Y direction MLCX single layer multileaf collimator in IEC X direction MLCY single layer multileaf collimator in IEC Y direction |
| &gt;&gt;Beam Limiting Device Position Tolerance | (300A,004A) | 1 | Maximum permitted difference (in mm) between planned and delivered leaf (element) or jaw positions for current beam limiting device (collimator). |
| &gt;Patient Support Angle Tolerance | (300A,004C) | 3 | Maximum permitted difference (in degrees) between planned and delivered Patient Support Angle. |
| &gt;Table Top Eccentric Angle Tolerance | (300A,004E) | 3 | Maximum permitted difference (in degrees) between planned and delivered Table Top Eccentric Angle. |
| &gt;Table Top Pitch Angle Tolerance | (300A,004F) | 3 | Maximum permitted difference (in degrees) between the planned and delivered Table Top Pitch Angle. |
| &gt;Table Top Roll Angle Tolerance | (300A,0050) | 3 | Maximum permitted difference (in degrees) between the planned and delivered Table Top Roll Angle. |
| &gt;Table Top Vertical Position Tolerance | (300A,0051) | 3 | Maximum permitted difference (in mm) between planned and delivered Table Top Vertical Position. |
| &gt;Table Top Longitudinal Position Tolerance | (300A,0052) | 3 | Maximum permitted difference (in mm) between planned and delivered Table Top Longitudinal Position. |
| &gt;Table Top Lateral Position Tolerance | (300A,0053) | 3 | Maximum permitted difference (in mm) between planned and delivered Table Top Lateral Position. |
### Note
- Tolerance Tables may be used to compare planned with delivered machine parameters. If the absolute difference between the planned and delivered values exceeds the Tolerance Table value, treatment may be inhibited or the operator may be warned.