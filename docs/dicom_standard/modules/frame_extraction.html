<div class="section">
 <div class="titlepage">
  <div>
   <div>
    <h3 class="title">
     <a id="sect_C.12.3" shape="rect">
     </a>
     C.12.3 Frame Extraction Module
    </h3>
   </div>
  </div>
 </div>
 <p>
  <a id="para_ccdb40f4-3332-4b0f-a5f6-fae0bdc8a94a" shape="rect">
  </a>
  <a class="xref" href="#table_C.12-9" shape="rect" title="Table C.12-9. Frame Extraction Module Attributes">
   Table C.12-9
  </a>
  defines the Attributes that describe the Frames extracted if the SOP Instance was created in response to a Frame-Level retrieve request.
 </p>
 <div class="table">
  <a id="table_C.12-9" shape="rect">
  </a>
  <p class="title">
   <strong>
    Table C.12-9. Frame Extraction Module Attributes
   </strong>
  </p>
  <div class="table-contents">
   <table frame="box" rules="all">
    <thead>
     <tr valign="top">
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_f6bd35fb-042f-4e8a-ade7-1c379de0be9c" shape="rect">
        </a>
        Attribute Name
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_89d12ce6-d3bc-4be6-9e5f-b3ecf3fbcb66" shape="rect">
        </a>
        Tag
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_f9adc87a-22a7-4174-aaa9-d5db47754331" shape="rect">
        </a>
        Type
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_dd10efc8-48a7-4ef5-862c-9ade0f4391bf" shape="rect">
        </a>
        Attribute Description
       </p>
      </th>
     </tr>
    </thead>
    <tbody>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_a591b334-9015-4afb-8540-f0e9932a8011" shape="rect">
        </a>
        Frame Extraction Sequence
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_8de79e7a-b846-445b-b132-9be23fc5292f" shape="rect">
        </a>
        (0008,1164)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_c2a4df8e-dfba-49d0-a452-b8ddfa05d82c" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_915501b1-f0f5-4f26-8709-2f8139b072a5" shape="rect">
        </a>
        Sequence containing details of how this SOP Instance was extracted from a source multi-frame SOP Instance.
       </p>
       <p>
        <a id="para_148c617e-4688-445a-b0f9-7234a2b587bd" shape="rect">
        </a>
        If this Instance was created from an Instance that contains a Frame Extraction Sequence, then this Sequence shall contain all of the Items from the parent's Frame Extraction Sequence and a new Item that describes this extraction.
       </p>
       <p>
        <a id="para_90c1ef95-50ef-4b6a-9acd-3ebd6e98afd3" shape="rect">
        </a>
        One or more Items shall be included in this Sequence.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_1da22214-c7b3-4b27-b6a6-10586026ed14" shape="rect">
        </a>
        &gt;Multi-frame Source SOP Instance UID
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_8a435bd6-4bf3-4145-82ff-f654692e3762" shape="rect">
        </a>
        (0008,1167)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_4caa1448-326e-4552-8b2b-6c698549e1b4" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_f711ab36-44f4-4acf-bf88-dcbdebe375f4" shape="rect">
        </a>
        SOP Instance from which the Frames of this Instance are extracted.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_c6f94d50-da0b-43b2-a1cd-c7ca1fed668b" shape="rect">
        </a>
        &gt;Simple Frame List
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_ed9abf20-d671-4eda-9f65-195808137be0" shape="rect">
        </a>
        (0008,1161)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_27fd9b57-bba3-434a-9c34-0712e91bb4eb" shape="rect">
        </a>
        1C
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_d29faea2-fbe5-4c76-b099-1cf0bbb50b54" shape="rect">
        </a>
        A list of Frames that were extracted in the form of a simple list.
       </p>
       <p>
        <a id="para_eedea3d9-4c73-43a0-990d-cb915f1c7050" shape="rect">
        </a>
        Required if object extraction is based on a Frame Level Retrieve using the Simple Frame List (0008,1161).
       </p>
       <p>
        <a id="para_e0a1c6df-a8da-4895-869b-c01d4d11938b" shape="rect">
        </a>
        See
        <a class="olink" href="part04.html#PS3.4" shape="rect">
         PS3.4
        </a>
        "Instance and Frame Level Retrieve SOP Classes".
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_07ad3971-3694-4785-9dff-e4b0dec737ed" shape="rect">
        </a>
        &gt;Calculated Frame List
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_1d233ccf-3a3e-4c31-99b1-7fa7b51f53c2" shape="rect">
        </a>
        (0008,1162)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_3e4757b5-6c7e-42e1-aa01-8a1e1d83bb80" shape="rect">
        </a>
        1C
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_df32990c-7e23-47e0-9404-2918e48ac0bf" shape="rect">
        </a>
        A list of Frames that were extracted in the form of one or more triplets
       </p>
       <p>
        <a id="para_093bfdef-c552-4f23-9990-fdd6f51f4481" shape="rect">
        </a>
        Required if object extraction is based on a Frame Level Retrieve using the Calculated Frame List (0008,1162).
       </p>
       <p>
        <a id="para_2dc7d0fa-30ce-4918-8593-5308d4ab6a43" shape="rect">
        </a>
        See
        <a class="olink" href="part04.html#PS3.4" shape="rect">
         PS3.4
        </a>
        "Instance and Frame Level Retrieve SOP Classes".
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_3a3695ec-3b24-4244-b855-fc1ff4979774" shape="rect">
        </a>
        &gt;Time Range
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_f918c2f2-4e03-480b-aa13-74863d5dcbe2" shape="rect">
        </a>
        (0008,1163)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_4964617d-aca3-4e2b-94e0-68ba389d4dfb" shape="rect">
        </a>
        1C
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_c871ea0d-b48f-47ba-a3bb-631fbe229295" shape="rect">
        </a>
        The start and end times of the Frames that were extracted.
       </p>
       <p>
        <a id="para_ad79b93f-5216-4887-9d13-48de056cef0b" shape="rect">
        </a>
        Required if object extraction is based on a Frame Level Retrieve using Time Range (0008,1163).
       </p>
       <p>
        <a id="para_7a1206a9-5b3b-41b0-a54a-031ddc0fc029" shape="rect">
        </a>
        See
        <a class="olink" href="part04.html#PS3.4" shape="rect">
         PS3.4
        </a>
        "Instance and Frame Level Retrieve SOP Classes".
       </p>
      </td>
     </tr>
    </tbody>
   </table>
  </div>
 </div>
 <br class="table-break" clear="none"/>
</div>
