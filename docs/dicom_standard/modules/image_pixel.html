<div class="section">
 <div class="titlepage">
  <div>
   <div>
    <h4 class="title">
     <a id="sect_C.7.6.3" shape="rect">
     </a>
     C.7.6.3 Image Pixel Module
    </h4>
   </div>
  </div>
 </div>
 <p>
  <a id="para_c240d602-bb20-4761-be5e-06098782231f" shape="rect">
  </a>
  <a class="xref" href="#table_C.7-11a" shape="rect" title="Table C.7-11a. Image Pixel Module Attributes">
   Table C.7-11a
  </a>
  specifies the Attributes of the
  <a class="xref" href="#sect_C.7.6.3" shape="rect" title="C.7.6.3 Image Pixel Module">
   Image Pixel Module
  </a>.
 </p>
 <div class="table">
  <a id="table_C.7-11a" shape="rect">
  </a>
  <p class="title">
   <strong>
    Table C.7-11a. Image Pixel Module Attributes
   </strong>
  </p>
  <div class="table-contents">
   <table frame="box" rules="all">
    <thead>
     <tr valign="top">
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_cc00db8b-4a44-4a7d-99c1-2d44223f9377" shape="rect">
        </a>
        Attribute Name
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_303060dd-f820-473b-a7dc-5eba1e969dd1" shape="rect">
        </a>
        Tag
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_17de6799-3c80-4d5a-9bda-958f55868735" shape="rect">
        </a>
        Type
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_59b2ecab-7027-4266-8506-44ac0f643785" shape="rect">
        </a>
        Attribute Description
       </p>
      </th>
     </tr>
    </thead>
    <tbody>
     <tr valign="top">
      <td align="left" colspan="3" rowspan="1">
       <p>
        <a id="para_5e88514f-1690-4386-a558-b684e7618436" shape="rect">
        </a>
        <span class="italic">
         Include
         <a class="xref" href="#table_C.7-11c" shape="rect" title="Table C.7-11c. Image Pixel Description Macro Attributes">
          Table C.7-11c Image Pixel Description Macro Attributes
         </a>
        </span>
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_907dfbc4-114a-4c83-96bd-17f5bf7d92e2" shape="rect">
        </a>
        <span class="italic">
         Required if the IOD is not being transferred in a STOW-RS Request and is not encoded as metadata and compressed bulk pixel data. May be present otherwise.
        </span>
       </p>
       <p>
        <a id="para_8ba92f6c-c963-4046-a9b5-a2fda56e7de5" shape="rect">
        </a>
        <span class="italic">
         See
         <a class="olink" href="part18.html#sect_10.5" shape="rect">
          Section 10.5 Store Transaction in PS3.18
         </a>.
        </span>
       </p>
       <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
        <h3 class="title">
         Note
        </h3>
        <p>
         <a id="para_a9a3e07e-cbeb-4a6c-9e0e-64f4281f626c" shape="rect">
         </a>
         <span class="italic">
          When the IOD is encoded as metadata in a STOW-RS Request and the bulk pixel data is compressed, the STOW-RS origin server is required to be able to derive appropriate Values for the
          <a class="xref" href="#sect_C.7.6.3.2" shape="rect" title="C.7.6.3.2 Image Pixel Macro">
           Image Pixel Macro
          </a>
          Attributes from the compressed bit stream.
         </span>
        </p>
       </div>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_bd2a0280-ce66-40d3-a12a-ee22720c8198" shape="rect">
        </a>
        Pixel Data
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_a38e36fc-c6de-4b06-87f7-6bbadbd38278" shape="rect">
        </a>
        (7FE0,0010)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_124b7900-0af2-41ea-ab1e-60680e00ec48" shape="rect">
        </a>
        1C
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_0961e0a6-a137-47a0-89b1-09873ff28622" shape="rect">
        </a>
        A data stream of the pixel samples that comprise the Image. See
        <a class="xref" href="#sect_C.*******.4" shape="rect" title="C.*******.4 Pixel Data">
         Section C.*******.4
        </a>
        for further explanation.
       </p>
       <p>
        <a id="para_e590e0d1-11ca-40a4-a09d-4bc53ccb9a64" shape="rect">
        </a>
        Required if Pixel Data Provider URL (0028,7FE0) is not present.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_2402c314-ee06-476c-b798-b118369f40af" shape="rect">
        </a>
        Pixel Data Provider URL
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_f7bb0795-c9ea-49b0-9d9a-39c11eb85ccb" shape="rect">
        </a>
        (0028,7FE0)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_8bc0ba4f-ad10-4521-9bcf-e3c6b1fd9d0a" shape="rect">
        </a>
        1C
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_2d0163fe-ec56-4a63-a7c0-26a40f6cb24b" shape="rect">
        </a>
        A URL of a provider service that supplies the pixel data of the Image.
       </p>
       <p>
        <a id="para_f6835e05-fc54-4e64-9273-86bfabf5ed5e" shape="rect">
        </a>
        Required if the image is to be transferred in one of the following presentation contexts identified by Transfer Syntax UID:
       </p>
       <div class="itemizedlist">
        <ul class="itemizedlist" style="list-style-type: disc; ">
         <li class="listitem">
          <p>
           <a id="para_b69edabe-681d-4a16-a838-70432a4754b7" shape="rect">
           </a>
           1.2.840.10008.******** (DICOM JPIP Referenced Transfer Syntax)
          </p>
         </li>
         <li class="listitem">
          <p>
           <a id="para_9c7dd645-5b0c-4189-8460-39f0d799fb0d" shape="rect">
           </a>
           1.2.840.10008.******** (DICOM JPIP Referenced Deflate Transfer Syntax)
          </p>
         </li>
        </ul>
       </div>
       <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
        <h3 class="title">
         Note
        </h3>
        <p>
         <a id="para_b15d9666-51a7-4941-98e7-d3abc03c3053" shape="rect">
         </a>
         The VR of this Data Element has changed from UT to UR.
        </p>
       </div>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_c55c36b5-c9c5-4a15-8f0f-70b7c3140329" shape="rect">
        </a>
        Pixel Padding Range Limit
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_a8198dbd-4cbb-4d2e-a238-ef355107f73b" shape="rect">
        </a>
        (0028,0121)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_10478f56-7f26-42a6-90c7-57314069a477" shape="rect">
        </a>
        1C
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_c73a51bd-e946-41f2-adcf-97b76f6f20e3" shape="rect">
        </a>
        Pixel value that represents one limit (inclusive) of a range of padding values used together with Pixel Padding Value (0028,0120) as defined in the
        <a class="xref" href="#sect_C.7.5.1" shape="rect" title="C.7.5.1 General Equipment Module">
         General Equipment Module
        </a>. See
        <a class="xref" href="#sect_C.*******.2" shape="rect" title="C.*******.2 Pixel Padding Value and Pixel Padding Range Limit">
         Section C.*******.2
        </a>
        for further explanation.
       </p>
       <p>
        <a id="para_c89f41c5-3090-4f09-8bc6-fd7661b3b788" shape="rect">
        </a>
        Required if pixel padding is to be defined as a range rather than a single Value.
       </p>
       <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
        <h3 class="title">
         Note
        </h3>
        <div class="orderedlist">
         <ol class="orderedlist" type="1">
          <li class="listitem">
           <p>
            <a id="para_771f7d85-3efe-46a1-af76-a914dcdd2118" shape="rect">
            </a>
            The Value Representation of this Attribute is determined by the Value of Pixel Representation (0028,0103).
           </p>
          </li>
          <li class="listitem">
           <p>
            <a id="para_9fd8e933-8558-479d-b99a-d401602de3b0" shape="rect">
            </a>
            Pixel Padding Value (0028,0120) is also required when this Attribute is present.
           </p>
          </li>
         </ol>
        </div>
       </div>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_3106edae-90d1-42aa-8e4e-a069bf8967e6" shape="rect">
        </a>
        Extended Offset Table
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_bd8aa189-7c34-4cc2-88aa-02724ec1f886" shape="rect">
        </a>
        (7FE0,0001)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_4959f701-64ce-49a0-a244-f1d5a9d946e8" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_f9d1f2a4-7635-4f95-a8d0-aa20049cc97d" shape="rect">
        </a>
        Byte offsets of the Item Tags of the Frames in the Sequence of Items in Encapsulated Pixel Data encoded in Pixel Data (7FE0,0010).
       </p>
       <p>
        <a id="para_8ea72466-67a0-41b5-9c0b-3aecb92ca759" shape="rect">
        </a>
        See
        <a class="xref" href="#sect_C.*******.8" shape="rect" title="C.*******.8 Extended Offset Table">
         Section C.*******.8
        </a>.
       </p>
       <p>
        <a id="para_1dcb67d8-3e1f-4b20-bd10-2a5b80c3607b" shape="rect">
        </a>
        May only be present when:
       </p>
       <div class="itemizedlist">
        <ul class="itemizedlist" style="list-style-type: disc; ">
         <li class="listitem">
          <p>
           <a id="para_5ccc75f3-9968-47f7-9629-b12ee85790b2" shape="rect">
           </a>
           Pixel Data (7FE0,0010) is present, and
          </p>
         </li>
         <li class="listitem">
          <p>
           <a id="para_a9f8dace-d28c-48ca-a9d7-8f7379b8fc54" shape="rect">
           </a>
           the Transfer Syntax uses Encapsulated Format for the Pixel Data (7FE0,0010), and
          </p>
         </li>
         <li class="listitem">
          <p>
           <a id="para_1df522d0-dc6f-45bd-b03b-98078d2fce25" shape="rect">
           </a>
           the Transfer Syntax encodes Frames in separate Fragments, and
          </p>
         </li>
         <li class="listitem">
          <p>
           <a id="para_74d3ae81-729e-4278-942e-f6a00b001cbd" shape="rect">
           </a>
           the Basic Offset Table is not present (i.e., the first Item of Pixel Data (7FE0,0010) has zero length), and
          </p>
         </li>
         <li class="listitem">
          <p>
           <a id="para_938b0375-972a-4339-a2cd-edd559abafdb" shape="rect">
           </a>
           each Frame is entirely contained within one Fragment.
          </p>
         </li>
        </ul>
       </div>
       <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
        <h3 class="title">
         Note
        </h3>
        <div class="orderedlist">
         <ol class="orderedlist" type="1">
          <li class="listitem">
           <p>
            <a id="para_07527651-b7eb-4dd5-b173-800c973824e3" shape="rect">
            </a>
            Unlike a Basic Offset Table, an Extended Offset Table, if the Attribute is present, is not permitted to be empty.
           </p>
          </li>
          <li class="listitem">
           <p>
            <a id="para_ad66bb0b-d9eb-4fa5-9750-10b6e0d9f086" shape="rect">
            </a>
            If this Instance is part of a Concatenation, only the offset and lengths of the Frames encoded in the Pixel Data (7FE0,0010) of this Instance are indexed in the Extended Offset Table (7FE0,0001) and Extended Offset Table Lengths (7FE0,0002) in this Instance, not those of the entire Concatenation. I.e., the Values of these two Attributes are specific to each Instance. See also
            <a class="xref" href="#sect_C.7.6.16.2.2.4" shape="rect" title="C.7.6.16.2.2.4 Concatenations and Stacks">
             Section C.7.6.16.2.2.4
            </a>.
           </p>
          </li>
          <li class="listitem">
           <p>
            <a id="para_182ef7a8-e018-4deb-8e40-0f77881d06f9" shape="rect">
            </a>
            If the Data Set is re-encoded (such as in a different Transfer Syntax) any Extended Offset Table may need to be recomputed or removed.
           </p>
          </li>
         </ol>
        </div>
       </div>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_9a926428-2659-4d86-a9f4-aca255e99dd5" shape="rect">
        </a>
        Extended Offset Table Lengths
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_1881728e-9fb9-464a-8641-4e341e1f26b2" shape="rect">
        </a>
        (7FE0,0002)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_e6a3f825-93ce-4108-b84c-234f6a0790f3" shape="rect">
        </a>
        1C
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_03fcf3a6-e4b6-47d5-838a-d91672a09bca" shape="rect">
        </a>
        Byte lengths of the Frames in the Sequence of Items in Encapsulated Pixel Data encoded in Pixel Data (7FE0,0010).
       </p>
       <p>
        <a id="para_86698ea5-5b13-492b-829a-ad33fdb552f3" shape="rect">
        </a>
        See
        <a class="xref" href="#sect_C.*******.8" shape="rect" title="C.*******.8 Extended Offset Table">
         Section C.*******.8
        </a>.
       </p>
       <p>
        <a id="para_6479b234-6071-4f8a-a1ed-8a49ce0d94f8" shape="rect">
        </a>
        Required if Extended Offset Table (7FE0,0001) is present.
       </p>
       <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
        <h3 class="title">
         Note
        </h3>
        <div class="orderedlist">
         <ol class="orderedlist" type="1">
          <li class="listitem">
           <p>
            <a id="para_b310dea9-9fab-4c27-9584-d1a04f3fb0cc" shape="rect">
            </a>
            This Attribute is only sent when each Frame is entirely contained within one Fragment as a single contiguous span of bytes so that it is not necessary to assemble the Frame from Fragments with delimiters.
           </p>
          </li>
          <li class="listitem">
           <p>
            <a id="para_33173f02-fd5a-476f-a671-2b876f0932a8" shape="rect">
            </a>
            The length encoded in this Attribute may be an odd number if the compressed bitstream for the Frame is an odd length; i.e., it does not include any trailing padding required to make the Item an even length.
           </p>
          </li>
         </ol>
        </div>
       </div>
      </td>
     </tr>
    </tbody>
   </table>
  </div>
 </div>
 <br class="table-break" clear="none"/>
 <div class="section">
  <div class="titlepage">
   <div>
    <div>
     <h5 class="title">
      <a id="sect_C.*******" shape="rect">
      </a>
      C.******* Image Pixel Module Attribute Descriptions
     </h5>
    </div>
   </div>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h6 class="title">
       <a id="sect_C.*******.1" shape="rect">
       </a>
       C.*******.1 Samples per Pixel
      </h6>
     </div>
    </div>
   </div>
   <p>
    <a id="para_f697a89a-e79a-4383-b293-5c2041ed45e5" shape="rect">
    </a>
    Samples per Pixel (0028,0002) is the number of separate planes in this image. One and three image planes are defined. Other numbers of image planes are allowed, but their meaning is not defined by this Standard.
   </p>
   <p>
    <a id="para_538b2d8c-baaf-4e40-af78-b6f18fe166fc" shape="rect">
    </a>
    For monochrome (gray scale) and palette color images, the number of planes is 1. For RGB and other three vector color models, the Value of this Attribute is 3.
   </p>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <p>
     <a id="para_b2137fa4-1d70-48f4-97dd-58cbb5e34ef2" shape="rect">
     </a>
     The use of a Value of 4 was previously described, but the Photometric Interpretations that used it have been retired.
    </p>
   </div>
   <p>
    <a id="para_9efe0faa-7b11-4bd9-9518-10e9a0dc6d47" shape="rect">
    </a>
    All image planes shall have the same number of Rows (0028,0010), Columns (0028,0011), Bits Allocated (0028,0100), Bits Stored (0028,0101), High Bit (0028,0102), Pixel Representation (0028,0103), and Pixel Aspect Ratio (0028,0034).
   </p>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <p>
     <a id="para_886f9142-a51c-479b-b082-062bbd9adbd7" shape="rect">
     </a>
     Downsampled chrominance planes of a color Photometric Interpretation are a special case, e.g., for a Photometric Interpretation (0028,0004) of YBR_FULL_422. In such cases, Samples per Pixel (0028,0002) describes the nominal number of channels (i.e., 3), and does not reflect that two chrominance samples are shared between four luminance samples. For YBR_FULL_422, Rows (0028,0010) and Columns (0028,0011) describe the size of the luminance plane, not the downsampled chrominance planes.
    </p>
   </div>
   <p>
    <a id="para_798aae68-fbc3-4de5-8bc1-cc1c8159c3c1" shape="rect">
    </a>
    The data in each pixel may be represented as a "Composite Pixel Code". If Samples per Pixel is one, the Composite Pixel Code is just the "n" bit pixel sample, where "n" = Bits Allocated. If Samples per Pixel is greater than one, Composite Pixel Code is a "k" bit concatenation of samples, where "k" = Bits Allocated multiplied by Samples per Pixel, and with the sample representing the vector color designated first in the Photometric Interpretation name comprising the most significant bits of the Composite Pixel Code, followed in order by the samples representing the next vector colors, with the sample representing the vector color designated last in the Photometric Interpretation name comprising the least significant bits of the Composite Pixel Code. For example, for Photometric Interpretation = "RGB", the most significant "Bits Allocated" bits contain the Red sample, the next "Bits Allocated" bits
                                contain the Green sample, and the least significant "Bits Allocated" bits contain the Blue sample.
   </p>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h6 class="title">
       <a id="sect_C.*******.2" shape="rect">
       </a>
       C.*******.2 Photometric Interpretation
      </h6>
     </div>
    </div>
   </div>
   <p>
    <a id="para_8ea9c814-7320-42b2-afeb-001d2b215dbd" shape="rect">
    </a>
    The Value of Photometric Interpretation (0028,0004) specifies the intended interpretation of the image pixel data.
   </p>
   <p>
    <a id="para_3d7cf56b-f9c6-44df-a10f-d1c3c101c814" shape="rect">
    </a>
    See
    <a class="olink" href="part05.html#PS3.5" shape="rect">
     PS3.5
    </a>
    for additional restrictions imposed by compressed Transfer Syntaxes.
   </p>
   <p>
    <a id="para_55caedd1-397f-440c-be64-18f9731010c9" shape="rect">
    </a>
    See
    <a class="olink" href="part05.html#sect_8.2.13" shape="rect">
     Section 8.2.13 in
     <span class="olinkdocname">
      PS3.5
     </span>
    </a>
    for constraints that apply when using DICOM Real-Time Video.
   </p>
   <p>
    <a id="para_962639f9-f777-4fc0-bd94-8860e59f5ecb" shape="rect">
    </a>
    The following Values are defined. Other Values are permitted if supported by the Transfer Syntax but the meaning is not defined by this Standard.
   </p>
   <div class="variablelist">
    <p class="title">
     <strong>
      Defined Terms:
     </strong>
    </p>
    <dl class="variablelist">
     <dt>
      <span class="term">
       MONOCHROME1
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_c5edb722-1cb3-4c49-8f0c-548e71e371ed" shape="rect">
       </a>
       Pixel data represent a single monochrome image plane. The minimum sample value is intended to be displayed as white after any VOI gray scale transformations have been performed. See
       <a class="olink" href="part04.html#PS3.4" shape="rect">
        PS3.4
       </a>. This Value may be used only when Samples per Pixel (0028,0002) has a Value of 1. May be used for pixel data in a Native (uncompressed) or Encapsulated (compressed) format; see
       <a class="olink" href="part05.html#sect_8.2" shape="rect">
        Section 8.2 in
        <span class="olinkdocname">
         PS3.5
        </span>
       </a>.
      </p>
     </dd>
     <dt>
      <span class="term">
       MONOCHROME2
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_689f7260-aa02-41c4-9712-eb9139762527" shape="rect">
       </a>
       Pixel data represent a single monochrome image plane. The minimum sample value is intended to be displayed as black after any VOI gray scale transformations have been performed. See
       <a class="olink" href="part04.html#PS3.4" shape="rect">
        PS3.4
       </a>. This Value may be used only when Samples per Pixel (0028,0002) has a Value of 1. May be used for pixel data in a Native (uncompressed) or Encapsulated (compressed) format; see
       <a class="olink" href="part05.html#sect_8.2" shape="rect">
        Section 8.2 in
        <span class="olinkdocname">
         PS3.5
        </span>
       </a>.
      </p>
     </dd>
     <dt>
      <span class="term">
       PALETTE COLOR
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_6df2a768-f435-4a02-8d25-b8d9482b98d0" shape="rect">
       </a>
       Pixel data describe a color image with a single sample per pixel (single image plane). The pixel value is used as an index into each of the Red, Blue, and Green Palette Color Lookup Tables (0028,1101-1103&amp;1201-1203). This Value may be used only when Samples per Pixel (0028,0002) has a Value of 1. May be used for pixel data in a Native (uncompressed) or Encapsulated (compressed) format; see
       <a class="olink" href="part05.html#sect_8.2" shape="rect">
        Section 8.2 in
        <span class="olinkdocname">
         PS3.5
        </span>
       </a>. When the Photometric Interpretation is Palette Color; Red, Blue, and Green Palette Color Lookup Tables shall be present.
      </p>
     </dd>
     <dt>
      <span class="term">
       RGB
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_88bb278f-860b-462f-8313-c017f15e4fc2" shape="rect">
       </a>
       Pixel data represent a color image described by red, green, and blue image planes. The minimum sample value for each color plane represents minimum intensity of the color. This Value may be used only when Samples per Pixel (0028,0002) has a Value of 3. Planar Configuration (0028,0006) may be 0 or 1. May be used for pixel data in a Native (uncompressed) or Encapsulated (compressed) format; see
       <a class="olink" href="part05.html#sect_8.2" shape="rect">
        Section 8.2 in
        <span class="olinkdocname">
         PS3.5
        </span>
       </a>.
      </p>
     </dd>
     <dt>
      <span class="term">
       HSV
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_672a8b5d-cc07-4dd3-9272-661cfc501c80" shape="rect">
       </a>
       <span class="italic">
        Retired.
       </span>
      </p>
     </dd>
     <dt>
      <span class="term">
       ARGB
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_589cb6d1-efe5-4dbe-b0a4-a99aab13f016" shape="rect">
       </a>
       <span class="italic">
        Retired.
       </span>
      </p>
     </dd>
     <dt>
      <span class="term">
       CMYK
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_c7c7289c-658c-462b-940d-6e070b254def" shape="rect">
       </a>
       <span class="italic">
        Retired.
       </span>
      </p>
     </dd>
     <dt>
      <span class="term">
       YBR_FULL
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_28ad2fb0-4d52-47ed-9d4f-3a88de11c906" shape="rect">
       </a>
       Pixel data represent a color image described by one luminance (Y) and two chrominance planes (CB and CR). This photometric interpretation may be used only when Samples per Pixel (0028,0002) has a Value of 3. May be used for pixel data in a Native (uncompressed) or Encapsulated (compressed) format; see
       <a class="olink" href="part05.html#sect_8.2" shape="rect">
        Section 8.2 in
        <span class="olinkdocname">
         PS3.5
        </span>
       </a>. Planar Configuration (0028,0006) may be 0 or 1.
      </p>
      <p>
       <a id="para_c99ced72-a5c9-46f0-949f-6bc06d351c85" shape="rect">
       </a>
       This Photometric Interpretation is primarily used with RLE compressed bit streams, for which the Planar Configuration (0028,0006) may be 0 or 1; see
       <a class="olink" href="part05.html#sect_8.2.2" shape="rect">
        Section 8.2.2 in
        <span class="olinkdocname">
         PS3.5
        </span>
       </a>
       and
       <a class="olink" href="part05.html#sect_G.2" shape="rect">
        Section G.2 in
        <span class="olinkdocname">
         PS3.5
        </span>
       </a>. When used in the US Image Module, the Planar Configuration (0028,0006) is required to be 1; see
       <a class="xref" href="#sect_C.8.5.6.1.16" shape="rect" title="C.8.5.6.1.16 Planar Configuration">
        Section C.8.5.6.1.16 Planar Configuration
       </a>.
      </p>
      <p>
       <a id="para_4382f25f-0686-4dfd-9904-1f2cfa105025" shape="rect">
       </a>
       Black is represented by Y equal to zero. The absence of color is represented by both CB and CR values equal to half full scale.
      </p>
      <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
       <h3 class="title">
        Note
       </h3>
       <p>
        <a id="para_d63ebf2f-9363-4b46-bf67-3db76a39310c" shape="rect">
        </a>
        In the case where Bits Allocated (0028,0100) has Value of 8, half full scale is 128.
       </p>
      </div>
      <p>
       <a id="para_0a80eafe-b06b-49bf-887e-a9ee4db9ee20" shape="rect">
       </a>
       In the case where Bits Allocated (0028,0100) has a Value of 8 then the following equations convert between RGB and YCBCR Photometric Interpretation.
      </p>
      <p>
       <a id="para_6d856863-9ffb-4dca-abbe-89eb5e021a0a" shape="rect">
       </a>
       Y = +.2990R +.5870G +.1140B
      </p>
      <p>
       <a id="para_f0d67560-d2b8-4323-be43-d0dad1fa3e18" shape="rect">
       </a>
       CB= -.1687R -.3313G +.5000B + 128
      </p>
      <p>
       <a id="para_1f88a61c-5517-4897-943e-27cc8ab2b1e1" shape="rect">
       </a>
       CR= +.5000R -.4187G -.0813B + 128
      </p>
      <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
       <h3 class="title">
        Note
       </h3>
       <p>
        <a id="para_7ac69d6d-1a7e-46c1-8ede-1b7a17b43069" shape="rect">
        </a>
        The above is based on CCIR Recommendation 601-2 dated 1990.
       </p>
      </div>
     </dd>
     <dt>
      <span class="term">
       YBR_FULL_422
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_10f0e14b-ff96-40af-b5a6-070f05125b83" shape="rect">
       </a>
       The same as YBR_FULL except that the CB and CR values are sampled horizontally at half the Y rate and as a result there are half as many CB and CR values as Y values.
      </p>
      <p>
       <a id="para_4347d9f2-c4d9-43ce-81e7-0261e12813c8" shape="rect">
       </a>
       Planar Configuration (0028,0006) shall be 0. May be used for pixel data in a Native (uncompressed) or Encapsulated (compressed) format; see
       <a class="olink" href="part05.html#sect_8.2" shape="rect">
        Section 8.2 in
        <span class="olinkdocname">
         PS3.5
        </span>
       </a>.
      </p>
      <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
       <h3 class="title">
        Note
       </h3>
       <div class="orderedlist">
        <ol class="orderedlist" type="1">
         <li class="listitem">
          <p>
           <a id="para_bfec2204-e8c1-413e-b12c-f098b668e055" shape="rect">
           </a>
           This Photometric Interpretation is primarily used with JPEG compressed bit streams, but is also occasionally used for pixel data in a Native (uncompressed) format.
          </p>
         </li>
         <li class="listitem">
          <p>
           <a id="para_442b66ec-6d91-42ba-bfeb-738c94d2d6d3" shape="rect">
           </a>
           Though the chrominance channels are downsampled, there are still nominally three channels, hence Samples per Pixel (0028,0002) has a Value of 3, not 2. I.e., for pixel data in a Native (uncompressed) format, the Value Length of Pixel Data (7FE0,0010) is not:
          </p>
          <p>
           <a id="para_d392f9b3-d50b-4aa0-bf23-751a73e77683" shape="rect">
           </a>
           Rows (0028,0010) * Columns (0028,0011) * Number of Frames (0028,0008) * Samples per Pixel (0028,0002) * ((Bits Allocated (0028,0100)-1)/8+1)
          </p>
          <p>
           <a id="para_038b33db-c192-490b-90e1-8145936052a9" shape="rect">
           </a>
           padded to an even length, as it would otherwise be, but rather is:
          </p>
          <p>
           <a id="para_8e12882c-befe-436b-bdf5-95acc2106a61" shape="rect">
           </a>
           Rows (0028,0010) * Columns (0028,0011) * Number of Frames (0028,0008) * 2 * ((Bits Allocated (0028,0100)-1)/8+1)
          </p>
          <p>
           <a id="para_1d7cbb7e-b365-4910-b3ca-6e1d8c789e52" shape="rect">
           </a>
           padded to an even length.
          </p>
         </li>
         <li class="listitem">
          <p>
           <a id="para_054ed567-c63e-4450-a128-0c8af7adc764" shape="rect">
           </a>
           When used to describe JPEG compressed bit streams, the chrominance sub-sampling in the JPEG bit stream may differ from this description. E.g., though many JPEG codecs produce only horizontally sub-sampled chrominance components (4:2:2), some sub-sample vertically as well (4:2:0). Though inaccurate, the use of YBR_FULL_422 to describe both has proven harmless. For a discussion of the sub-sampling notation, see
           <a class="xref" href="#biblio_Poynton_Chroma" shape="rect" title="Chroma subsampling notation">
            [
            <abbr class="abbrev">
             Poynton 2008
            </abbr>
            ]
           </a>.
          </p>
         </li>
        </ol>
       </div>
      </div>
      <p>
       <a id="para_cbf1f014-22f2-4fa5-8fe2-b13908bd975f" shape="rect">
       </a>
       Two Y values shall be stored followed by one CB and one CR value. The CB and CR values shall be sampled at the location of the first of the two Y values. For each Row of Pixels, the first CB and CR samples shall be at the location of the first Y sample. The next CB and CR samples shall be at the location of the third Y sample etc.
      </p>
      <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
       <h3 class="title">
        Note
       </h3>
       <p>
        <a id="para_f95aac28-98ac-42e1-b7db-f5a1a95ed6be" shape="rect">
        </a>
        This subsampling sited on the even luminance pixels is often referred to as cosited sampling. The cositing applies when describing pixel data in a Native (uncompressed) form. When used to describe compressed bit streams, the siting depends on the compression scheme. E.g., for JPEG according to JFIF
        <a class="xref" href="#biblio_ISOIEC10918-5" shape="rect" title="JPEG Standard for digital compression and encoding of continuous-tone still images. Part 5 - JPEG File Interchange Format (JFIF)">
         [
         <abbr class="abbrev">
          ISO/IEC 10918-5
         </abbr>
         ]
        </a>, the siting is midway between luminance samples, whereas for MPEG2
        <a class="xref" href="#biblio_ISOIEC13818-2" shape="rect" title="Information technology - Generic coding of moving pictures and associated audio information: Video">
         [
         <abbr class="abbrev">
          ISO/IEC 13818-2
         </abbr>
         ]
        </a>, the sampling is cosited with the even luminance pixels. See also
        <a class="xref" href="#biblio_Poynton_Chroma" shape="rect" title="Chroma subsampling notation">
         [
         <abbr class="abbrev">
          Poynton 2008
         </abbr>
         ]
        </a>.
       </p>
      </div>
     </dd>
     <dt>
      <span class="term">
       YBR_PARTIAL_422
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_c2c3c216-0657-4e77-8313-2cf4ef501b94" shape="rect">
       </a>
       Retired. See
       <a class="link" href="http://dicom.nema.org/MEDICAL/Dicom/2017b/output/pdf/part03.pdf" shape="rect" target="_top">
        PS3.3-2017b
       </a>.
      </p>
     </dd>
     <dt>
      <span class="term">
       YBR_PARTIAL_420
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_f18af114-0f09-438c-8554-a9e85c8a82f3" shape="rect">
       </a>
       Pixel data represent a color image described by one luminance (Y) and two chrominance planes (CB and CR).
      </p>
      <p>
       <a id="para_af2c2d32-0f4b-46c8-8086-41e62f7d9382" shape="rect">
       </a>
       This photometric interpretation may be used only when Samples per Pixel (0028,0002) has a Value of 3. The CB and CR values are sampled horizontally and vertically at half the Y rate and as a result there are four times less CB and CR values than Y values.
      </p>
      <p>
       <a id="para_d99b3757-3218-4f30-88d8-a672a65e3f13" shape="rect">
       </a>
       Planar Configuration (0028,0006) shall be 0. Shall only be used for pixel data in an Encapsulated (compressed) format; see
       <a class="olink" href="part05.html#sect_8.2" shape="rect">
        Section 8.2 in
        <span class="olinkdocname">
         PS3.5
        </span>
       </a>.
      </p>
      <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
       <h3 class="title">
        Note
       </h3>
       <p>
        <a id="para_17826a3c-9e3c-42c4-a873-452018575754" shape="rect">
        </a>
        This Photometric Interpretation is primarily used with MPEG compressed bit streams. For a discussion of the sub-sampling notation and siting, see
        <a class="xref" href="#biblio_Poynton_Chroma" shape="rect" title="Chroma subsampling notation">
         [
         <abbr class="abbrev">
          Poynton 2008
         </abbr>
         ]
        </a>.
       </p>
      </div>
      <p>
       <a id="para_edd24ce9-8ae3-47a2-9265-5ff9ffec5dc7" shape="rect">
       </a>
       Luminance and chrominance values are represented as follows:
      </p>
      <div class="orderedlist">
       <ol class="orderedlist" type="1">
        <li class="listitem">
         <p>
          <a id="para_68c2df27-d754-415e-bdaf-943c0774480b" shape="rect">
          </a>
          black corresponds to Y = 16;
         </p>
        </li>
        <li class="listitem">
         <p>
          <a id="para_29e5e871-34d5-45c2-9a86-3074d295b122" shape="rect">
          </a>
          Y is restricted to 220 levels (i.e., the maximum value is 235);
         </p>
        </li>
        <li class="listitem">
         <p>
          <a id="para_b3898e3b-005b-4631-a964-d95d415abf9a" shape="rect">
          </a>
          CB and CR each has a minimum value of 16;
         </p>
        </li>
        <li class="listitem">
         <p>
          <a id="para_bfdf57d8-12e3-4958-a742-9c1d675230c8" shape="rect">
          </a>
          CB and CR are restricted to 225 levels (i.e., the maximum value is 240);
         </p>
        </li>
        <li class="listitem">
         <p>
          <a id="para_00cfc386-7699-4642-949c-c743329b8681" shape="rect">
          </a>
          lack of color is represented by CB and CR equal to 128.
         </p>
        </li>
       </ol>
      </div>
      <p>
       <a id="para_937c01e6-81aa-4080-a50f-bbcbd3e4d09a" shape="rect">
       </a>
       In the case where Bits Allocated (0028,0100) has the Value of 8 then the following equations convert between RGB and YBR_PARTIAL_420 Photometric Interpretation
      </p>
      <p>
       <a id="para_c877d543-a629-4f40-9b45-9a73cebf59e2" shape="rect">
       </a>
       Y = +.2568R +.5041G +.0979B + 16
      </p>
      <p>
       <a id="para_5b6d75aa-ea01-4dcd-9b94-0694d3a6b7f9" shape="rect">
       </a>
       CB= -.1482R -.2910G +.4392B + 128
      </p>
      <p>
       <a id="para_fd20139a-26cd-4c0d-9f4a-5d706d46bbf7" shape="rect">
       </a>
       CR= +.4392R -.3678G -.0714B + 128
      </p>
      <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
       <h3 class="title">
        Note
       </h3>
       <p>
        <a id="para_a035976e-e8bb-4971-934d-cc01779a044f" shape="rect">
        </a>
        The above is based on CCIR Recommendation 601-2 dated 1990.
       </p>
      </div>
      <p>
       <a id="para_3757bd2d-2f1d-472e-9aa2-016c5a9fad7f" shape="rect">
       </a>
       The CB and CR values shall be sampled at the location of the first of the two Y values. For the first Row of Pixels (etc.), the first CB and CR samples shall be at the location of the first Y sample. The next CB and CR samples shall be at the location of the third Y sample etc. The next Rows of Pixels containing CB and CR samples (at the same locations than for the first Row) will be the third etc.
      </p>
     </dd>
     <dt>
      <span class="term">
       YBR_ICT
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_d8353953-87c9-46a0-b863-1b0d57001701" shape="rect">
       </a>
       Irreversible Color Transformation:
      </p>
      <p>
       <a id="para_0f411b52-056e-4808-81ce-9cccdbe8c34c" shape="rect">
       </a>
       Pixel data represent a color image described by one luminance (Y) and two chrominance planes (CB and CR).
      </p>
      <p>
       <a id="para_24a945f8-f133-4954-9f51-a17fa06a803f" shape="rect">
       </a>
       This photometric interpretation may be used only when Samples per Pixel (0028,0002) has a Value of 3. Planar Configuration (0028,0006) shall be 0. Shall only be used for pixel data in an Encapsulated (compressed) format; see
       <a class="olink" href="part05.html#sect_8.2" shape="rect">
        Section 8.2 in
        <span class="olinkdocname">
         PS3.5
        </span>
       </a>.
      </p>
      <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
       <h3 class="title">
        Note
       </h3>
       <p>
        <a id="para_53ae92cd-6fef-4c89-be07-97978699cc3e" shape="rect">
        </a>
        This Photometric Interpretation is primarily used with JPEG 2000 compressed bit streams.
       </p>
      </div>
      <p>
       <a id="para_1edec279-3403-4208-abf1-c2eccdf4eb2c" shape="rect">
       </a>
       Black is represented by Y equal to zero. The absence of color is represented by both CB and CR values equal to zero.
      </p>
      <p>
       <a id="para_de17b0cc-7ca1-4257-9eb8-1a0ce448796f" shape="rect">
       </a>
       Regardless of the Value of Bits Allocated (0028,0100), the following equations convert between RGB and YCBCR Photometric Interpretation.
      </p>
      <p>
       <a id="para_9025e6d0-ef50-44ef-bb3d-7093c981480c" shape="rect">
       </a>
       Y = +.29900R +.58700G +.11400B
      </p>
      <p>
       <a id="para_06f4bf7d-994f-4d10-bb28-6d9576f03214" shape="rect">
       </a>
       CB= -.16875R -.33126G +.50000B
      </p>
      <p>
       <a id="para_b793919c-c941-4cfc-a581-9fbfd3fa478f" shape="rect">
       </a>
       CR= +.50000R -.41869G -.08131B
      </p>
      <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
       <h3 class="title">
        Note
       </h3>
       <div class="orderedlist">
        <ol class="orderedlist" type="1">
         <li class="listitem">
          <p>
           <a id="para_66a41f1c-b53a-45ab-84fe-1f9b262f59da" shape="rect">
           </a>
           The above is based on
           <a class="xref" href="#biblio_ISOIEC15444-1" shape="rect" title="JPEG 2000 Image Coding System">
            [
            <abbr class="abbrev">
             ISO/IEC 15444-1
            </abbr>
            ]
           </a>
           (JPEG 2000).
          </p>
         </li>
         <li class="listitem">
          <p>
           <a id="para_41a1f240-9a76-4645-8437-6543dd73dfe9" shape="rect">
           </a>
           In a JPEG 2000 bit stream, DC level shifting (used if the untransformed components are unsigned) is applied before forward color transformation, and the transformed components may be signed (unlike in JPEG ISO/IEC 10918-1).
          </p>
         </li>
         <li class="listitem">
          <p>
           <a id="para_54f14642-5650-4ffa-92fe-89b51fab0ada" shape="rect">
           </a>
           In JPEG 2000, spatial down-sampling of the chrominance components, if performed, is signaled in the JPEG 2000 bit stream.
          </p>
         </li>
        </ol>
       </div>
      </div>
     </dd>
     <dt>
      <span class="term">
       YBR_RCT
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_94300c5e-92bc-4c38-b349-20d2af4bfe22" shape="rect">
       </a>
       Reversible Color Transformation:
      </p>
      <p>
       <a id="para_5dd8f16b-896b-429c-a166-e5576d5ce3e2" shape="rect">
       </a>
       Pixel data represent a color image described by one luminance (Y) and two chrominance planes (CB and CR).
      </p>
      <p>
       <a id="para_a34a11b9-75e5-4388-ba14-c23c044a1929" shape="rect">
       </a>
       This photometric interpretation may be used only when Samples per Pixel (0028,0002) has a Value of 3. Planar Configuration (0028,0006) shall be 0. Shall only be used for pixel data in an Encapsulated (compressed) format; see
       <a class="olink" href="part05.html#sect_8.2" shape="rect">
        Section 8.2 in
        <span class="olinkdocname">
         PS3.5
        </span>
       </a>.
      </p>
      <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
       <h3 class="title">
        Note
       </h3>
       <p>
        <a id="para_efaff2d7-dbbc-450a-a22e-adc7728274d9" shape="rect">
        </a>
        This Photometric Interpretation is primarily used with JPEG 2000 compressed bit streams.
       </p>
      </div>
      <p>
       <a id="para_024fb805-a9dd-45d8-873e-65c8704d650e" shape="rect">
       </a>
       Black is represented by Y equal to zero. The absence of color is represented by both CB and CR values equal to zero.
      </p>
      <p>
       <a id="para_d7c5bcce-bf7f-444e-8638-0fa93b157419" shape="rect">
       </a>
       Regardless of the Value of Bits Allocated (0028,0100), the following equations convert between RGB and YBR_RCT Photometric Interpretation.
      </p>
      <p>
       <a id="para_e5cf3a5e-063e-4d09-af30-f386b137745c" shape="rect">
       </a>
       Y = (R + 2G +B) / 4 (Note:  mean floor)
      </p>
      <p>
       <a id="para_70070abb-39ae-44c0-a611-0fd6ae6d3684" shape="rect">
       </a>
       CB= B - G
      </p>
      <p>
       <a id="para_ceb05edc-b162-430b-bffb-afb269c29c6f" shape="rect">
       </a>
       CR= R - G
      </p>
      <p>
       <a id="para_f3deeb00-3ef5-4836-816e-efb41ace56fd" shape="rect">
       </a>
       The following equations convert between YBR_RCT and RGB Photometric Interpretation.
      </p>
      <p>
       <a id="para_50635fe3-6000-4472-890c-6dc302bd0b73" shape="rect">
       </a>
       G = Y -  (CR+ CB) / 4
      </p>
      <p>
       <a id="para_7a3e22b7-6492-4ba6-b77a-b5cb46b0ea24" shape="rect">
       </a>
       R = CR+ G
      </p>
      <p>
       <a id="para_015901ac-7676-49a4-96ae-48b8b1e14d4b" shape="rect">
       </a>
       B = CB+ G
      </p>
      <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
       <h3 class="title">
        Note
       </h3>
       <div class="orderedlist">
        <ol class="orderedlist" type="1">
         <li class="listitem">
          <p>
           <a id="para_40112d2d-6cdb-40a7-8845-4601f70c7337" shape="rect">
           </a>
           The above is based on
           <a class="xref" href="#biblio_ISOIEC15444-1" shape="rect" title="JPEG 2000 Image Coding System">
            [
            <abbr class="abbrev">
             ISO/IEC 15444-1
            </abbr>
            ]
           </a>
           (JPEG 2000).
          </p>
         </li>
         <li class="listitem">
          <p>
           <a id="para_65ff255f-3d89-495e-838b-5ced1c459ee5" shape="rect">
           </a>
           In a JPEG 2000 bit stream, DC level shifting (used if the untransformed components are unsigned) is applied before forward color transformation, and the transformed components may be signed (unlike in JPEG ISO/IEC 10918-1).
          </p>
         </li>
         <li class="listitem">
          <p>
           <a id="para_48bd9bf7-9999-4713-a6e4-4bae29eaa018" shape="rect">
           </a>
           This photometric interpretation is a reversible approximation to the YUV transformation used in PAL and SECAM.
          </p>
         </li>
        </ol>
       </div>
      </div>
     </dd>
     <dt>
      <span class="term">
       XYB
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_2c9f89e7-9191-4ae9-b85b-f34fd6e87db2" shape="rect">
       </a>
       Pixel data represent a color image described by XYB, the long/medium/short wavelength (LMS) based color model inspired by the human visual system, facilitating perceptually uniform quantization. It uses a gamma of 3 for computationally efficient decoding. The exact details of the XYB encoding are defined as part of a specific image being encoded in order to optimize image fidelity. Images in XYB transcoded to other Transfer Syntaxes will use RGB or the appropriate equivalent (e.g., YBR_FULL_422 for JPEG).
      </p>
      <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
       <h3 class="title">
        Note
       </h3>
       <p>
        <a id="para_38174c07-0620-485a-b629-81839446361b" shape="rect">
        </a>
        This is a possible color space used in JPEG XL
        <a class="xref" href="#biblio_ISOIEC18181-1" shape="rect" title="Information technology - JPEG XL Image Coding System - Part 1 Core Coding System">
         [
         <abbr class="abbrev">
          ISO/IEC 18181-1
         </abbr>
         ]
        </a>.
       </p>
      </div>
     </dd>
    </dl>
   </div>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h6 class="title">
       <a id="sect_C.*******.3" shape="rect">
       </a>
       C.*******.3 Planar Configuration
      </h6>
     </div>
    </div>
   </div>
   <p>
    <a id="para_80a38ce3-6b56-42e6-9941-a3db04694c7d" shape="rect">
    </a>
    Planar Configuration (0028,0006) indicates whether the color pixel data are encoded color-by-plane or color-by-pixel. This Attribute shall be present if Samples per Pixel (0028,0002) has a Value greater than 1. It shall not be present otherwise.
   </p>
   <div class="variablelist">
    <p class="title">
     <strong>
      Enumerated Values:
     </strong>
    </p>
    <dl class="variablelist compact">
     <dt>
      <span class="term">
       0
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_d8963355-e071-451b-8fb3-f8ec32dc8097" shape="rect">
       </a>
       The sample values for the first pixel are followed by the sample values for the second pixel, etc. For RGB images, this means the order of the pixel values encoded shall be R1, G1, B1, R2, G2, B2,, etc.
      </p>
     </dd>
     <dt>
      <span class="term">
       1
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_8eab4794-0f34-46d8-ba6d-118d39ee9f1a" shape="rect">
       </a>
       Each color plane shall be encoded contiguously. For RGB images, this means the order of the pixel values encoded is R1, R2, R3,, G1, G2, G3,, B1, B2, B3, etc.
      </p>
     </dd>
    </dl>
   </div>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <p>
     <a id="para_a1dcc4bc-a0cd-40a6-914e-4f25a5f52e2a" shape="rect">
     </a>
     Planar Configuration (0028,0006) is not meaningful when a compression Transfer Syntax is used that involves reorganization of sample components in the compressed bit stream. In such cases, since the Attribute is required to be present, then an appropriate value to use may be specified in the description of the Transfer Syntax in
     <a class="olink" href="part05.html#PS3.5" shape="rect">
      PS3.5
     </a>, though in all likelihood the value of the Attribute will be ignored by the receiving implementation.
    </p>
   </div>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h6 class="title">
       <a id="sect_C.*******.4" shape="rect">
       </a>
       C.*******.4 Pixel Data
      </h6>
     </div>
    </div>
   </div>
   <p>
    <a id="para_5741462e-cc2d-441f-82d9-5c82f24136d1" shape="rect">
    </a>
    Pixel Data (7FE0,0010) for this image. The order of pixels encoded for each image plane is left to right, top to bottom, i.e., the upper left pixel (labeled 1,1) is encoded first followed by the remainder of row 1, followed by the first pixel of row 2 (labeled 2,1) then the remainder of row 2 and so on.
   </p>
   <p>
    <a id="para_32ec08a2-bfaf-4bc8-8521-3e4334e5f6f6" shape="rect">
    </a>
    For multi-plane images see Planar Configuration (0028,0006) in this Section.
   </p>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h6 class="title">
       <a id="sect_C.*******.5" shape="rect">
       </a>
       C.*******.5 Palette Color Lookup Table Descriptor
      </h6>
     </div>
    </div>
   </div>
   <p>
    <a id="para_6b6cdcbd-e86a-46fc-a9c9-b97ea1701519" shape="rect">
    </a>
    The three Values of Palette Color Lookup Table Descriptor (0028,1101-1104) describe the format of the Lookup Table Data in the corresponding Data Element (0028,1201-1204) or (0028,1221-1223). In this section, the term "input value" is either the Palette Color Lookup Table input value described in the Enhanced Palette Color Lookup Table Sequence (0028,140B) or if that Attribute is absent, the stored pixel value.
   </p>
   <p>
    <a id="para_0207e64e-0957-4f88-810e-9eb2be3aa632" shape="rect">
    </a>
    The first Palette Color Lookup Table Descriptor Value is the number of entries in the lookup table. When the number of table entries is equal to 2
    <sup>
     16
    </sup>
    then this Value shall be 0. The first Value shall be identical for each of the Red, Green, Blue and Alpha Palette Color Lookup Table Descriptors.
   </p>
   <p>
    <a id="para_ef58b19b-f72e-4f49-b292-30b956a74547" shape="rect">
    </a>
    The second Palette Color Lookup Table Descriptor Value is the first input value mapped. This input value is mapped to the first entry in the Lookup Table Data. All input values less than the first value mapped are also mapped to the first entry in the Lookup Table Data if the Photometric Interpretation is PALETTE COLOR.
   </p>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <p>
     <a id="para_da6b7a18-9473-4945-b3af-fb94e498b417" shape="rect">
     </a>
     In the case of the Supplemental Palette Color LUT, the stored pixel values less than the second descriptor Value are grayscale values.
    </p>
   </div>
   <p>
    <a id="para_e8b978aa-4fa0-4b0e-8fd6-d30b26506cc7" shape="rect">
    </a>
    An input value one greater than the first value mapped is mapped to the second entry in the Lookup Table Data. Subsequent input values are mapped to the subsequent entries in the Lookup Table Data up to an input value equal to number of entries + first value mapped - 1, which is mapped to the last entry in the Lookup Table Data. Input values greater than or equal to number of entries + first value mapped are also mapped to the last entry in the Lookup Table Data. The second Value shall be identical for each of the Red, Green, Blue and Alpha Palette Color Lookup Table Descriptors.
   </p>
   <p>
    <a id="para_e7c2c21b-0010-4351-9bad-27e84ef7bddd" shape="rect">
    </a>
    The third Palette Color Lookup Table Descriptor Value specifies the number of bits for each entry in the Lookup Table Data. It shall take the Value of 8 or 16. The LUT Data shall be stored in a format equivalent to 8 bits allocated when the number of bits for each entry is 8, and 16 bits allocated when the number of bits for each entry is 16, where in both cases the high bit is equal to bits allocated-1. The third Value shall be identical for each of the Red, Green and Blue Palette Color Lookup Table Descriptors.
   </p>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <p>
     <a id="para_bdc8c43c-0691-4969-8213-402311653ba7" shape="rect">
     </a>
     Some implementations have encoded 8 bit entries with 16 bits allocated, padding the high bits; this can be detected by comparing the number of entries specified in the LUT Descriptor with the actual Value Length of the LUT Data entry. The Value Length in bytes should equal the number of entries if bits allocated is 8, and be twice as long if bits allocated is 16.
    </p>
   </div>
   <p>
    <a id="para_eb14e4e9-2ca8-43b6-9296-3d3c1479a1a5" shape="rect">
    </a>
    When the Red, Green, or Blue Palette Color Lookup Table Descriptor (0028,1101-1103) are used as part of the
    <a class="xref" href="#sect_C.7.9" shape="rect" title="C.7.9 Palette Color Lookup Table Module">
     Palette Color Lookup Table Module
    </a>
    or the
    <a class="xref" href="#sect_C.7.6.19" shape="rect" title="C.7.6.19 Supplemental Palette Color Lookup Table Module">
     Supplemental Palette Color Lookup Table Module
    </a>
    in an Image or Presentation State IOD, The third Value shall be equal to 16. When the Alpha Palette Color Lookup Table Descriptor (0028,1104) is used, The third Value shall be equal to 8.
   </p>
   <p>
    <a id="para_a614c81f-e874-4f62-a0e2-916d3c153311" shape="rect">
    </a>
    When the Red, Green, or Blue Palette Color Lookup Table Descriptor (0028,1101-1103) are used as part of the
    <a class="xref" href="#sect_C.7.9" shape="rect" title="C.7.9 Palette Color Lookup Table Module">
     Palette Color Lookup Table Module
    </a>
    in a
    <a class="xref" href="#sect_A.58" shape="rect" title="A.58 Color Palette IOD">
     Color Palette IOD
    </a>, the 3
    <sup>
     rd
    </sup>
    Value of Palette Color Lookup Table Descriptor (0028,1101-1103) (i.e, the number of bits for each entry in the Lookup Table Data) shall be 8.
   </p>
   <p>
    <a id="para_b5fa854d-da59-4c81-9c32-ece1f401f0b3" shape="rect">
    </a>
    When the Red, Green, or Blue Palette Color Lookup Table Descriptor (0028,1101-1103) are used as part of the
    <a class="xref" href="#sect_C.7.9" shape="rect" title="C.7.9 Palette Color Lookup Table Module">
     Palette Color Lookup Table Module
    </a>
    in the Segmentation IOD, the 3
    <sup>
     rd
    </sup>
    Value of Palette Color Lookup Table Descriptor (0028,1101-1103) (i.e, the number of bits for each entry in the Lookup Table Data) shall be 8 or 16.
   </p>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <div class="orderedlist">
     <ol class="orderedlist" type="1">
      <li class="listitem">
       <p>
        <a id="para_a4e67be1-02b6-48b4-89e3-70bb105d7ab1" shape="rect">
        </a>
        A Value of 16 indicates the Lookup Table Data will range from (0,0,0) minimum intensity to (65535,65535,65535) maximum intensity.
       </p>
      </li>
      <li class="listitem">
       <p>
        <a id="para_8f3a1178-e335-4395-bed6-64a6f5811da2" shape="rect">
        </a>
        Since the Palette Color Lookup Table Descriptor (0028,1101-1104) are multi-valued, in an Explicit VR Transfer Syntax, only one Value Representation (US or SS) may be specified, even though the first and third Values are always by definition interpreted as unsigned. The explicit VR actually used is dictated by the VR needed to represent the second Value, which will be consistent with Pixel Representation (0028,0103).
       </p>
      </li>
     </ol>
    </div>
   </div>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h6 class="title">
       <a id="sect_C.*******.6" shape="rect">
       </a>
       C.*******.6 Palette Color Lookup Table Data
      </h6>
     </div>
    </div>
   </div>
   <p>
    <a id="para_ec954e64-50ac-4f38-9fc9-0c4eac04286e" shape="rect">
    </a>
    Palette Color Lookup Table Data (0028,1201-1204) contain the lookup table data corresponding to the Lookup Table Descriptor (0028,1101-1104).
   </p>
   <p>
    <a id="para_69dc3373-aa60-41ae-896c-9a6e617bbb78" shape="rect">
    </a>
    Palette color values must always be scaled across the full range of available intensities. This is indicated by the fact that there are no bits stored and high bit Values for palette color data.
   </p>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <p>
     <a id="para_28bc4966-d994-4aef-b310-465729e8dc24" shape="rect">
     </a>
     For example, if there are 16 bits per entry specified and only 8 bits of value are truly used then the 8 bit intensities from 0 to 255 must be scaled to the corresponding 16 bit intensities from 0 to 65535. To do this for 8 bit values, simply replicate the Value in both the most and least significant bytes.
    </p>
   </div>
   <p>
    <a id="para_943fc0e4-4e10-4aaf-a387-29f7cdd08070" shape="rect">
    </a>
    These lookup tables shall be used only when there is a single sample per pixel (single image plane) in the image.
   </p>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h6 class="title">
       <a id="sect_C.*******.7" shape="rect">
       </a>
       C.*******.7 Pixel Aspect Ratio
      </h6>
     </div>
    </div>
   </div>
   <p>
    <a id="para_658d16e4-f5a6-4b41-b78a-58837bd4ef43" shape="rect">
    </a>
    The pixel aspect ratio is the ratio of the vertical size and horizontal size of the pixels in the image specified by a pair of integer values where the first Value is the vertical pixel size, and the second Value is the horizontal pixel size. To illustrate, consider the example pixel size shown in
    <a class="xref" href="#figure_C.*******.7-1" shape="rect" title="Figure C.*******.7-1. Example of Pixel Size and Aspect Ratio">
     Figure C.*******.7-1
    </a>
   </p>
   <p>
    <a id="para_e85d08b9-594b-49ca-b956-9fb0d496b324" shape="rect">
    </a>
   </p>
   <div class="figure">
    <a id="figure_C.*******.7-1" shape="rect">
    </a>
    <div class="figure-contents">
     <div class="mediaobject">
      <img alt="Example of Pixel Size and Aspect Ratio" src="figures/PS3.3_C.*******.7-1.svg"/>
     </div>
    </div>
    <p class="title">
     <strong>
      Figure C.*******.7-1. Example of Pixel Size and Aspect Ratio
     </strong>
    </p>
   </div>
   <p>
    <br class="figure-break" clear="none"/>
   </p>
   <p>
    <a id="para_97bf9d09-437e-44a5-acca-25e83f84c4f6" shape="rect">
    </a>
    Pixel Aspect Ratio = Vertical Size \ Horizontal Size = 0.30 mm \0.25 mm. Thus the Pixel Aspect Ratio could be represented as the multi-valued integer string "6\5", "60\50", or any equivalent integer ratio.
   </p>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h6 class="title">
       <a id="sect_C.*******.8" shape="rect">
       </a>
       C.*******.8 Extended Offset Table
      </h6>
     </div>
    </div>
   </div>
   <p>
    <a id="para_50de02f7-de9b-421f-8b3a-640702a43200" shape="rect">
    </a>
    The Extended Offset Table (7FE0,0001) Value shall contain byte offsets to the first byte of the Item Tag of the one and only Fragment for every Frame in the Pixel Data (7FE0,0010) Sequence of this Instance.
   </p>
   <p>
    <a id="para_59952002-77a5-4d29-aa55-453eb6c24377" shape="rect">
    </a>
    The byte offsets are measured from the first byte of the first Item Tag following the empty (zero length) Basic Offset Table Item, i.e., the Item Tag of the one and only Fragment of the first Frame.
   </p>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <p>
     <a id="para_846367fb-b2cb-44e4-89bc-9af046ae8901" shape="rect">
     </a>
     The offset is to the first byte of the Item Tag itself, not the first byte of the Value Field within the Item. See also the example of the Basic Offset Table in
     <a class="olink" href="part05.html#table_A.4-2" shape="rect">
      Table A.4-2 in
      <span class="olinkdocname">
       PS3.5
      </span>
     </a>.
    </p>
   </div>
   <p>
    <a id="para_c9b878fa-bc62-4292-aac5-9bc7bfc68cee" shape="rect">
    </a>
    If present, the first entry will always be 0000000000000000H.
   </p>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <p>
     <a id="para_da865724-a8b3-47f6-9c1e-4fced8f0f372" shape="rect">
     </a>
     The offset table is only present when the Pixel Data (7FE0,0010) is encoded in Encapsulated Format, since the position of uncompressed Frames encoded in Native Format can be computed from their known fixed length (i.e., derived from Rows (0028,0010), Columns (0028,0011), Samples per Pixel (0028,0002) and Bits Allocated (0028,0100)).
    </p>
   </div>
  </div>
 </div>
 <div class="section">
  <div class="titlepage">
   <div>
    <div>
     <h5 class="title">
      <a id="sect_C.7.6.3.2" shape="rect">
      </a>
      C.7.6.3.2 Image Pixel Macro
     </h5>
    </div>
   </div>
  </div>
  <p>
   <a id="para_4034fef0-c60a-4c3a-9f6a-2d3efa582169" shape="rect">
   </a>
   <a class="xref" href="#table_C.7-11b" shape="rect" title="Table C.7-11b. Image Pixel Macro Attributes">
    Table C.7-11b
   </a>
   specifies the Attributes of the
   <a class="xref" href="#sect_C.7.6.3.2" shape="rect" title="C.7.6.3.2 Image Pixel Macro">
    Section C.7.6.3.2 Image Pixel Macro
   </a>, which describe and encode the pixel data of the image.
  </p>
  <div class="table">
   <a id="table_C.7-11b" shape="rect">
   </a>
   <p class="title">
    <strong>
     Table C.7-11b. Image Pixel Macro Attributes
    </strong>
   </p>
   <div class="table-contents">
    <table frame="box" rules="all">
     <thead>
      <tr valign="top">
       <th align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_31a1dfb5-8951-419f-9ad2-980dfd90e223" shape="rect">
         </a>
         Attribute Name
        </p>
       </th>
       <th align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_c5414be3-1fa7-439d-98b5-927721922c3f" shape="rect">
         </a>
         Tag
        </p>
       </th>
       <th align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_ad8054fd-958e-49e4-b017-1290b391c4ab" shape="rect">
         </a>
         Type
        </p>
       </th>
       <th align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_2e3115dc-0bb7-49c2-b1cc-f0fb31f6aa7c" shape="rect">
         </a>
         Attribute Description
        </p>
       </th>
      </tr>
     </thead>
     <tbody>
      <tr valign="top">
       <td align="left" colspan="4" rowspan="1">
        <p>
         <a id="para_c3244cc2-4c98-4c48-870e-ff0f0eb29da0" shape="rect">
         </a>
         <span class="italic">
          Include
          <a class="xref" href="#table_C.7-11c" shape="rect" title="Table C.7-11c. Image Pixel Description Macro Attributes">
           Table C.7-11c Image Pixel Description Macro Attributes
          </a>
         </span>
        </p>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_7f0abce0-508b-424a-ae8a-8b4f83556cc1" shape="rect">
         </a>
         Pixel Data
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_f61854dc-931d-4908-a252-1ed13b66de96" shape="rect">
         </a>
         (7FE0,0010)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_3f6f7025-8ce0-4d21-9fb8-08538bfc0de7" shape="rect">
         </a>
         1
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_09779cd3-e12f-4e7f-af9e-b9039cb03579" shape="rect">
         </a>
         A data stream of the pixel samples that comprise the Image. See
         <a class="xref" href="#sect_C.*******.4" shape="rect" title="C.*******.4 Pixel Data">
          Section C.*******.4
         </a>
         for further explanation.
        </p>
       </td>
      </tr>
     </tbody>
    </table>
   </div>
  </div>
  <br class="table-break" clear="none"/>
 </div>
 <div class="section">
  <div class="titlepage">
   <div>
    <div>
     <h5 class="title">
      <a id="sect_C.*******" shape="rect">
      </a>
      C.******* Image Pixel Description Macro
     </h5>
    </div>
   </div>
  </div>
  <p>
   <a id="para_e797b304-1e45-4440-8e5d-709b9732a728" shape="rect">
   </a>
   <a class="xref" href="#table_C.7-11c" shape="rect" title="Table C.7-11c. Image Pixel Description Macro Attributes">
    Table C.7-11c
   </a>
   specifies the Attributes of the
   <a class="xref" href="#sect_C.*******" shape="rect" title="C.******* Image Pixel Description Macro">
    Section C.******* Image Pixel Description Macro
   </a>, which are the common Attributes that describe the pixel data of the image.
  </p>
  <div class="table">
   <a id="table_C.7-11c" shape="rect">
   </a>
   <p class="title">
    <strong>
     Table C.7-11c. Image Pixel Description Macro Attributes
    </strong>
   </p>
   <div class="table-contents">
    <table frame="box" rules="all">
     <thead>
      <tr valign="top">
       <th align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_2cdeece3-29b7-4f94-9ffc-6da19ee39eef" shape="rect">
         </a>
         Attribute Name
        </p>
       </th>
       <th align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_ce1e96de-7531-45e6-821c-7e7b6934bb55" shape="rect">
         </a>
         Tag
        </p>
       </th>
       <th align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_4ec31088-493d-4af5-8489-15f740e3fbdf" shape="rect">
         </a>
         Type
        </p>
       </th>
       <th align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_eab5ad53-0d93-407c-a912-5cefe1d30314" shape="rect">
         </a>
         Attribute Description
        </p>
       </th>
      </tr>
     </thead>
     <tbody>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_7b60f64f-e6f3-426c-8197-cebcf8752c5e" shape="rect">
         </a>
         Samples per Pixel
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_3b03cf41-8973-47f2-895d-421e651e3451" shape="rect">
         </a>
         (0028,0002)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_b23c16ba-974b-4684-9a27-32d9479beb43" shape="rect">
         </a>
         1
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_cb0f5461-d46c-4022-b457-b7480a90e490" shape="rect">
         </a>
         Number of samples (planes) in this image. See
         <a class="xref" href="#sect_C.*******.1" shape="rect" title="C.*******.1 Samples per Pixel">
          Section C.*******.1
         </a>
         for further explanation.
        </p>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_09ff4c11-8bd6-4fca-a646-d8a632369d1a" shape="rect">
         </a>
         Photometric Interpretation
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_6bc58ca3-a8ec-4459-a651-047c0b0f232b" shape="rect">
         </a>
         (0028,0004)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_1d8adee4-5966-4751-84ee-c0cdae4ba541" shape="rect">
         </a>
         1
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_e86b5217-5343-4fb3-9860-91926c908a63" shape="rect">
         </a>
         Specifies the intended interpretation of the pixel data. See
         <a class="xref" href="#sect_C.*******.2" shape="rect" title="C.*******.2 Photometric Interpretation">
          Section C.*******.2
         </a>
         for further explanation.
        </p>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_ce407029-c083-45c4-a755-a53b7ffcb8e1" shape="rect">
         </a>
         Rows
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_d2d76b5e-dd2d-421b-a02a-edc159db06b5" shape="rect">
         </a>
         (0028,0010)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_15549346-5ea9-4a3b-8c7b-5a0afecfc5e2" shape="rect">
         </a>
         1
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_d7447378-3ce9-48b7-bb28-d8c3be47354e" shape="rect">
         </a>
         Number of rows in the image.
        </p>
        <p>
         <a id="para_d401f07a-f172-48d1-acff-10ac0aa0007b" shape="rect">
         </a>
         Shall be an exact multiple of the vertical downsampling factor if any of the samples (planes) are encoded downsampled in the vertical direction for pixel data encoded in a Native (uncompressed) format. E.g., required to be an even value for a Photometric Interpretation (0028,0004) of YBR_FULL_422.
        </p>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_4c4538de-13d4-4aca-b675-1163a8e72924" shape="rect">
         </a>
         Columns
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_daea10fd-db12-4a49-b85f-30c08912a080" shape="rect">
         </a>
         (0028,0011)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_c0bb3b6d-7b7d-4aa1-83d0-1d0ed834dcdb" shape="rect">
         </a>
         1
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_6cc096f5-8430-41be-ace5-2731a56a5c2f" shape="rect">
         </a>
         Number of columns in the image.
        </p>
        <p>
         <a id="para_83668f60-3a80-4d48-815b-628743c2eac5" shape="rect">
         </a>
         Shall be an exact multiple of the horizontal downsampling factor if any of the samples (planes) are encoded downsampled in the horizontal direction for pixel data encoded in a Native (uncompressed) format. E.g., required to be an even value for a Photometric Interpretation (0028,0004) of YBR_FULL_422.
        </p>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_aa7643f9-8b02-41ab-8d77-ef88769760d6" shape="rect">
         </a>
         Bits Allocated
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_79d4778b-01cf-4c05-98cf-fa84bee56307" shape="rect">
         </a>
         (0028,0100)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_ca9c6604-30a0-40b2-8de0-3ff8c993d70b" shape="rect">
         </a>
         1
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_f5c1c3f3-19dc-4dc8-a150-62824ed991db" shape="rect">
         </a>
         Number of bits allocated for each pixel sample. Each sample shall have the same number of bits allocated. Bits Allocated (0028,0100) shall be either 1, or a multiple of 8. See
         <a class="olink" href="part05.html#PS3.5" shape="rect">
          PS3.5
         </a>
         for further explanation.
        </p>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_c968d09c-7c7e-422b-a59d-c5107f22714f" shape="rect">
         </a>
         Bits Stored
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_0ec29841-c8fa-43f1-b94e-8df88843397f" shape="rect">
         </a>
         (0028,0101)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_65ebbb3f-553f-46e2-83e0-35302acf4ca5" shape="rect">
         </a>
         1
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_facdc70e-f693-42d2-9f44-24fb9e71f868" shape="rect">
         </a>
         Number of bits stored for each pixel sample. Each sample shall have the same number of bits stored. See
         <a class="olink" href="part05.html#PS3.5" shape="rect">
          PS3.5
         </a>
         for further explanation.
        </p>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_8d1cda25-70af-468a-b492-56d715a45e5c" shape="rect">
         </a>
         High Bit
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_25575ab1-5339-499e-9215-72b6301b785d" shape="rect">
         </a>
         (0028,0102)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_0121d43b-88b4-48dd-9a14-ada5b8fed254" shape="rect">
         </a>
         1
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_fbaff261-93e4-485e-ae26-6b8cf9c22053" shape="rect">
         </a>
         Most significant bit for pixel sample data. Each sample shall have the same high bit. High Bit (0028,0102) shall be one less than Bits Stored (0028,0101). See
         <a class="olink" href="part05.html#PS3.5" shape="rect">
          PS3.5
         </a>
         for further explanation.
        </p>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_3c42c86a-7ba6-4fd4-9724-ac16585edaa0" shape="rect">
         </a>
         Pixel Representation
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_1e4975b5-7d67-4e10-917e-17c9aea71fc7" shape="rect">
         </a>
         (0028,0103)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_75919236-355e-464f-a4b0-dee2fb352d8e" shape="rect">
         </a>
         1
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_ee444489-0d46-45ca-9650-139c4ead14d0" shape="rect">
         </a>
         Data representation of the pixel samples. Each sample shall have the same pixel representation.
        </p>
        <div class="variablelist">
         <p class="title">
          <strong>
           Enumerated Values:
          </strong>
         </p>
         <dl class="variablelist compact">
          <dt>
           <span class="term">
            0000H
           </span>
          </dt>
          <dd>
           <p>
            <a id="para_c16a71d4-ba6c-4dca-9766-93c0e4986ef2" shape="rect">
            </a>
            unsigned integer.
           </p>
          </dd>
          <dt>
           <span class="term">
            0001H
           </span>
          </dt>
          <dd>
           <p>
            <a id="para_8c88599e-1314-47ef-b33f-b9aeb2c2601d" shape="rect">
            </a>
            2's complement
           </p>
          </dd>
         </dl>
        </div>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_ebf579a3-cc59-41f7-a971-84c98ab24f96" shape="rect">
         </a>
         Planar Configuration
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_cda57cb7-6b9f-42b8-8c53-86d20405087c" shape="rect">
         </a>
         (0028,0006)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_3cba2ef5-36de-452f-8f7f-222bd8c1e58d" shape="rect">
         </a>
         1C
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_ee7ce3ac-5667-4430-9070-501e82cee7cd" shape="rect">
         </a>
         Indicates whether the pixel data are encoded color-by-plane or color-by-pixel. Required if Samples per Pixel (0028,0002) has a Value greater than 1. See
         <a class="xref" href="#sect_C.*******.3" shape="rect" title="C.*******.3 Planar Configuration">
          Section C.*******.3
         </a>
         for further explanation.
        </p>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_dde268cd-efa0-419c-a5e5-ffe98af14547" shape="rect">
         </a>
         Pixel Aspect Ratio
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_190a30cc-934a-466b-a7ae-544c7a340cef" shape="rect">
         </a>
         (0028,0034)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_8e3f56fa-e64b-4871-b3f7-302285672045" shape="rect">
         </a>
         1C
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_ea8fed14-81a7-4faa-a3ee-810aad8dd497" shape="rect">
         </a>
         Ratio of the vertical size and horizontal size of the pixels in the image specified by a pair of integer values where the first Value is the vertical pixel size, and the second Value is the horizontal pixel size. Required if the aspect ratio values do not have a ratio of 1:1 and the physical pixel spacing is not specified by Pixel Spacing (0028,0030), or Imager Pixel Spacing (0018,1164) or Nominal Scanned Pixel Spacing (0018,2010), either for the entire Image or per-frame in a Functional Group Macro. See
         <a class="xref" href="#sect_C.*******.7" shape="rect" title="C.*******.7 Pixel Aspect Ratio">
          Section C.*******.7
         </a>.
        </p>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_a9977326-4954-4f96-b919-6266a449191e" shape="rect">
         </a>
         Smallest Image Pixel Value
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_8321b56d-ddc0-4d3f-b8b1-3ae40609f6d5" shape="rect">
         </a>
         (0028,0106)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_ce5fe446-d163-464c-8f6b-d0f58594ef1c" shape="rect">
         </a>
         3
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_aaca8944-85c1-4b6e-9c46-a52f30a03fdd" shape="rect">
         </a>
         The minimum actual pixel value encountered in this image.
        </p>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_fb7afd63-03b0-4e1c-9686-6a5bb841a2e3" shape="rect">
         </a>
         Largest Image Pixel Value
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_b51d419e-bbca-41b0-baec-812ea6538ee9" shape="rect">
         </a>
         (0028,0107)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_24793638-4bd5-4fa0-8be7-fc40b70b0344" shape="rect">
         </a>
         3
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_2a514770-caf6-4b67-84eb-092d0417fbc7" shape="rect">
         </a>
         The maximum actual pixel value encountered in this image.
        </p>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_6f54bfa0-5235-4be4-ab28-5d3a86c0fd7d" shape="rect">
         </a>
         Red Palette Color Lookup Table Descriptor
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_c06681a3-7f57-4851-b444-7135b6317125" shape="rect">
         </a>
         (0028,1101)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_29521127-807a-4502-bda8-c4940dbb8b55" shape="rect">
         </a>
         1C
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_fd6f2526-afae-498d-b2c1-ec9a980ecfb9" shape="rect">
         </a>
         Specifies the format of the Red Palette Color Lookup Table Data (0028,1201). Required if Photometric Interpretation (0028,0004) has a Value of PALETTE COLOR or Pixel Presentation (0008,9205) at the image level equals COLOR or MIXED. See
         <a class="xref" href="#sect_C.*******.5" shape="rect" title="C.*******.5 Palette Color Lookup Table Descriptor">
          Section C.*******.5
         </a>
         for further explanation.
        </p>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_1bb98062-be3a-4565-8bc8-e5e974781e30" shape="rect">
         </a>
         Green Palette Color Lookup Table Descriptor
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_3a6158ff-f5f5-4a80-84cf-421d721150f0" shape="rect">
         </a>
         (0028,1102)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_e2affc79-ea6c-4456-809c-bfa436452d84" shape="rect">
         </a>
         1C
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_85d92819-3351-4f23-bfe7-c959fadfc8a3" shape="rect">
         </a>
         Specifies the format of the Green Palette Color Lookup Table Data (0028,1202). Required if Photometric Interpretation (0028,0004) has a Value of PALETTE COLOR or Pixel Presentation (0008,9205) at the image level equals COLOR or MIXED. See
         <a class="xref" href="#sect_C.*******.5" shape="rect" title="C.*******.5 Palette Color Lookup Table Descriptor">
          Section C.*******.5
         </a>
         for further explanation.
        </p>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_961d8d3d-f552-4258-85d0-dd0f90f02922" shape="rect">
         </a>
         Blue Palette Color Lookup Table Descriptor
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_7a0358ff-b699-4c5a-9eda-40770e707100" shape="rect">
         </a>
         (0028,1103)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_afeaa237-4228-45d8-bc16-483a4eb0e4a7" shape="rect">
         </a>
         1C
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_a6d5d47d-6227-4a3a-9c3f-9dfb6ba91c3d" shape="rect">
         </a>
         Specifies the format of the Blue Palette Color Lookup Table Data (0028,1203). Required if Photometric Interpretation (0028,0004) has a Value of PALETTE COLOR or Pixel Presentation (0008,9205) at the image level equals COLOR or MIXED. See
         <a class="xref" href="#sect_C.*******.5" shape="rect" title="C.*******.5 Palette Color Lookup Table Descriptor">
          Section C.*******.5
         </a>
         for further explanation.
        </p>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_ec02d492-fc12-41eb-aa99-ae42e6b3c9af" shape="rect">
         </a>
         Red Palette Color Lookup Table Data
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_5d5706fb-dd0f-41cc-b97a-646bd5294836" shape="rect">
         </a>
         (0028,1201)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_b1a3ad12-d272-4c9c-b535-ca62b781cd53" shape="rect">
         </a>
         1C
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_daf33894-4c82-48d5-952c-0f331cd57a54" shape="rect">
         </a>
         Red Palette Color Lookup Table Data. Required if Photometric Interpretation (0028,0004) has a Value of PALETTE COLOR or Pixel Presentation (0008,9205) at the image level equals COLOR or MIXED. See
         <a class="xref" href="#sect_C.*******.6" shape="rect" title="C.*******.6 Palette Color Lookup Table Data">
          Section C.*******.6
         </a>
         for further explanation.
        </p>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_1130d897-7b28-4aad-a0cd-7f19370307a8" shape="rect">
         </a>
         Green Palette Color Lookup Table Data
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_e0cb3a65-8924-467b-9930-43a7235c512b" shape="rect">
         </a>
         (0028,1202)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_a011b507-310c-4fac-92f6-6f2d95e1977b" shape="rect">
         </a>
         1C
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_88fcd7cb-46c9-45ef-b346-fd796094cf94" shape="rect">
         </a>
         Green Palette Color Lookup Table Data. Required if Photometric Interpretation (0028,0004) has a Value of PALETTE COLOR or Pixel Presentation (0008,9205) at the image level equals COLOR or MIXED. See
         <a class="xref" href="#sect_C.*******.6" shape="rect" title="C.*******.6 Palette Color Lookup Table Data">
          Section C.*******.6
         </a>
         for further explanation.
        </p>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_f6be5248-551f-4e27-9924-03f0f865ddc0" shape="rect">
         </a>
         Blue Palette Color Lookup Table Data
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_ae830dc1-d20e-4ca0-95ea-d4e0b623c04a" shape="rect">
         </a>
         (0028,1203)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_81e6f0d6-fa20-4fc6-99db-b46742a954f2" shape="rect">
         </a>
         1C
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_24fcbf88-25ce-405e-9b0a-1a7544b8b5d8" shape="rect">
         </a>
         Blue Palette Color Lookup Table Data. Required if Photometric Interpretation (0028,0004) has a Value of PALETTE COLOR or Pixel Presentation (0008,9205) at the image level equals COLOR or MIXED. See
         <a class="xref" href="#sect_C.*******.6" shape="rect" title="C.*******.6 Palette Color Lookup Table Data">
          Section C.*******.6
         </a>
         for further explanation.
        </p>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_bf9aa22a-8899-437f-af4d-b0d896cb51c9" shape="rect">
         </a>
         ICC Profile
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_d9ff371e-c74a-4533-a874-74145f975cce" shape="rect">
         </a>
         (0028,2000)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_a5cb3ebf-8fc2-4fc3-9e5a-b8bf451c9656" shape="rect">
         </a>
         3
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_18e5de14-d4df-43f5-ac5b-5727051656ea" shape="rect">
         </a>
         An ICC Profile encoding the transformation of device-dependent color stored pixel values into PCS-Values.
        </p>
        <p>
         <a id="para_9a163954-c9a2-4774-87d0-1fb6f8c57ee3" shape="rect">
         </a>
         See
         <a class="xref" href="#sect_C.11.15.1.1" shape="rect" title="C.11.15.1.1 ICC Profile">
          Section C.11.15.1.1
         </a>.
        </p>
        <p>
         <a id="para_6169e1ce-5911-4062-824c-44da066f4c74" shape="rect">
         </a>
         When present, defines the color space of color Pixel Data (7FE0,0010) Values, and the output of Palette Color Lookup Table Data (0028,1201-1203).
        </p>
        <p>
         <a id="para_df2c6a16-6655-4636-9432-4b51998c0b5a" shape="rect">
         </a>
         Shall not be present in the top level dataset when the Optical Path Sequence (0048,0105) is present.
        </p>
        <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
         <h3 class="title">
          Note
         </h3>
         <div class="orderedlist">
          <ol class="orderedlist" type="1">
           <li class="listitem">
            <p>
             <a id="para_d69f5d22-89f7-4927-ba3e-806a769850dc" shape="rect">
             </a>
             The profile applies only to Pixel Data (7FE0,0010) at the same level of the Data Set and not to any icons nested within Sequences, which may or may not have their own ICC profile specified.
            </p>
           </li>
           <li class="listitem">
            <p>
             <a id="para_3aa3b8d8-6ecf-41f3-a75e-d78fcde394eb" shape="rect">
             </a>
             When the
             <a class="xref" href="#sect_C.8.12.5" shape="rect" title="C.8.12.5 Optical Path Module">
              Optical Path Module
             </a>
             is used, each optical path (Item of the Optical Path Sequence (0048,0105)) has its own ICC Profile (0028,2000).
            </p>
           </li>
          </ol>
         </div>
        </div>
       </td>
      </tr>
      <tr valign="top">
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_e5f83504-71b7-4baf-9386-7cf6bfe1141b" shape="rect">
         </a>
         Color Space
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_3f5a851f-6fd0-4903-95fa-cc80bb93b7ba" shape="rect">
         </a>
         (0028,2002)
        </p>
       </td>
       <td align="center" colspan="1" rowspan="1">
        <p>
         <a id="para_5216487c-5e7a-436d-a1ee-777f2f626e44" shape="rect">
         </a>
         3
        </p>
       </td>
       <td align="left" colspan="1" rowspan="1">
        <p>
         <a id="para_09a601fd-8012-4186-8e39-33fa28f85f55" shape="rect">
         </a>
         A label that identifies the well-known color space of the image. Shall be consistent with any ICC Profile (0028,2000) that is also present.
        </p>
        <p>
         <a id="para_092156be-e858-4202-a18a-89c38dbaa0ad" shape="rect">
         </a>
         Shall not be present when the Optical Path Sequence (0048,0105) is present.
        </p>
        <p>
         <a id="para_83317235-3f09-4d2f-a78c-ccacec3435ee" shape="rect">
         </a>
         See
         <a class="xref" href="#sect_C.11.15.1.2" shape="rect" title="C.11.15.1.2 Color Space">
          Section C.11.15.1.2
         </a>.
        </p>
       </td>
      </tr>
     </tbody>
    </table>
   </div>
  </div>
  <br class="table-break" clear="none"/>
 </div>
</div>
