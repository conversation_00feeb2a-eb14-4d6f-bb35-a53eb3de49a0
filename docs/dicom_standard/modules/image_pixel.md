#### C.7.6.3 Image Pixel Module
Table C.7-11a specifies the Attributes of the Image Pixel Module.
**Table C.7-11a. Image Pixel Module Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Include Table C.7-11c Image Pixel Description Macro Attributes |   |   | Required if the IOD is not being transferred in a STOW-RS Request and is not encoded as metadata and compressed bulk pixel data. May be present otherwise. See [Section 10.5 Store Transaction in PS3.18](part18.html#sect_10.5). ### Note When the IOD is encoded as metadata in a STOW-RS Request and the bulk pixel data is compressed, the STOW-RS origin server is required to be able to derive appropriate Values for the Image Pixel Macro Attributes from the compressed bit stream. |
| Pixel Data | (7FE0,0010) | 1C | A data stream of the pixel samples that comprise the Image. See Section C.*******.4 for further explanation. Required if Pixel Data Provider URL (0028,7FE0) is not present. |
| Pixel Data Provider URL | (0028,7FE0) | 1C | A URL of a provider service that supplies the pixel data of the Image. Required if the image is to be transferred in one of the following presentation contexts identified by Transfer Syntax UID: - 1.2.840.10008.******** (DICOM JPIP Referenced Transfer Syntax) - 1.2.840.10008.******** (DICOM JPIP Referenced Deflate Transfer Syntax) ### Note The VR of this Data Element has changed from UT to UR. |
| Pixel Padding Range Limit | (0028,0121) | 1C | Pixel value that represents one limit (inclusive) of a range of padding values used together with Pixel Padding Value (0028,0120) as defined in the General Equipment Module. See Section C.*******.2 for further explanation. Required if pixel padding is to be defined as a range rather than a single Value. ### Note - The Value Representation of this Attribute is determined by the Value of Pixel Representation (0028,0103). - Pixel Padding Value (0028,0120) is also required when this Attribute is present. |
| Extended Offset Table | (7FE0,0001) | 3 | Byte offsets of the Item Tags of the Frames in the Sequence of Items in Encapsulated Pixel Data encoded in Pixel Data (7FE0,0010). See Section C.*******.8. May only be present when: - Pixel Data (7FE0,0010) is present, and - the Transfer Syntax uses Encapsulated Format for the Pixel Data (7FE0,0010), and - the Transfer Syntax encodes Frames in separate Fragments, and - the Basic Offset Table is not present (i.e., the first Item of Pixel Data (7FE0,0010) has zero length), and - each Frame is entirely contained within one Fragment. ### Note - Unlike a Basic Offset Table, an Extended Offset Table, if the Attribute is present, is not permitted to be empty. - If this Instance is part of a Concatenation, only the offset and lengths of the Frames encoded in the Pixel Data (7FE0,0010) of this Instance are indexed in the Extended Offset Table (7FE0,0001) and Extended Offset Table Lengths (7FE0,0002) in this Instance, not those of the entire Concatenation. I.e., the Values of these two Attributes are specific to each Instance. See also Section C.7.6.16.2.2.4. - If the Data Set is re-encoded (such as in a different Transfer Syntax) any Extended Offset Table may need to be recomputed or removed. |
| Extended Offset Table Lengths | (7FE0,0002) | 1C | Byte lengths of the Frames in the Sequence of Items in Encapsulated Pixel Data encoded in Pixel Data (7FE0,0010). See Section C.*******.8. Required if Extended Offset Table (7FE0,0001) is present. ### Note - This Attribute is only sent when each Frame is entirely contained within one Fragment as a single contiguous span of bytes so that it is not necessary to assemble the Frame from Fragments with delimiters. - The length encoded in this Attribute may be an odd number if the compressed bitstream for the Frame is an odd length; i.e., it does not include any trailing padding required to make the Item an even length. |
##### C.******* Image Pixel Module Attribute Descriptions
###### C.*******.1 Samples per Pixel
Samples per Pixel (0028,0002) is the number of separate planes in this image. One and three image planes are defined. Other numbers of image planes are allowed, but their meaning is not defined by this Standard.
For monochrome (gray scale) and palette color images, the number of planes is 1. For RGB and other three vector color models, the Value of this Attribute is 3.
### Note
The use of a Value of 4 was previously described, but the Photometric Interpretations that used it have been retired.
All image planes shall have the same number of Rows (0028,0010), Columns (0028,0011), Bits Allocated (0028,0100), Bits Stored (0028,0101), High Bit (0028,0102), Pixel Representation (0028,0103), and Pixel Aspect Ratio (0028,0034).
### Note
Downsampled chrominance planes of a color Photometric Interpretation are a special case, e.g., for a Photometric Interpretation (0028,0004) of YBR_FULL_422. In such cases, Samples per Pixel (0028,0002) describes the nominal number of channels (i.e., 3), and does not reflect that two chrominance samples are shared between four luminance samples. For YBR_FULL_422, Rows (0028,0010) and Columns (0028,0011) describe the size of the luminance plane, not the downsampled chrominance planes.
The data in each pixel may be represented as a "Composite Pixel Code". If Samples per Pixel is one, the Composite Pixel Code is just the "n" bit pixel sample, where "n" = Bits Allocated. If Samples per Pixel is greater than one, Composite Pixel Code is a "k" bit concatenation of samples, where "k" = Bits Allocated multiplied by Samples per Pixel, and with the sample representing the vector color designated first in the Photometric Interpretation name comprising the most significant bits of the Composite Pixel Code, followed in order by the samples representing the next vector colors, with the sample representing the vector color designated last in the Photometric Interpretation name comprising the least significant bits of the Composite Pixel Code. For example, for Photometric Interpretation = "RGB", the most significant "Bits Allocated" bits contain the Red sample, the next "Bits Allocated" bits contain the Green sample, and the least significant "Bits Allocated" bits contain the Blue sample.
###### C.*******.2 Photometric Interpretation
The Value of Photometric Interpretation (0028,0004) specifies the intended interpretation of the image pixel data.
See [PS3.5](part05.html#PS3.5) for additional restrictions imposed by compressed Transfer Syntaxes.
See  Section 8.2.13 in  PS3.5   for constraints that apply when using DICOM Real-Time Video.
The following Values are defined. Other Values are permitted if supported by the Transfer Syntax but the meaning is not defined by this Standard.
**Defined Terms:**
MONOCHROME1
Pixel data represent a single monochrome image plane. The minimum sample value is intended to be displayed as white after any VOI gray scale transformations have been performed. See [PS3.4](part04.html#PS3.4). This Value may be used only when Samples per Pixel (0028,0002) has a Value of 1. May be used for pixel data in a Native (uncompressed) or Encapsulated (compressed) format; see  Section 8.2 in  PS3.5  .
MONOCHROME2
Pixel data represent a single monochrome image plane. The minimum sample value is intended to be displayed as black after any VOI gray scale transformations have been performed. See [PS3.4](part04.html#PS3.4). This Value may be used only when Samples per Pixel (0028,0002) has a Value of 1. May be used for pixel data in a Native (uncompressed) or Encapsulated (compressed) format; see  Section 8.2 in  PS3.5  .
PALETTE COLOR
Pixel data describe a color image with a single sample per pixel (single image plane). The pixel value is used as an index into each of the Red, Blue, and Green Palette Color Lookup Tables (0028,1101-1103&amp;1201-1203). This Value may be used only when Samples per Pixel (0028,0002) has a Value of 1. May be used for pixel data in a Native (uncompressed) or Encapsulated (compressed) format; see  Section 8.2 in  PS3.5  . When the Photometric Interpretation is Palette Color; Red, Blue, and Green Palette Color Lookup Tables shall be present.
RGB
Pixel data represent a color image described by red, green, and blue image planes. The minimum sample value for each color plane represents minimum intensity of the color. This Value may be used only when Samples per Pixel (0028,0002) has a Value of 3. Planar Configuration (0028,0006) may be 0 or 1. May be used for pixel data in a Native (uncompressed) or Encapsulated (compressed) format; see  Section 8.2 in  PS3.5  .
HSV
Retired. 
ARGB
Retired. 
CMYK
Retired. 
YBR_FULL
Pixel data represent a color image described by one luminance (Y) and two chrominance planes (CB and CR). This photometric interpretation may be used only when Samples per Pixel (0028,0002) has a Value of 3. May be used for pixel data in a Native (uncompressed) or Encapsulated (compressed) format; see  Section 8.2 in  PS3.5  . Planar Configuration (0028,0006) may be 0 or 1.
This Photometric Interpretation is primarily used with RLE compressed bit streams, for which the Planar Configuration (0028,0006) may be 0 or 1; see  Section 8.2.2 in  PS3.5   and  Section G.2 in  PS3.5  . When used in the US Image Module, the Planar Configuration (0028,0006) is required to be 1; see Section C.8.5.6.1.16 Planar Configuration.
Black is represented by Y equal to zero. The absence of color is represented by both CB and CR values equal to half full scale.
### Note
In the case where Bits Allocated (0028,0100) has Value of 8, half full scale is 128.
In the case where Bits Allocated (0028,0100) has a Value of 8 then the following equations convert between RGB and YCBCR Photometric Interpretation.
Y = +.2990R +.5870G +.1140B
CB= -.1687R -.3313G +.5000B + 128
CR= +.5000R -.4187G -.0813B + 128
### Note
The above is based on CCIR Recommendation 601-2 dated 1990.
YBR_FULL_422
The same as YBR_FULL except that the CB and CR values are sampled horizontally at half the Y rate and as a result there are half as many CB and CR values as Y values.
Planar Configuration (0028,0006) shall be 0. May be used for pixel data in a Native (uncompressed) or Encapsulated (compressed) format; see  Section 8.2 in  PS3.5  .
### Note
- This Photometric Interpretation is primarily used with JPEG compressed bit streams, but is also occasionally used for pixel data in a Native (uncompressed) format.
- Though the chrominance channels are downsampled, there are still nominally three channels, hence Samples per Pixel (0028,0002) has a Value of 3, not 2. I.e., for pixel data in a Native (uncompressed) format, the Value Length of Pixel Data (7FE0,0010) is not: Rows (0028,0010) * Columns (0028,0011) * Number of Frames (0028,0008) * Samples per Pixel (0028,0002) * ((Bits Allocated (0028,0100)-1)/8+1) padded to an even length, as it would otherwise be, but rather is: Rows (0028,0010) * Columns (0028,0011) * Number of Frames (0028,0008) * 2 * ((Bits Allocated (0028,0100)-1)/8+1) padded to an even length.
- When used to describe JPEG compressed bit streams, the chrominance sub-sampling in the JPEG bit stream may differ from this description. E.g., though many JPEG codecs produce only horizontally sub-sampled chrominance components (4:2:2), some sub-sample vertically as well (4:2:0). Though inaccurate, the use of YBR_FULL_422 to describe both has proven harmless. For a discussion of the sub-sampling notation, see [ Poynton 2008 ] .
Two Y values shall be stored followed by one CB and one CR value. The CB and CR values shall be sampled at the location of the first of the two Y values. For each Row of Pixels, the first CB and CR samples shall be at the location of the first Y sample. The next CB and CR samples shall be at the location of the third Y sample etc.
### Note
This subsampling sited on the even luminance pixels is often referred to as cosited sampling. The cositing applies when describing pixel data in a Native (uncompressed) form. When used to describe compressed bit streams, the siting depends on the compression scheme. E.g., for JPEG according to JFIF  [  ISO/IEC 10918-5  ] , the siting is midway between luminance samples, whereas for MPEG2  [  ISO/IEC 13818-2  ] , the sampling is cosited with the even luminance pixels. See also  [  Poynton 2008  ] .
YBR_PARTIAL_422
Retired. See [PS3.3-2017b](http://dicom.nema.org/MEDICAL/Dicom/2017b/output/pdf/part03.pdf).
YBR_PARTIAL_420
Pixel data represent a color image described by one luminance (Y) and two chrominance planes (CB and CR).
This photometric interpretation may be used only when Samples per Pixel (0028,0002) has a Value of 3. The CB and CR values are sampled horizontally and vertically at half the Y rate and as a result there are four times less CB and CR values than Y values.
Planar Configuration (0028,0006) shall be 0. Shall only be used for pixel data in an Encapsulated (compressed) format; see  Section 8.2 in  PS3.5  .
### Note
This Photometric Interpretation is primarily used with MPEG compressed bit streams. For a discussion of the sub-sampling notation and siting, see  [  Poynton 2008  ] .
Luminance and chrominance values are represented as follows:
- black corresponds to Y = 16;
- Y is restricted to 220 levels (i.e., the maximum value is 235);
- CB and CR each has a minimum value of 16;
- CB and CR are restricted to 225 levels (i.e., the maximum value is 240);
- lack of color is represented by CB and CR equal to 128.
In the case where Bits Allocated (0028,0100) has the Value of 8 then the following equations convert between RGB and YBR_PARTIAL_420 Photometric Interpretation
Y = +.2568R +.5041G +.0979B + 16
CB= -.1482R -.2910G +.4392B + 128
CR= +.4392R -.3678G -.0714B + 128
### Note
The above is based on CCIR Recommendation 601-2 dated 1990.
The CB and CR values shall be sampled at the location of the first of the two Y values. For the first Row of Pixels (etc.), the first CB and CR samples shall be at the location of the first Y sample. The next CB and CR samples shall be at the location of the third Y sample etc. The next Rows of Pixels containing CB and CR samples (at the same locations than for the first Row) will be the third etc.
YBR_ICT
Irreversible Color Transformation:
Pixel data represent a color image described by one luminance (Y) and two chrominance planes (CB and CR).
This photometric interpretation may be used only when Samples per Pixel (0028,0002) has a Value of 3. Planar Configuration (0028,0006) shall be 0. Shall only be used for pixel data in an Encapsulated (compressed) format; see  Section 8.2 in  PS3.5  .
### Note
This Photometric Interpretation is primarily used with JPEG 2000 compressed bit streams.
Black is represented by Y equal to zero. The absence of color is represented by both CB and CR values equal to zero.
Regardless of the Value of Bits Allocated (0028,0100), the following equations convert between RGB and YCBCR Photometric Interpretation.
Y = +.29900R +.58700G +.11400B
CB= -.16875R -.33126G +.50000B
CR= +.50000R -.41869G -.08131B
### Note
- The above is based on [ ISO/IEC 15444-1 ] (JPEG 2000).
- In a JPEG 2000 bit stream, DC level shifting (used if the untransformed components are unsigned) is applied before forward color transformation, and the transformed components may be signed (unlike in JPEG ISO/IEC 10918-1).
- In JPEG 2000, spatial down-sampling of the chrominance components, if performed, is signaled in the JPEG 2000 bit stream.
YBR_RCT
Reversible Color Transformation:
Pixel data represent a color image described by one luminance (Y) and two chrominance planes (CB and CR).
This photometric interpretation may be used only when Samples per Pixel (0028,0002) has a Value of 3. Planar Configuration (0028,0006) shall be 0. Shall only be used for pixel data in an Encapsulated (compressed) format; see  Section 8.2 in  PS3.5  .
### Note
This Photometric Interpretation is primarily used with JPEG 2000 compressed bit streams.
Black is represented by Y equal to zero. The absence of color is represented by both CB and CR values equal to zero.
Regardless of the Value of Bits Allocated (0028,0100), the following equations convert between RGB and YBR_RCT Photometric Interpretation.
Y = (R + 2G +B) / 4 (Note: mean floor)
CB= B - G
CR= R - G
The following equations convert between YBR_RCT and RGB Photometric Interpretation.
G = Y - (CR+ CB) / 4
R = CR+ G
B = CB+ G
### Note
- The above is based on [ ISO/IEC 15444-1 ] (JPEG 2000).
- In a JPEG 2000 bit stream, DC level shifting (used if the untransformed components are unsigned) is applied before forward color transformation, and the transformed components may be signed (unlike in JPEG ISO/IEC 10918-1).
- This photometric interpretation is a reversible approximation to the YUV transformation used in PAL and SECAM.
XYB
Pixel data represent a color image described by XYB, the long/medium/short wavelength (LMS) based color model inspired by the human visual system, facilitating perceptually uniform quantization. It uses a gamma of 3 for computationally efficient decoding. The exact details of the XYB encoding are defined as part of a specific image being encoded in order to optimize image fidelity. Images in XYB transcoded to other Transfer Syntaxes will use RGB or the appropriate equivalent (e.g., YBR_FULL_422 for JPEG).
### Note
This is a possible color space used in JPEG XL  [  ISO/IEC 18181-1  ] .
###### C.*******.3 Planar Configuration
Planar Configuration (0028,0006) indicates whether the color pixel data are encoded color-by-plane or color-by-pixel. This Attribute shall be present if Samples per Pixel (0028,0002) has a Value greater than 1. It shall not be present otherwise.
**Enumerated Values:**
0
The sample values for the first pixel are followed by the sample values for the second pixel, etc. For RGB images, this means the order of the pixel values encoded shall be R1, G1, B1, R2, G2, B2,, etc.
1
Each color plane shall be encoded contiguously. For RGB images, this means the order of the pixel values encoded is R1, R2, R3,, G1, G2, G3,, B1, B2, B3, etc.
### Note
Planar Configuration (0028,0006) is not meaningful when a compression Transfer Syntax is used that involves reorganization of sample components in the compressed bit stream. In such cases, since the Attribute is required to be present, then an appropriate value to use may be specified in the description of the Transfer Syntax in [PS3.5](part05.html#PS3.5), though in all likelihood the value of the Attribute will be ignored by the receiving implementation.
###### C.*******.4 Pixel Data
Pixel Data (7FE0,0010) for this image. The order of pixels encoded for each image plane is left to right, top to bottom, i.e., the upper left pixel (labeled 1,1) is encoded first followed by the remainder of row 1, followed by the first pixel of row 2 (labeled 2,1) then the remainder of row 2 and so on.
For multi-plane images see Planar Configuration (0028,0006) in this Section.
###### C.*******.5 Palette Color Lookup Table Descriptor
The three Values of Palette Color Lookup Table Descriptor (0028,1101-1104) describe the format of the Lookup Table Data in the corresponding Data Element (0028,1201-1204) or (0028,1221-1223). In this section, the term "input value" is either the Palette Color Lookup Table input value described in the Enhanced Palette Color Lookup Table Sequence (0028,140B) or if that Attribute is absent, the stored pixel value.
The first Palette Color Lookup Table Descriptor Value is the number of entries in the lookup table. When the number of table entries is equal to 2  16  then this Value shall be 0. The first Value shall be identical for each of the Red, Green, Blue and Alpha Palette Color Lookup Table Descriptors.
The second Palette Color Lookup Table Descriptor Value is the first input value mapped. This input value is mapped to the first entry in the Lookup Table Data. All input values less than the first value mapped are also mapped to the first entry in the Lookup Table Data if the Photometric Interpretation is PALETTE COLOR.
### Note
In the case of the Supplemental Palette Color LUT, the stored pixel values less than the second descriptor Value are grayscale values.
An input value one greater than the first value mapped is mapped to the second entry in the Lookup Table Data. Subsequent input values are mapped to the subsequent entries in the Lookup Table Data up to an input value equal to number of entries + first value mapped - 1, which is mapped to the last entry in the Lookup Table Data. Input values greater than or equal to number of entries + first value mapped are also mapped to the last entry in the Lookup Table Data. The second Value shall be identical for each of the Red, Green, Blue and Alpha Palette Color Lookup Table Descriptors.
The third Palette Color Lookup Table Descriptor Value specifies the number of bits for each entry in the Lookup Table Data. It shall take the Value of 8 or 16. The LUT Data shall be stored in a format equivalent to 8 bits allocated when the number of bits for each entry is 8, and 16 bits allocated when the number of bits for each entry is 16, where in both cases the high bit is equal to bits allocated-1. The third Value shall be identical for each of the Red, Green and Blue Palette Color Lookup Table Descriptors.
### Note
Some implementations have encoded 8 bit entries with 16 bits allocated, padding the high bits; this can be detected by comparing the number of entries specified in the LUT Descriptor with the actual Value Length of the LUT Data entry. The Value Length in bytes should equal the number of entries if bits allocated is 8, and be twice as long if bits allocated is 16.
When the Red, Green, or Blue Palette Color Lookup Table Descriptor (0028,1101-1103) are used as part of the Palette Color Lookup Table Module or the Supplemental Palette Color Lookup Table Module in an Image or Presentation State IOD, The third Value shall be equal to 16. When the Alpha Palette Color Lookup Table Descriptor (0028,1104) is used, The third Value shall be equal to 8.
When the Red, Green, or Blue Palette Color Lookup Table Descriptor (0028,1101-1103) are used as part of the Palette Color Lookup Table Module in a Color Palette IOD, the 3  rd  Value of Palette Color Lookup Table Descriptor (0028,1101-1103) (i.e, the number of bits for each entry in the Lookup Table Data) shall be 8.
When the Red, Green, or Blue Palette Color Lookup Table Descriptor (0028,1101-1103) are used as part of the Palette Color Lookup Table Module in the Segmentation IOD, the 3  rd  Value of Palette Color Lookup Table Descriptor (0028,1101-1103) (i.e, the number of bits for each entry in the Lookup Table Data) shall be 8 or 16.
### Note
- A Value of 16 indicates the Lookup Table Data will range from (0,0,0) minimum intensity to (65535,65535,65535) maximum intensity.
- Since the Palette Color Lookup Table Descriptor (0028,1101-1104) are multi-valued, in an Explicit VR Transfer Syntax, only one Value Representation (US or SS) may be specified, even though the first and third Values are always by definition interpreted as unsigned. The explicit VR actually used is dictated by the VR needed to represent the second Value, which will be consistent with Pixel Representation (0028,0103).
###### C.*******.6 Palette Color Lookup Table Data
Palette Color Lookup Table Data (0028,1201-1204) contain the lookup table data corresponding to the Lookup Table Descriptor (0028,1101-1104).
Palette color values must always be scaled across the full range of available intensities. This is indicated by the fact that there are no bits stored and high bit Values for palette color data.
### Note
For example, if there are 16 bits per entry specified and only 8 bits of value are truly used then the 8 bit intensities from 0 to 255 must be scaled to the corresponding 16 bit intensities from 0 to 65535. To do this for 8 bit values, simply replicate the Value in both the most and least significant bytes.
These lookup tables shall be used only when there is a single sample per pixel (single image plane) in the image.
###### C.*******.7 Pixel Aspect Ratio
The pixel aspect ratio is the ratio of the vertical size and horizontal size of the pixels in the image specified by a pair of integer values where the first Value is the vertical pixel size, and the second Value is the horizontal pixel size. To illustrate, consider the example pixel size shown in Figure C.*******.7-1
**Figure C.*******.7-1. Example of Pixel Size and Aspect Ratio**
Pixel Aspect Ratio = Vertical Size \ Horizontal Size = 0.30 mm \0.25 mm. Thus the Pixel Aspect Ratio could be represented as the multi-valued integer string "6\5", "60\50", or any equivalent integer ratio.
###### C.*******.8 Extended Offset Table
The Extended Offset Table (7FE0,0001) Value shall contain byte offsets to the first byte of the Item Tag of the one and only Fragment for every Frame in the Pixel Data (7FE0,0010) Sequence of this Instance.
The byte offsets are measured from the first byte of the first Item Tag following the empty (zero length) Basic Offset Table Item, i.e., the Item Tag of the one and only Fragment of the first Frame.
### Note
The offset is to the first byte of the Item Tag itself, not the first byte of the Value Field within the Item. See also the example of the Basic Offset Table in  Table A.4-2 in  PS3.5  .
If present, the first entry will always be 0000000000000000H.
### Note
The offset table is only present when the Pixel Data (7FE0,0010) is encoded in Encapsulated Format, since the position of uncompressed Frames encoded in Native Format can be computed from their known fixed length (i.e., derived from Rows (0028,0010), Columns (0028,0011), Samples per Pixel (0028,0002) and Bits Allocated (0028,0100)).
##### C.******* Image Pixel Macro
Table C.7-11b specifies the Attributes of the Section C.******* Image Pixel Macro, which describe and encode the pixel data of the image.
**Table C.7-11b. Image Pixel Macro Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Include Table C.7-11c Image Pixel Description Macro Attributes |   |   |   |
| Pixel Data | (7FE0,0010) | 1 | A data stream of the pixel samples that comprise the Image. See Section C.*******.4 for further explanation. |
##### C.******* Image Pixel Description Macro
Table C.7-11c specifies the Attributes of the Section C.******* Image Pixel Description Macro, which are the common Attributes that describe the pixel data of the image.
**Table C.7-11c. Image Pixel Description Macro Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Samples per Pixel | (0028,0002) | 1 | Number of samples (planes) in this image. See Section C.*******.1 for further explanation. |
| Photometric Interpretation | (0028,0004) | 1 | Specifies the intended interpretation of the pixel data. See Section C.*******.2 for further explanation. |
| Rows | (0028,0010) | 1 | Number of rows in the image. Shall be an exact multiple of the vertical downsampling factor if any of the samples (planes) are encoded downsampled in the vertical direction for pixel data encoded in a Native (uncompressed) format. E.g., required to be an even value for a Photometric Interpretation (0028,0004) of YBR_FULL_422. |
| Columns | (0028,0011) | 1 | Number of columns in the image. Shall be an exact multiple of the horizontal downsampling factor if any of the samples (planes) are encoded downsampled in the horizontal direction for pixel data encoded in a Native (uncompressed) format. E.g., required to be an even value for a Photometric Interpretation (0028,0004) of YBR_FULL_422. |
| Bits Allocated | (0028,0100) | 1 | Number of bits allocated for each pixel sample. Each sample shall have the same number of bits allocated. Bits Allocated (0028,0100) shall be either 1, or a multiple of 8. See [PS3.5](part05.html#PS3.5) for further explanation. |
| Bits Stored | (0028,0101) | 1 | Number of bits stored for each pixel sample. Each sample shall have the same number of bits stored. See [PS3.5](part05.html#PS3.5) for further explanation. |
| High Bit | (0028,0102) | 1 | Most significant bit for pixel sample data. Each sample shall have the same high bit. High Bit (0028,0102) shall be one less than Bits Stored (0028,0101). See [PS3.5](part05.html#PS3.5) for further explanation. |
| Pixel Representation | (0028,0103) | 1 | Data representation of the pixel samples. Each sample shall have the same pixel representation. **Enumerated Values:** 0000H unsigned integer. 0001H 2's complement |
| Planar Configuration | (0028,0006) | 1C | Indicates whether the pixel data are encoded color-by-plane or color-by-pixel. Required if Samples per Pixel (0028,0002) has a Value greater than 1. See Section C.*******.3 for further explanation. |
| Pixel Aspect Ratio | (0028,0034) | 1C | Ratio of the vertical size and horizontal size of the pixels in the image specified by a pair of integer values where the first Value is the vertical pixel size, and the second Value is the horizontal pixel size. Required if the aspect ratio values do not have a ratio of 1:1 and the physical pixel spacing is not specified by Pixel Spacing (0028,0030), or Imager Pixel Spacing (0018,1164) or Nominal Scanned Pixel Spacing (0018,2010), either for the entire Image or per-frame in a Functional Group Macro. See Section C.*******.7. |
| Smallest Image Pixel Value | (0028,0106) | 3 | The minimum actual pixel value encountered in this image. |
| Largest Image Pixel Value | (0028,0107) | 3 | The maximum actual pixel value encountered in this image. |
| Red Palette Color Lookup Table Descriptor | (0028,1101) | 1C | Specifies the format of the Red Palette Color Lookup Table Data (0028,1201). Required if Photometric Interpretation (0028,0004) has a Value of PALETTE COLOR or Pixel Presentation (0008,9205) at the image level equals COLOR or MIXED. See Section C.*******.5 for further explanation. |
| Green Palette Color Lookup Table Descriptor | (0028,1102) | 1C | Specifies the format of the Green Palette Color Lookup Table Data (0028,1202). Required if Photometric Interpretation (0028,0004) has a Value of PALETTE COLOR or Pixel Presentation (0008,9205) at the image level equals COLOR or MIXED. See Section C.*******.5 for further explanation. |
| Blue Palette Color Lookup Table Descriptor | (0028,1103) | 1C | Specifies the format of the Blue Palette Color Lookup Table Data (0028,1203). Required if Photometric Interpretation (0028,0004) has a Value of PALETTE COLOR or Pixel Presentation (0008,9205) at the image level equals COLOR or MIXED. See Section C.*******.5 for further explanation. |
| Red Palette Color Lookup Table Data | (0028,1201) | 1C | Red Palette Color Lookup Table Data. Required if Photometric Interpretation (0028,0004) has a Value of PALETTE COLOR or Pixel Presentation (0008,9205) at the image level equals COLOR or MIXED. See Section C.*******.6 for further explanation. |
| Green Palette Color Lookup Table Data | (0028,1202) | 1C | Green Palette Color Lookup Table Data. Required if Photometric Interpretation (0028,0004) has a Value of PALETTE COLOR or Pixel Presentation (0008,9205) at the image level equals COLOR or MIXED. See Section C.*******.6 for further explanation. |
| Blue Palette Color Lookup Table Data | (0028,1203) | 1C | Blue Palette Color Lookup Table Data. Required if Photometric Interpretation (0028,0004) has a Value of PALETTE COLOR or Pixel Presentation (0008,9205) at the image level equals COLOR or MIXED. See Section C.*******.6 for further explanation. |
| ICC Profile | (0028,2000) | 3 | An ICC Profile encoding the transformation of device-dependent color stored pixel values into PCS-Values. See Section C.11.15.1.1. When present, defines the color space of color Pixel Data (7FE0,0010) Values, and the output of Palette Color Lookup Table Data (0028,1201-1203). Shall not be present in the top level dataset when the Optical Path Sequence (0048,0105) is present. ### Note - The profile applies only to Pixel Data (7FE0,0010) at the same level of the Data Set and not to any icons nested within Sequences, which may or may not have their own ICC profile specified. - When the Optical Path Module is used, each optical path (Item of the Optical Path Sequence (0048,0105)) has its own ICC Profile (0028,2000). |
| Color Space | (0028,2002) | 3 | A label that identifies the well-known color space of the image. Shall be consistent with any ICC Profile (0028,2000) that is also present. Shall not be present when the Optical Path Sequence (0048,0105) is present. See Section C.11.15.1.2. |