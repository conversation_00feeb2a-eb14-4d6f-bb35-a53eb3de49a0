<div class="section">
 <div class="titlepage">
  <div>
   <div>
    <h4 class="title">
     <a id="sect_C.7.6.2" shape="rect">
     </a>
     C.7.6.2 Image Plane Module
    </h4>
   </div>
  </div>
 </div>
 <p>
  <a id="para_81876b9d-5726-4ab4-8b99-5c66f4d63024" shape="rect">
  </a>
  <a class="xref" href="#table_C.7-10" shape="rect" title="Table C.7-10. Image Plane Module Attributes">
   Table C.7-10
  </a>
  specifies the Attributes of the
  <a class="xref" href="#sect_C.7.6.2" shape="rect" title="C.7.6.2 Image Plane Module">
   Image Plane Module
  </a>, which define the transmitted pixel array of a two dimensional image plane in a three dimensional space.
 </p>
 <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
  <h3 class="title">
   Note
  </h3>
  <p>
   <a id="para_97f0c088-50a2-4da0-a356-96ea0ef2c51f" shape="rect">
   </a>
   In previous versions of the Standard (
   <a class="xref" href="#biblio_ACR_NEMA_300_1985" shape="rect" title="Digital Imaging and Communications">
    [
    <abbr class="abbrev">
     ACR-NEMA 300-1985
    </abbr>
    ]
   </a>,
   <a class="xref" href="#biblio_ACR_NEMA_300_1988" shape="rect" title="Digital Imaging and Communications">
    [
    <abbr class="abbrev">
     ACR-NEMA 300-1988
    </abbr>
    ]
   </a>
   ), image position and image orientation were specified relative to a specific equipment coordinate system. This equipment coordinate system was not fully defined and a number of ambiguities existed. The Equipment-Based Coordinate System has been retired and replaced by the Patient-Based Coordinate System defined in this Module.
  </p>
 </div>
 <div class="table">
  <a id="table_C.7-10" shape="rect">
  </a>
  <p class="title">
   <strong>
    Table C.7-10. Image Plane Module Attributes
   </strong>
  </p>
  <div class="table-contents">
   <table frame="box" rules="all">
    <thead>
     <tr valign="top">
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_4cb4e252-7aae-402b-9ce5-4b48608aa146" shape="rect">
        </a>
        Attribute Name
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_ef5a5a9a-c545-44ea-95c5-85d0959fcf6b" shape="rect">
        </a>
        Tag
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_baf8cfc4-de88-4f88-9243-d560be4bba91" shape="rect">
        </a>
        Type
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_a62c9195-44e6-45a8-9e82-7679caf887f8" shape="rect">
        </a>
        Attribute Description
       </p>
      </th>
     </tr>
    </thead>
    <tbody>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_09be5138-c425-4b3a-b186-4deffbb7cb52" shape="rect">
        </a>
        Pixel Spacing
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_5a4b15f5-db2c-47f7-8bf5-4dfeee0ac322" shape="rect">
        </a>
        (0028,0030)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_dd12f3eb-e241-4f80-a323-4048687813c0" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_0990d168-0f15-4a44-8425-628117447661" shape="rect">
        </a>
        Physical distance in the patient between the center of each pixel, specified by a numeric pair - adjacent row spacing (delimiter) adjacent column spacing in mm. See
        <a class="xref" href="#sect_10.7.1.3" shape="rect" title="10.7.1.3 Pixel Spacing Value Order and Valid Values">
         Section 10.7.1.3
        </a>
        for further explanation.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_5c1047cf-bfee-45ae-ac55-25d4fda02a33" shape="rect">
        </a>
        Image Orientation (Patient)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_a4038941-73c1-48fe-bdce-a76546089564" shape="rect">
        </a>
        (0020,0037)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_c0116d7d-f83a-4d4c-bb80-72a009b45edb" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_c3827cd7-5ffc-4532-a3fd-ded943f68397" shape="rect">
        </a>
        The direction cosines of the first row and the first column with respect to the patient. See
        <a class="xref" href="#sect_C.*******.1" shape="rect" title="C.*******.1 Image Position and Image Orientation">
         Section C.*******.1
        </a>
        for further explanation.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_4527eedd-9f70-4d9e-8b6d-1ca90e539ed3" shape="rect">
        </a>
        Image Position (Patient)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_7e757b55-0b83-4181-91fb-7d93c6656593" shape="rect">
        </a>
        (0020,0032)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_851d362b-8df2-4bc7-bf8f-3c897b58b945" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_ad2a2a3f-9c63-4aa4-aaee-4baa70930da4" shape="rect">
        </a>
        The x, y, and z coordinates of the upper left hand corner (center of the first voxel transmitted) of the image, in mm. See
        <a class="xref" href="#sect_C.*******.1" shape="rect" title="C.*******.1 Image Position and Image Orientation">
         Section C.*******.1
        </a>
        for further explanation.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_7f9e1890-b7bc-4f86-bf7d-e77beea6cbfe" shape="rect">
        </a>
        Slice Thickness
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_aa42af64-f519-4f41-adbf-8b8dc1916b60" shape="rect">
        </a>
        (0018,0050)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_11424cc2-4040-4fc8-8574-d7eba09bc376" shape="rect">
        </a>
        2
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_03dd7545-36c3-4140-aef3-09a375164753" shape="rect">
        </a>
        Nominal slice thickness, in mm.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_b463d699-93b3-4bd3-b362-2d109f1e5d3a" shape="rect">
        </a>
        Spacing Between Slices
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_4f2fbb33-e8b7-4f07-97ed-5a5a27857959" shape="rect">
        </a>
        (0018,0088)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_32b3afa9-4fa1-45d4-81cc-3df730c48485" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_9f54fd0a-bef3-48a9-97f8-8bdc2a43cb56" shape="rect">
        </a>
        Spacing between adjacent slices, in mm. The spacing is measured from the center-to-center of each slice.
       </p>
       <p>
        <a id="para_808f7f36-7db8-48d2-b9c7-a703307eadb4" shape="rect">
        </a>
        If present, shall not be negative, unless specialized to define the meaning of the sign in a specialized IOD, e.g., as in the
        <a class="xref" href="#sect_C.8.4.15" shape="rect" title="C.8.4.15 NM Reconstruction Module">
         Section C.8.4.15
        </a>.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_2b9e9c7e-1951-4216-8da5-170999d00061" shape="rect">
        </a>
        Slice Location
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_a8e14a02-4112-497e-a374-0121e5f52556" shape="rect">
        </a>
        (0020,1041)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_d3d4848f-4063-4e8a-9d55-4715be682ffb" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_f4902707-5a4a-43a9-a7de-3e43c7852e51" shape="rect">
        </a>
        Relative position of the image plane expressed in mm. See
        <a class="xref" href="#sect_C.*******.2" shape="rect" title="C.*******.2 Slice Location">
         Section C.*******.2
        </a>
        for further explanation.
       </p>
      </td>
     </tr>
    </tbody>
   </table>
  </div>
 </div>
 <br class="table-break" clear="none"/>
 <div class="section">
  <div class="titlepage">
   <div>
    <div>
     <h5 class="title">
      <a id="sect_C.*******" shape="rect">
      </a>
      C.******* Image Plane Module Attribute Descriptions
     </h5>
    </div>
   </div>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h6 class="title">
       <a id="sect_C.*******.1" shape="rect">
       </a>
       C.*******.1 Image Position and Image Orientation
      </h6>
     </div>
    </div>
   </div>
   <p>
    <a id="para_98a38bd3-9b8a-40ea-9146-391989cddce0" shape="rect">
    </a>
    Image Position (Patient) (0020,0032) specifies the x, y, and z coordinates of the upper left hand corner of the image; it is the center of the first voxel transmitted. Image Orientation (Patient) (0020,0037) specifies the direction cosines of the first row and the first column with respect to the patient. These Attributes shall be provide as a pair. Row value for the x, y, and z axes respectively followed by the Column value for the x, y, and z axes respectively.
   </p>
   <p>
    <a id="para_dccc8c6e-1d50-4af2-bf90-455b4d60cd24" shape="rect">
    </a>
    The direction of the axes is defined fully by the patient's orientation.
   </p>
   <p>
    <a id="para_549d985c-7571-421b-8e8c-a2211eb5c9af" shape="rect">
    </a>
    If Anatomical Orientation Type (0010,2210) is absent or has a Value of BIPED, the x-axis is increasing to the left hand side of the patient. The y-axis is increasing to the posterior side of the patient. The z-axis is increasing toward the head of the patient.
   </p>
   <p>
    <a id="para_3ef6de49-f656-4f53-87db-d422ebc73d57" shape="rect">
    </a>
    If Anatomical Orientation Type (0010,2210) has a Value of QUADRUPED, the
   </p>
   <div class="itemizedlist">
    <ul class="itemizedlist" style="list-style-type: disc; ">
     <li class="listitem">
      <p>
       <a id="para_bf72452f-8976-4948-9de8-4a42b10d5dfa" shape="rect">
       </a>
       x-axis is increasing to the left (as opposed to right) side of the patient
      </p>
     </li>
     <li class="listitem">
      <p>
       <a id="para_2df48a1d-13a2-4c94-8774-a2738f3f1777" shape="rect">
       </a>
       the y-axis is increasing towards
      </p>
      <div class="itemizedlist">
       <ul class="itemizedlist" style="list-style-type: circle; ">
        <li class="listitem">
         <p>
          <a id="para_e1e07170-a9be-4121-8f19-9bfe425640ce" shape="rect">
          </a>
          the dorsal (as opposed to ventral) side of the patient for the neck, trunk and tail,
         </p>
        </li>
        <li class="listitem">
         <p>
          <a id="para_c931a656-27e4-4d54-929b-f01bf248f7a3" shape="rect">
          </a>
          the dorsal (as opposed to ventral) side of the patient for the head,
         </p>
        </li>
        <li class="listitem">
         <p>
          <a id="para_9762f404-961a-4dac-a332-dd9a4f0b300c" shape="rect">
          </a>
          the dorsal (as opposed to plantar or palmar) side of the distal limbs,
         </p>
        </li>
        <li class="listitem">
         <p>
          <a id="para_18ee73b2-3e7d-4dae-9bdf-12410f4c236c" shape="rect">
          </a>
          the cranial (as opposed caudal) side of the proximal limbs, and
         </p>
        </li>
       </ul>
      </div>
     </li>
     <li class="listitem">
      <p>
       <a id="para_59a991a5-b078-4400-ae76-b4a3937f1e2b" shape="rect">
       </a>
       the z-axis is increasing towards
      </p>
      <div class="itemizedlist">
       <ul class="itemizedlist" style="list-style-type: circle; ">
        <li class="listitem">
         <p>
          <a id="para_f1681d77-f7e0-4a45-b928-d79200d67ddc" shape="rect">
          </a>
          the cranial (as opposed to caudal) end of the patient for the neck, trunk and tail,
         </p>
        </li>
        <li class="listitem">
         <p>
          <a id="para_db1f15e5-558f-46a9-88f9-c3c00f3bd9b4" shape="rect">
          </a>
          the rostral (as opposed to caudal) end of the patient for the head, and
         </p>
        </li>
        <li class="listitem">
         <p>
          <a id="para_69613051-b2e7-4a43-8c08-c064a856c371" shape="rect">
          </a>
          the proximal (as opposed to distal) end of the limbs
         </p>
        </li>
       </ul>
      </div>
     </li>
    </ul>
   </div>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <div class="orderedlist">
     <ol class="orderedlist" type="1">
      <li class="listitem">
       <p>
        <a id="para_6e746863-de82-4e80-b8ac-e3283974db3f" shape="rect">
        </a>
        The axes for quadrupeds are those defined and illustrated in Smallwood et al for proper anatomic directional terms as they apply to various parts of the body.
       </p>
      </li>
      <li class="listitem">
       <p>
        <a id="para_53174598-6588-41d8-88b6-5009df9750bd" shape="rect">
        </a>
        It should be anticipated that when quadrupeds are imaged on human equipment, and particularly when they are position in a manner different from the traditional human prone and supine head or feet first longitudinal position, then the equipment may well not indicate the correct orientation, though it will remain an orthogonal Cartesian right-handed system that could be corrected subsequently.
       </p>
      </li>
     </ol>
    </div>
   </div>
   <p>
    <a id="para_2a793374-f0eb-4e5e-8277-78c217d5ea59" shape="rect">
    </a>
    The Patient-Based Coordinate System is a right handed system, i.e., the vector cross product of a unit vector along the positive x-axis and a unit vector along the positive y-axis is equal to a unit vector along the positive z-axis.
   </p>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <p>
     <a id="para_2af9c379-9097-4144-94ec-c0a2886e6ed0" shape="rect">
     </a>
     If a patient is positioned parallel to the ground, in dorsal recumbency (i.e., for humans, face-up on the table), with the caudo-cranial (i.e., for humans, feet-to-head) direction the same as the front-to-back direction of the imaging equipment, the direction of the axes of this Patient-Based Coordinate System and the Equipment-Based Coordinate System in previous versions of this Standard will coincide.
    </p>
   </div>
   <p>
    <a id="para_d0ff808e-94de-4f7f-944b-78d3cbe28b4a" shape="rect">
    </a>
    The Image Plane Attributes, in conjunction with the Pixel Spacing Attribute, describe the position and orientation of the image slices relative to the Patient-Based Coordinate System. In each image Frame Image Position (Patient) (0020,0032) specifies the origin of the image with respect to the Patient-Based Coordinate System. RCS and Image Orientation (Patient) (0020,0037) Values specify the orientation of the image Frame rows and columns. The mapping of an integer (entire) pixel location (i,j) to the RCS is calculated as follows:
   </p>
   <p>
    <a id="para_88271808-e485-4906-bbea-b3ae874dde02" shape="rect">
    </a>
   </p>
   <div class="equation">
    <a id="equation_C.*******-1" shape="rect">
    </a>
    <p class="title">
     <strong>
      Equation C.*******-1. 
     </strong>
    </p>
    <div class="equation-contents">
     <object data="figures/part03_withmml_image_1.svg" style="" type="image/svg+xml">
      <param name="src" value="image_1.svg"/>
     </object>
    </div>
   </div>
   <p>
    <br class="equation-break" clear="none"/>
   </p>
   <p>
    <a id="para_add40375-1797-44d5-98a4-15bfbe53f00b" shape="rect">
    </a>
    Where:
   </p>
   <div class="itemizedlist">
    <ul class="itemizedlist" style="list-style-type: none; ">
     <li class="listitem" style="list-style-type: none">
      <p>
       <a id="para_c7da7dd0-4444-473e-9364-02df2fc470b1" shape="rect">
       </a>
       P
       <sub>
        xyz
       </sub>
       The coordinates of the voxel (i,j) in the Frame's image plane in units of mm.
      </p>
     </li>
     <li class="listitem" style="list-style-type: none">
      <p>
       <a id="para_ee610cf1-1dd1-4ce6-8806-6e542f29b23a" shape="rect">
       </a>
       S
       <sub>
        xyz
       </sub>
       The three Values of Image Position (Patient) (0020,0032). It is the location in mm from the origin of the RCS.
      </p>
     </li>
     <li class="listitem" style="list-style-type: none">
      <p>
       <a id="para_e5fde752-b6d6-49d4-9a9a-d1662fd2ae15" shape="rect">
       </a>
       X
       <sub>
        xyz
       </sub>
       The Values from the row (X) direction cosine of Image Orientation (Patient) (0020,0037).
      </p>
     </li>
     <li class="listitem" style="list-style-type: none">
      <p>
       <a id="para_dc9b8632-3ca2-40fe-a62d-59828edb4e18" shape="rect">
       </a>
       Y
       <sub>
        xyz
       </sub>
       The Values from the column (Y) direction cosine of Image Orientation (Patient) (0020,0037).
      </p>
     </li>
     <li class="listitem" style="list-style-type: none">
      <p>
       <a id="para_18cd9c68-c639-4cec-945e-68c9ad811aad" shape="rect">
       </a>
       <span class="italic">
        i
       </span>
       Column integer index to the image plane. The first (entire) column is index zero.
      </p>
     </li>
     <li class="listitem" style="list-style-type: none">
      <p>
       <a id="para_7c2e3e50-5c41-4026-8798-fe39b9810525" shape="rect">
       </a>
       <span class="italic">
        
        <sub>
         i
        </sub>
       </span>
       Column pixel resolution of Pixel Spacing (0028,0030) in units of mm.
      </p>
     </li>
     <li class="listitem" style="list-style-type: none">
      <p>
       <a id="para_0c4cd4b5-5b00-43f9-808f-f42efa99ebcc" shape="rect">
       </a>
       <span class="italic">
        j
       </span>
       Row integer index to the image plane. The first (entire) row index is zero.
      </p>
     </li>
     <li class="listitem" style="list-style-type: none">
      <p>
       <a id="para_8b1bef6c-e126-470a-8f7b-368c7cd7c886" shape="rect">
       </a>
       <span class="italic">
        
        <sub>
         j
        </sub>
       </span>
       Row pixel resolution of Pixel Spacing (0028,0030) in units of mm.
      </p>
     </li>
    </ul>
   </div>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <p>
     <a id="para_a85c5ee6-c138-4257-a6ed-b24d656f7308" shape="rect">
     </a>
     The integer entire row and column indices (i,j) that are the input to this equation start from zero, which is a common mathematical convention. Many DICOM Attributes define such indices as starting from one, e.g., those affected by Bounding Box Annotation Units (0070,0003) for PIXEL and MATRIX in
     <a class="xref" href="#sect_C.10.5" shape="rect" title="C.10.5 Graphic Annotation Module">
      Section C.10.5 Graphic Annotation Module
     </a>. This needs to be accounted for when applying this equation literally.
    </p>
   </div>
   <p>
    <a id="para_b964f287-3fd3-4b20-9d2c-8aaf0126386c" shape="rect">
    </a>
    The mapping of a sub-pixel resolution image or total pixel matrix relative location (c,r), such as used in
    <a class="xref" href="#sect_C.18.6" shape="rect" title="C.18.6 Spatial Coordinates Macro">
     Spatial Coordinates Macro
    </a>, to the RCS is calculated as follows
   </p>
   <p>
    <a id="para_708e3bf5-ed6e-46b7-b195-cc370b7fb1a7" shape="rect">
    </a>
   </p>
   <div class="equation">
    <a id="equation_C.*******-2" shape="rect">
    </a>
    <p class="title">
     <strong>
      Equation C.*******-2. 
     </strong>
    </p>
    <div class="equation-contents">
     <object data="figures/part03_withmml_image_2.svg" style="" type="image/svg+xml">
      <param name="src" value="image_2.svg"/>
     </object>
    </div>
   </div>
   <p>
    <br class="equation-break" clear="none"/>
   </p>
   <p>
    <a id="para_33a0da7d-6dc0-4f50-91f9-8bbfde43bb7c" shape="rect">
    </a>
    Where:
   </p>
   <div class="itemizedlist">
    <ul class="itemizedlist" style="list-style-type: none; ">
     <li class="listitem" style="list-style-type: none">
      <p>
       <a id="para_bb37a8c1-290b-4777-9baf-c4a4c96941b4" shape="rect">
       </a>
       P
       <sub>
        xyz
       </sub>
       The coordinates of the voxel (c,r) in the Frame's image plane in units of mm.
      </p>
     </li>
     <li class="listitem" style="list-style-type: none">
      <p>
       <a id="para_9f786281-68bd-4699-8104-a8d4c1cf0353" shape="rect">
       </a>
       S
       <sub>
        xyz
       </sub>
       The three Values of Image Position (Patient) (0020,0032). It is the location in mm from the origin of the RCS.
      </p>
     </li>
     <li class="listitem" style="list-style-type: none">
      <p>
       <a id="para_2a69b1cb-ac78-4692-bc1d-50320941a6aa" shape="rect">
       </a>
       X
       <sub>
        xyz
       </sub>
       The Values from the row (X) direction cosine of Image Orientation (Patient) (0020,0037).
      </p>
     </li>
     <li class="listitem" style="list-style-type: none">
      <p>
       <a id="para_5a6fc03d-08bc-4f28-9e82-6b6c5b2f403d" shape="rect">
       </a>
       Y
       <sub>
        xyz
       </sub>
       The Values from the column (Y) direction cosine of Image Orientation (Patient) (0020,0037).
      </p>
     </li>
     <li class="listitem" style="list-style-type: none">
      <p>
       <a id="para_c8942f96-7f4d-4b48-bb4c-9ffa4abb9776" shape="rect">
       </a>
       <span class="italic">
        c
       </span>
       Column sub-pixel resolution index to the image plane. The left pixel edge of the first column of the Frame or total pixel matrix is index zero.
      </p>
     </li>
     <li class="listitem" style="list-style-type: none">
      <p>
       <a id="para_9ad0a855-8f3b-4007-8975-d295b47ba97c" shape="rect">
       </a>
       <span class="italic">
        
        <sub>
         c
        </sub>
       </span>
       Column pixel resolution of Pixel Spacing (0028,0030) in units of mm.
      </p>
     </li>
     <li class="listitem" style="list-style-type: none">
      <p>
       <a id="para_5bc51836-3f3a-49e1-9371-45f37f1582b2" shape="rect">
       </a>
       <span class="italic">
        r
       </span>
       Row sub-pixel resolution index to the image plane. The top pixel edge of the first row of the Frame or total pixel matrix index is zero.
      </p>
     </li>
     <li class="listitem" style="list-style-type: none">
      <p>
       <a id="para_8844b4c8-112e-4c50-b59a-11588ef513e5" shape="rect">
       </a>
       <span class="italic">
        
        <sub>
         r
        </sub>
       </span>
       Row pixel resolution of Pixel Spacing (0028,0030) in units of mm.
      </p>
     </li>
    </ul>
   </div>
   <p>
    <a id="para_84114e14-20ae-43a8-8b7b-1b46aeac5bb0" shape="rect">
    </a>
    Additional constraints apply:
   </p>
   <div class="orderedlist">
    <ol class="orderedlist" type="1">
     <li class="listitem">
      <p>
       <a id="para_f4d326fd-cdf4-4735-a51a-189c60a28e4e" shape="rect">
       </a>
       The row and column direction cosine vectors shall be orthogonal, i.e., their dot product shall be zero.
      </p>
     </li>
     <li class="listitem">
      <p>
       <a id="para_db6104bc-5f2c-4f51-94f7-46a7fc2142a1" shape="rect">
       </a>
       The row and column direction cosine vectors shall be normal, i.e., the dot product of each direction cosine vector with itself shall be unity.
      </p>
     </li>
    </ol>
   </div>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h6 class="title">
       <a id="sect_C.*******.2" shape="rect">
       </a>
       C.*******.2 Slice Location
      </h6>
     </div>
    </div>
   </div>
   <p>
    <a id="para_b94f410c-b405-4db9-9208-ab69ab3aeacb" shape="rect">
    </a>
    Slice Location (0020,1041) is defined as the relative position of the image plane expressed in mm. This information is relative to an unspecified implementation specific reference point.
   </p>
  </div>
 </div>
</div>
