#### C.7.10.1 General Acquisition Module
Table C.7.10.1-1 specifies the Attributes of the General Acquisition Module, which identify and describe general information about an Acquisition.
**Table C.7.10.1-1. General Acquisition Module Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Acquisition UID | (0008,0017) | 3 | Unique identification of the single continuous gathering of data over a period of time that resulted in this instance. |
| Acquisition Number | (0020,0012) | 3 | A number identifying the single continuous gathering of data over a period of time that resulted in this instance. |
| Acquisition Date | (0008,0022) | 3 | The date the acquisition of data that resulted in this instance started. |
| Acquisition Time | (0008,0032) | 3 | The time the acquisition of data that resulted in this instance started. |
| Acquisition DateTime | (0008,002A) | 3 | The date and time that the acquisition of data that resulted in this instance started. ### Note The synchronization of this time with an external clock is specified in the Synchronization Module in Acquisition Time Synchronized (0018,1800). |
| Acquisition Duration | (0018,9073) | 3 | Duration of the single continuous gathering of data over a period of time that resulted in this instance, in seconds. ### Note E.g., to acquire the data for an image, such as for an MR image, to run the prescribed pulse sequence. |
| Images in Acquisition | (0020,1002) | 3 | Number of images that resulted from this acquisition of data. |
| Irradiation Event UID | (0008,3010) | 3 | Unique identification of the irradiation event(s) associated with the acquisition of this instance. See Section C.********.1. ### Note There is not necessarily a 1:1 relationship between an Irradiation Event and an Acquisition. An Acquisition may not involve the use of ionizing radiation, in which case this Attribute would be absent. A single Acquisition may result from more than one Irradiation Event, e.g., when there are multiple X-Ray sources. More than one Irradiation Event may be involved in a single Acquisition, e.g., when there is a quiescent period between Irradiation Events during which data gathering continues. |
##### C.******** General Acquisition Module Attribute Descriptions
###### C.********.1 Irradiation Event UID
An irradiation event is the loading of X-Ray equipment caused by a single continuous actuation of the equipment's irradiation switch, from the start of the loading time of the first pulse until the loading time trailing edge of the final pulse. Any on-off switching of the irradiation source during the event shall not be treated as separate events, rather the event includes the time between start and stop of irradiation as triggered by the user. E.g., a pulsed fluoro X-Ray acquisition shall be treated as a single irradiation event.