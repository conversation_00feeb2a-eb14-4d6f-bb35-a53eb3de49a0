#### C.7.6.1 General Image Module
Table C.7-9 specifies the Attributes of the General Image Module, which identify and describe an image within a particular Series.
**Table C.7-9. General Image Module Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Instance Number | (0020,0013) | 2 | A number that identifies this image. ### Note This Attribute was named Image Number in previous releases of this Standard. |
| Patient Orientation | (0020,0020) | 2C | Patient direction of the rows and columns of the image. Required if image does not require Image Orientation (Patient) (0020,0037) and Image Position (Patient) (0020,0032) or if image does not require Image Orientation (Slide) (0048,0102). May be present otherwise. See Section C.*******.1 for further explanation. ### Note IODs may have Attributes other than Patient Orientation, Image Orientation, or Image Position (Patient) to describe orientation in which case this Attribute will be zero length. |
| Content Date | (0008,0023) | 2C | The date the image pixel data creation started. Required if image is part of a Series in which the images are temporally related. May be present otherwise. ### Note This Attribute was formerly known as Image Date. |
| Content Time | (0008,0033) | 2C | The time the image pixel data creation started. Required if image is part of a Series in which the images are temporally related. May be present otherwise. |
| Image Type | (0008,0008) | 3 | Image identification characteristics. See Section C.*******.2 for Defined Terms and further explanation. |
| Image Comments | (0020,4000) | 3 | User-defined comments about the image. |
| Quality Control Image | (0028,0300) | 3 | Indicates whether or not quality control material (such as calibration or control material, or a phantom) is present in this image. **Enumerated Values:** YES the image contains only quality control material NO the image does not contain quality control material BOTH the image contains both subject (patient) and quality control information If this Attribute is absent, then the image may or may not be a quality control or phantom image. The phantom device or quality control material in the image can be described using the Device Module. See Section C.7.6.12. ### Note Examples of the presence of both subject and quality control information include: - presence of objects of known density in radiographic images for calibration - presence of control material for immunohistochemistry within slide images |
| Burned In Annotation | (0028,0301) | 3 | Indicates whether or not image contains sufficient burned in annotation to identify the patient and date the image was acquired. **Enumerated Values:** YES NO If this Attribute is absent, then the image may or may not contain burned in annotation. |
| Recognizable Visual Features | (0028,0302) | 3 | Indicates whether or not the image contains sufficiently recognizable visual features to allow the image or a reconstruction from a set of images to identify the Patient. **Enumerated Values:** YES NO If this Attribute is absent, then the image may or may not contain recognizable visual features. |
| Lossy Image Compression | (0028,2110) | 3 | Specifies whether an Image has undergone lossy compression (at a point in its lifetime). **Enumerated Values:** 00 Image has not been subjected to lossy compression. 01 Image has been subjected to lossy compression. Once this Attribute has been set to a Value of "01" it shall not be reset. See Section C.*******.5. |
| Lossy Image Compression Ratio | (0028,2112) | 3 | Describes the approximate lossy compression ratio(s) that have been applied to this image. See Section C.*******.5.2. |
| Lossy Image Compression Method | (0028,2114) | 3 | A label for the lossy compression method(s) that have been applied to this image. See Section C.*******.5.1. |
| Icon Image Sequence | (0088,0200) | 3 | This icon image is representative of the Image. Only a single Item is permitted in this Sequence. |
| &gt;Include Table C.7-11b Image Pixel Macro Attributes |   |   | See Section C.*******.6 for further explanation. |
| Presentation LUT Shape | (2050,0020) | 3 | When present, specifies an identity transformation for the Presentation LUT such that the output of all grayscale transformations, if any, are defined to be in P-Values. **Enumerated Values:** IDENTITY output is in P-Values - shall be used if Photometric Interpretation (0028,0004) is MONOCHROME2 or any color photometric interpretation. INVERSE output after inversion is in P-Values - shall be used if Photometric Interpretation (0028,0004) is MONOCHROME1. When this Attribute is used with a color photometric interpretation then the luminance component is in P-Values. |
| Real World Value Mapping Sequence | (0040,9096) | 3 | The mapping of stored values to associated Real World values. One or more Items are permitted in this Sequence. |
| &gt;Include Table C.7.6.16-12b Real World Value Mapping Item Macro Attributes |   |   |   |
| Image Laterality | (0020,0062) | 3 | Laterality of (possibly paired) body part (as described in Anatomic Region Sequence (0008,2218)) examined. **Enumerated Values:** R right L left U unpaired B both left and right Shall be consistent with any laterality information contained in Primary Anatomic Structure Modifier Sequence (0008,2230) and/or Laterality (0020,0060), if present. ### Note Laterality (0020,0060) is a Series level Attribute and must be the same for all Images in the Series, hence it must be absent if Image Laterality (0020,0062) has different Values for Images in the same Series. |
| Include Table 10-7 General Anatomy Optional Macro Attributes |   |   | Anatomic Region Sequence B [CID 4031 Common Anatomic Region](part16.html#sect_CID_4031) for humans. Anatomic Region Sequence B [CID 7483 Common Anatomic Regions for Animal](part16.html#sect_CID_7483) for non-human organisms. |
### Note
Previous releases of this Standard specified use of the Referenced Waveform Sequence (0008,113A), but that use has been superseded by Referenced Instance Sequence (0008,114A). See [PS3.3-2004](http://dicom.nema.org/MEDICAL/Dicom/2004/printed/04_03pu3.pdf).
##### C.******* General Image Module Attribute Descriptions
###### C.*******.1 Patient Orientation
Patient Orientation (0020,0020) relative to the image plane shall be specified by two Values that designate the anatomical direction of the positive row axis (left to right) and the positive column axis (top to bottom). The first entry is the direction of the rows, given by the direction of the last pixel in the first row from the first pixel in that row. The second entry is the direction of the columns, given by the direction of the last pixel in the first column from the first pixel in that column. Shall be consistent with Image Orientation (Patient) (0020,0037), if both Attributes are present and Patient Orientation (0020,0020) is not zero length.
If Anatomical Orientation Type (0010,2210) is absent or has a Value of BIPED, anatomical direction shall be designated by abbreviations using the capital letters:
- A (anterior)
- P (posterior)
- R (right)
- L (left)
- H (head)
- F (foot)
If Anatomical Orientation Type (0010,2210) has a Value of QUADRUPED, anatomical direction shall be designated by the abbreviations using capital letters:
- LE (Le or Left)
- RT (Rt or Right)
- D (Dorsal)
- V (Ventral)
- CR (Cr or Cranial)
- CD (Cd or Caudal)
- R (Rostral)
- M (Medial)
- L (Lateral)
- PR (Pr or Proximal)
- DI (Di or Distal)
- PA (Pa or Palmar)
- PL (Pl or Plantar)
### Note
- These abbreviations are capitalized versions of those defined in Smallwood et al for describing radiographic projections. Because of the Code String (CS) Value Representation of Patient Orientation (0020,0020), lowercase letters cannot be used.
- It is unfortunate that the conventional veterinary abbreviations (e.g., R for rostral and Rt for right) differ from those chosen for humans for DICOM usage (e.g., R for right), but confusion with in the respective human and non-human organism domains will be reduced. Hanging protocols may need to account for the difference by checking for the correct species.
- Smallwood et al define an O (Oblique) abbreviation, which is useful for describing radiographic projections, but do not specify its use for directional terms, and hence it is not included here for describing the row and column directions.
- The terms "anterior" and "posterior" are commonly used in vertebrate zoology to describe the cranial and caudal directions respectively, the veterinary terms are used in preference here, also in order to avoid confusion with the contradictory human use of anterior and posterior to mean ventral and dorsal.
- For non-human organisms other than quadrupeds, for example, birds and fish, it is anticipated that the same nomenclature can be logically extended to describe, for example, wings and fins.
Each Value of the orientation Attribute shall contain at least one of these abbreviations. If refinements in the orientation descriptions are to be specified, then they shall be designated by one or two additional abbreviations in each Value. Within each Value, the abbreviations shall be ordered with the principal orientation designated in the first abbreviations.
### Note
- For bipeds, since each abbreviation is a single character, no delimiter is required within a single Value and none is used. For quadrupeds, though lowercase letters cannot be used, delimiters are not necessary within a single Value to eliminate ambiguity, since the abbreviations used are sufficiently distinct, and can be parsed from left to right with a single character of lookahead.
- E.g., a medio-lateral oblique projection of the left breast of a human might be encoded with Patient Orientation Values of "A\FR" rather than "A\F", since the plane is obliquely inclined such that the columns are directed both downwards and medially, which for a left breast is towards the right, though the downwards direction is the principal column orientation.
- E.g., a right dorsal-left ventral oblique view of a quadruped's abdomen might be encoded with Patient Orientation Values of "LEV\CD", rather than "LE\CD", since the plane is obliquely inclined such that the rows are directed both to the left and ventrally, though the left direction is the principal row orientation. The abbreviations "LEV", "LE" and "CD", correspond to the designations in Smallwood et al of "LeV", "Le" and "Cd", respectively
###### C.*******.2 Image Type
Image Type (0008,0008) identifies important image identification characteristics. These characteristics are:
- Pixel Data Characteristics is the image an ORIGINAL Image; an image whose pixel values are based on original or source data
- is the image a DERIVED Image; an image whose pixel values have been derived in some manner from the pixel value of one or more other images
- Patient Examination Characteristics is the image a PRIMARY Image; an image created as a direct result of the patient examination
- is the image a SECONDARY Image; an image created after the initial patient examination
- Modality Specific Characteristics
- Implementation specific identifiers; other implementation specific identifiers shall be documented in an implementation's conformance statement.
The Image Type Attribute is multi-valued and shall be provided in the following manner:
- Value 1 shall identify the Pixel Data Characteristics **Enumerated Values:** ORIGINAL identifies an Original Image DERIVED identifies a Derived Image
- Value 2 shall identify the Patient Examination Characteristics **Enumerated Values:** PRIMARY identifies a Primary Image SECONDARY identifies a Secondary Image
- Value 3 shall identify any Image IOD specific specialization (optional)
- Other Values that are implementation specific (optional)
Any of the optional Values (Value 3 and beyond) may be encoded either with a value or zero-length, independent of other optional Values, unless otherwise specified by a specialization of this Attribute in an IOD.
If the pixel data of the derived Image is different from the pixel data of the source images and this difference is expected to affect professional interpretation of the image, the Derived Image shall have a SOP Instance UID (0008,0018) different than all the source images.
###### C.*******.3 Derivation Description
See Section C.********.
###### C.*******.4 Source Image Sequence
Source Image Sequence (0008,2112) was formerly used in this Module but has been moved to the General Reference Module. See Section C.******** Source Image Sequence.
###### C.*******.5 Lossy Image Compression
The Attribute Lossy Image Compression (0028,2110) conveys that the Image has undergone lossy compression. It provides a means to record that the Image has been compressed (at a point in its lifetime) with a lossy algorithm and changes have been introduced into the pixel data. Once this Attribute has been set to a Value of "01" it shall not be reset.
### Note
If an image is compressed with a lossy algorithm, the Attribute Lossy Image Compression (0028,2110) is set to "01". Subsequently, if the image is decompressed and transferred in uncompressed format, this Attribute Value remains "01".
The Value of Lossy Image Compression (0028,2110) in SOP Instances containing multiple Frames in which one or more of the Frames have undergone lossy compression shall be "01".
### Note
It is recommended that the applicable Frames be noted in the Attribute Derivation Description (0008,2111).
If an image is originally obtained as a lossy compressed image from the sensor, then Lossy Image Compression (0028,2110) is set to "01" and Value 1 of the Attribute Image Type (0008,0008) shall be set to ORIGINAL.
If an image is a compressed version of another image, Lossy Image Compression (0028,2110) is set to "01", Value 1 of the Attribute Image Type (0008,0008) shall be set to DERIVED, and if the predecessor was a DICOM image, then the Image shall receive a new SOP Instance UID.
### Note
- It is recommended that the approximate compression ratio be provided in the Attribute Derivation Description (0008,2111). Furthermore, it is recommended that Derivation Description (0008,2111) be used to indicate when pixel data changes might affect professional interpretation (see Section C.******** ).
- The Attribute Lossy Image Compression (0028,2110) is defined as Type 3 for backward compatibility with existing IODs. It is expected to be required (i.e., defined as Type 1C) for new Image IODs and for existing IODs that undergo a major revision (e.g., a new IOD is specified).
###### C.*******.5.1 Lossy Image Compression Method
Lossy Image Compression Method (0028,2114) may be multi-valued if successive lossy compression steps have been applied; the order of the Values shall correspond to the Values of Lossy Image Compression Ratio (0028,2112), if present.
**Defined Terms for Lossy Image Compression Method (0028,2114):**
ISO_10918_1
JPEG Lossy Compression  [  ISO/IEC 10918-1  ] 
ISO_14495_1
JPEG-LS Near-lossless Compression  [  ISO/IEC 14495-1  ] 
ISO_15444_1
JPEG 2000 Irreversible Compression  [  ISO/IEC 15444-1  ] 
ISO_15444_15
High-Throughput JPEG 2000 Irreversible Compression  [  ISO/IEC 15444-15  ] 
ISO_18181_1
JPEG XL Image Coding System - Part 1 Core Coding System  [  ISO/IEC 18181-1  ] 
ISO_13818_2
MPEG2 Compression  [  ISO/IEC 13818-2  ] 
ISO_14496_10
MPEG-4 AVC/H.264 Compression  [  ISO/IEC 14496-10  ] 
ISO_23008_2
HEVC/H.265 Lossy Compression  [  ISO/IEC 23008-2  ] 
###### C.*******.5.2 Lossy Image Compression Ratio
The value of the "compression ratio" is encoded as a numeric value that represents the numerator of an implicit ratio in which the denominator is always one, consistent with the traditional representation in the literature.
### Note
For example, a compression ratio of 30:1 would be described with a Value of 30.
The value may be an estimate (e.g., the nominal value that is supplied to the compressor), or it may be a measured value (e.g., computed by dividing the uncompressed pixel data size by the size of the compressed bit stream).
Lossy Image Compression Ratio (0028,2112) may be multi-valued if successive lossy compression steps have been applied; if so, the order of the Values shall correspond to the multiple Values of Lossy Image Compression Method (0028,2114), if present.
### Note
For historical reasons, the lossy compression ratio should also be described in Derivation Description (0008,2111).
###### C.*******.6 Icon Image Sequence
An Icon Image may be used as a key representative of an Image. It is defined as a Sequence that contains a single Item encapsulating the Data Set made of the Data Elements of the Icon Image. The Data Elements are defined by the Section C.******* Image Pixel Macro. Unless otherwise specified in the Module or Macro table where the Icon Image Sequence (0088,0200) is used, the following restrictions shall apply on the Image Pixel Macro usage:
- Only monochrome and palette color images shall be used. Samples per Pixel (0028,0002) shall have a Value of 1, Photometric Interpretation (0028,0004) shall have a Value of either MONOCHROME 1, MONOCHROME 2 or PALETTE COLOR, Planar Configuration (0028,0006) shall not be present. ### Note True color icon images are not supported. This is due to the fact that the reduced size of the Icon Image makes the quality of a palette color image (with 256 colors) sufficient in most cases.
- There is no explicit limitation on the size of an Icon Image specified by Rows (0028,0010) and Columns (0028,0011).
- Pixel samples shall have a Value of either 1 or 8 for Bits Allocated (0028,0100) and Bits Stored (0028,0101). High Bit (0028,0102) shall have a Value of one less than the Value used in Bit Stored.
- Pixel Representation (0028,0103) shall specify an unsigned integer representation (Value 0000H).
- The pixels shall be square (i.e., their aspect ratio shall be 1:1) and therefore Pixel Aspect Ratio (0028,0034) shall not be present.
- If a Palette Color lookup Table is used, Bits Allocated (0028,0100) shall have a Value of 8.
###### C.*******.7 Irradiation Event UID
See Section C.7.10.1.1.1.