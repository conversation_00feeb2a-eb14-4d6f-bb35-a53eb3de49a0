<div class="section">
 <div class="titlepage">
  <div>
   <div>
    <h4 class="title">
     <a id="sect_C.8.8.6" shape="rect">
     </a>
     C.8.8.6 ROI Contour Module
    </h4>
   </div>
  </div>
 </div>
 <p>
  <a id="para_d5e99224-1d6e-4056-b725-85b11223bc59" shape="rect">
  </a>
  In general, a ROI can be defined by either a sequence of overlays or a sequence of contours. This Module, if present, is used to define the ROI as a set of contours. Each ROI contains a sequence of one or more contours, where a contour is either a single point (for a point ROI) or more than one point (representing an open or closed polygon).
 </p>
 <div class="table">
  <a id="table_C.8-42" shape="rect">
  </a>
  <p class="title">
   <strong>
    Table C.8-42. ROI Contour Module Attributes
   </strong>
  </p>
  <div class="table-contents">
   <table frame="box" rules="all">
    <thead>
     <tr valign="top">
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_f3653f23-e05d-40d1-95e1-065a0fb2bae6" shape="rect">
        </a>
        Attribute Name
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_c75214f0-9953-4be2-93a5-596aa7387226" shape="rect">
        </a>
        Tag
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_423d0c7e-0713-4110-96da-1bb5f5d401eb" shape="rect">
        </a>
        Type
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_8463cf31-e9a2-490f-993f-9b67b0d0ceeb" shape="rect">
        </a>
        Attribute Description
       </p>
      </th>
     </tr>
    </thead>
    <tbody>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_3fab2aee-8023-4adb-8591-c8d58fdd37c4" shape="rect">
        </a>
        ROI Contour Sequence
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_30ba12d1-dd2c-47f4-b792-72d41e10c630" shape="rect">
        </a>
        (3006,0039)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="3">
       <p>
        <a id="para_c774c1e4-6d47-41bd-aeac-246c3b75602a" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_de60780f-19d5-4974-b7d1-a53c8197f786" shape="rect">
        </a>
        Sequence of Contour Sequences defining ROIs.
       </p>
       <p>
        <a id="para_5bfff524-a4c5-407a-8c7d-f3b6efaeafd3" shape="rect">
        </a>
        One or more Items are permitted in this Sequence.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_a5361281-0162-476a-a91a-b06f3fd4ac6a" shape="rect">
        </a>
        &gt;Referenced ROI Number
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_ea17c8bc-8bec-41a6-8afb-777f550401ec" shape="rect">
        </a>
        (3006,0084)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_bac1d044-878a-4260-b1e0-6b4056e7233b" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_5a25ffa1-3edc-4619-9721-7f57db4c3027" shape="rect">
        </a>
        Uniquely identifies the referenced ROI described in the Structure Set ROI Sequence (3006,0020).
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_875a03e7-028e-421d-b014-ef12996953a7" shape="rect">
        </a>
        &gt;ROI Display Color
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_1586b43e-8f5a-4c82-9144-de7f8a35b447" shape="rect">
        </a>
        (3006,002A)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_892d8e81-cc56-4a67-8f36-33af7bd476f1" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_c68bc991-10c4-4651-b160-ae2a4fa66d14" shape="rect">
        </a>
        RGB triplet color representation for ROI, specified using the range 0-255.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_79837df9-0598-461a-ad0e-20832b2615bc" shape="rect">
        </a>
        &gt;Recommended Display Grayscale Value
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_a82de3a0-004d-422b-a5cd-e9bc7877b31c" shape="rect">
        </a>
        (0062,000C)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_b2642ce9-c3f0-48a1-b6ca-48a44887bb0e" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_d1ec2f96-4c83-497b-8164-f72a41ba753c" shape="rect">
        </a>
        A default single gray unsigned value in which it is recommended that the contour be rendered on a monochrome display. The units are specified in P-Values from a minimum of 0000H (black) up to a maximum of FFFFH (white).
       </p>
       <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
        <h3 class="title">
         Note
        </h3>
        <p>
         <a id="para_6113a007-b553-4634-9416-ccaeedafe8a0" shape="rect">
         </a>
         The maximum P-Value for this Attribute may be different from the maximum P-Value from the output of the Presentation LUT, which may be less than 16 bits in depth.
        </p>
       </div>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_bfb9b03d-63c9-4d31-b208-5d90d200530e" shape="rect">
        </a>
        &gt;Recommended Display CIELab Value
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_d928b3bd-9f60-410c-88b1-ba1b5ffc111b" shape="rect">
        </a>
        (0062,000D)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_f7c85e11-e1ce-4b5d-ab69-ec97c2c88aad" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_c96c7816-67a4-4a96-9cef-c59e5806f8f8" shape="rect">
        </a>
        A default triplet value in which it is recommended that the contour be rendered on a color display. The units are specified in PCS-Values, and the value is encoded as CIELab. See
        <a class="xref" href="#sect_C.********" shape="rect" title="C.******** Encoding of CIELab Values">
         Section C.********
        </a>.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_0f50084a-5b3b-4551-a488-0ce97f07fe4f" shape="rect">
        </a>
        &gt;Source Pixel Planes Characteristics Sequence
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_642e2653-96a4-45f6-8bf7-ceadbc30ffd0" shape="rect">
        </a>
        (3006,004A)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_1acd8b5f-070e-4754-b4db-146a5cd60d70" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_c6b0a0fa-22f3-48b1-94c8-be270aaf9d7b" shape="rect">
        </a>
        The characteristics of the pixel planes from which the grid-based representation of the Contours was derived.
       </p>
       <p>
        <a id="para_f218d3b3-de85-4a54-973e-c4bc994273bd" shape="rect">
        </a>
        Only a single Item is permitted in this Sequence.
       </p>
       <p>
        <a id="para_966e75da-9377-410a-922e-085d23280d60" shape="rect">
        </a>
        See
        <a class="xref" href="#sect_C.*******" shape="rect" title="C.******* Source Pixel Planes Characteristics">
         Section C.*******
        </a>.
       </p>
       <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
        <h3 class="title">
         Note
        </h3>
        <p>
         <a id="para_c4a5f01b-faa3-41f0-9ad9-f9d330f179b7" shape="rect">
         </a>
         This is not useful if Contour Geometric Type (3006,0042) equals POINT, OPEN_PLANAR or OPEN_NONPLANAR
        </p>
       </div>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_c2ab5232-6e66-4120-8bf2-705393054bed" shape="rect">
        </a>
        &gt;&gt;Pixel Spacing
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_863ee056-70ae-4206-bfe8-860c0434b3d0" shape="rect">
        </a>
        (0028,0030)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_bade1ff7-7b52-4832-88a5-c48e557c0c84" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_c203b123-bef5-448d-a59e-4e261e90dbea" shape="rect">
        </a>
        Physical distance in the patient between the center of each pixel, specified by a numeric pair - adjacent row spacing (delimiter) adjacent column spacing in mm. See
        <a class="xref" href="#sect_10.7.1.3" shape="rect" title="10.7.1.3 Pixel Spacing Value Order and Valid Values">
         Section 10.7.1.3
        </a>
        for further explanation.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_18f32dc9-bf03-45b8-9e22-e669e32e799e" shape="rect">
        </a>
        &gt;&gt;Spacing Between Slices
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_6784c230-0298-40e4-bc92-7f6e3b683390" shape="rect">
        </a>
        (0018,0088)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_7e00ebc7-b747-444a-8af1-7308c063c264" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_6ba921fa-5f1c-4ba9-a7e8-e1e27b20b8e4" shape="rect">
        </a>
        Spacing between adjacent slices, in mm. The spacing is measured from the center-to-center of each slice, and shall not be negative.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_b98b83ce-c1ae-429d-9ce2-057164b82161" shape="rect">
        </a>
        &gt;&gt;Image Orientation (Patient)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_3a76ded3-be36-408b-bc3d-1114a7bd679c" shape="rect">
        </a>
        (0020,0037)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_7a0bc7c3-c3f4-4df5-a701-b72e4632c18a" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_315ed5f2-bcc3-445b-ad81-3a629a6af261" shape="rect">
        </a>
        The direction cosines of the first row and the first column with respect to the patient. See
        <a class="xref" href="#sect_C.*******.1" shape="rect" title="C.*******.1 Image Position and Image Orientation">
         Section C.*******.1
        </a>.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_0d5732ce-f715-4e5e-9441-23a1d772fa30" shape="rect">
        </a>
        &gt;&gt;Image Position (Patient)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_482bb761-2485-418d-b237-a0c54f0f4274" shape="rect">
        </a>
        (0020,0032)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_8660649a-9f57-4ad0-ad6f-9fec63aa4588" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_61093910-6a70-48f2-8b63-7b4baf96d839" shape="rect">
        </a>
        The x, y and z coordinates in mm of the upper left hand corner of the pixel matrix in the Patient-Based Coordinate System described in
        <a class="xref" href="#sect_C.*******.1" shape="rect" title="C.*******.1 Image Position and Image Orientation">
         Section C.*******.1
        </a>.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_7056279e-2945-4b2b-a932-987b52a28de3" shape="rect">
        </a>
        &gt;&gt;Number of Frames
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_0ab9995a-bbb6-4fb8-a28c-3933a3d33f4c" shape="rect">
        </a>
        (0028,0008)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_f7f96689-2df9-47f0-ab4d-dca8a25e0b6d" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_c1b1cd08-36a2-40e1-9028-b32080056adb" shape="rect">
        </a>
        Number of source pixel planes.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_67b3df3e-7729-4a26-a85e-f3583bfaaad1" shape="rect">
        </a>
        &gt;&gt;Rows
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_8ae6ab38-951c-4899-be80-23b628621222" shape="rect">
        </a>
        (0028,0010)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_87b3a5bd-1068-4d1d-85f0-292839916c6a" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_86809ee2-c8e0-42a4-b0fd-e922fc8d8fdc" shape="rect">
        </a>
        Number of rows in the source pixel planes.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_a0062837-cfc4-47e6-8e6b-632f4256be0b" shape="rect">
        </a>
        &gt;&gt;Columns
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_03a37daa-9e7e-40d3-8d0b-7b992eff1797" shape="rect">
        </a>
        (0028,0011)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_4c21bde8-e1a0-4e5b-beaa-618124f7692b" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_a8c93ff7-c046-40fc-81fe-afbe57523119" shape="rect">
        </a>
        Number of columns in the source pixel planes.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_ccc7aad7-b3f7-4fdc-b498-5590a1983bdd" shape="rect">
        </a>
        &gt;Source Series Sequence
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_d3cb9be6-6c92-4757-b088-398a46062396" shape="rect">
        </a>
        (3006,004B)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_3f5b5df6-c275-4ecc-b22d-1846b5569f29" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_04f0afc6-ef2a-4bd3-b559-eaf233bf5c44" shape="rect">
        </a>
        Identifies the Image Series on which the ROI was defined.
       </p>
       <p>
        <a id="para_4d62c002-2808-4360-b8bd-83f930d640a3" shape="rect">
        </a>
        One or more Items are permitted in this Sequence.
       </p>
       <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
        <h3 class="title">
         Note
        </h3>
        <div class="orderedlist">
         <ol class="orderedlist" type="1">
          <li class="listitem">
           <p>
            <a id="para_5b8bb0e5-2e3f-4c5b-83ac-950d98876838" shape="rect">
            </a>
            The referenced Series may or may not have the same Frame of Reference UID (0020,0052) as this Instance, and there may be more than one referenced Series within the same Frame of Reference UID (0020,0052).
           </p>
          </li>
          <li class="listitem">
           <p>
            <a id="para_18d2cfc6-a59e-45b2-a23f-f922375c6036" shape="rect">
            </a>
            The referenced Series may or may not contain the images referenced in the Contour Image Sequence (3006,0016).
           </p>
          </li>
         </ol>
        </div>
       </div>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_4c6f3504-f085-482b-87c5-ea7a0ccdeb73" shape="rect">
        </a>
        &gt;&gt;Series Instance UID
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_a54f0255-c1c5-4f9b-9e94-a7495391fb62" shape="rect">
        </a>
        (0020,000E)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_337d0ffb-e36f-49da-8f0f-3a79ec35471d" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_513881db-15cb-4cd2-8c6a-c8199523dd24" shape="rect">
        </a>
        Unique identifier of the Series containing the referenced Instances.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_8df2ae10-15f6-498d-b728-bf1db5c17d01" shape="rect">
        </a>
        &gt;Contour Sequence
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_32e5d070-c142-4396-b984-21acd3c1bbf8" shape="rect">
        </a>
        (3006,0040)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_af62dfaa-d140-4332-b651-d4da0b8124fe" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_1fc0a3e2-6e73-4da1-96fa-d1c59d88e666" shape="rect">
        </a>
        Sequence of Contours defining ROI. One or more Items are permitted in this Sequence.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_4cce654e-16fe-4755-8df8-0461d943dfd8" shape="rect">
        </a>
        &gt;&gt;Contour Number
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_27235739-4922-43af-a293-2ac98a3ec801" shape="rect">
        </a>
        (3006,0048)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_8ef7b0fd-f010-4c6b-a2c9-978747282911" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_e43f6b09-90cd-4e51-b5f6-af357782adfe" shape="rect">
        </a>
        Identification number of the contour. The Value of Contour Number (3006,0048) shall be unique within the Contour Sequence (3006,0040) in which it is defined. No semantics or ordering shall be inferred from this Attribute.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_8e05d445-4adf-4f97-ab98-b186990d005b" shape="rect">
        </a>
        &gt;&gt;Contour Image Sequence
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_27a768ce-6b31-4d8b-b427-a1092bfc0ff4" shape="rect">
        </a>
        (3006,0016)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_9bc88903-b61a-4707-9641-20ea6c6c2cd4" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_cf846a41-9f07-49c2-aecc-a4aed7db6abb" shape="rect">
        </a>
        Sequence of images containing the contour.
       </p>
       <p>
        <a id="para_ac5cb41b-13b3-48a9-96bd-1ca464853fb8" shape="rect">
        </a>
        One or more Items are permitted in this Sequence.
       </p>
       <p>
        <a id="para_734014cd-d9cd-4794-9c36-9312d7ef110a" shape="rect">
        </a>
        See
        <a class="xref" href="#sect_C.*******" shape="rect" title="C.******* Source Pixel Planes Characteristics">
         Section C.*******
        </a>.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="4" rowspan="1">
       <p>
        <a id="para_be87dfe9-6d09-451c-b532-72bb4adcc607" shape="rect">
        </a>
        <span class="italic">
         &gt;&gt;&gt;Include
         <a class="xref" href="#table_10-3" shape="rect" title="Table 10-3. Image SOP Instance Reference Macro Attributes">
          Table 10-3 Image SOP Instance Reference Macro Attributes
         </a>
        </span>
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_fc1c7979-9d22-45e9-b8c7-9067e6d51165" shape="rect">
        </a>
        &gt;&gt;Contour Geometric Type
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_3d15b54d-3174-4c5f-aa58-bfc446ef300e" shape="rect">
        </a>
        (3006,0042)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_8cb6b935-865a-49f3-b171-6d09f840ba85" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_6e5d0251-61f0-494d-af68-7d91e9e4b396" shape="rect">
        </a>
        Geometric type of contour. See
        <a class="xref" href="#sect_C.*******" shape="rect" title="C.******* Contour Geometric Type">
         Section C.*******
        </a>.
       </p>
       <div class="variablelist">
        <p class="title">
         <strong>
          Enumerated Values:
         </strong>
        </p>
        <dl class="variablelist compact">
         <dt>
          <span class="term">
           POINT
          </span>
         </dt>
         <dd>
          <p>
           <a id="para_02eceb48-7c94-4ae8-bd79-1089b35d2c58" shape="rect">
           </a>
           single point
          </p>
         </dd>
         <dt>
          <span class="term">
           OPEN_PLANAR
          </span>
         </dt>
         <dd>
          <p>
           <a id="para_9e6c5b1b-0d3e-42c0-aebd-5a103b3f3a46" shape="rect">
           </a>
           open contour containing coplanar points
          </p>
         </dd>
         <dt>
          <span class="term">
           OPEN_NONPLANAR
          </span>
         </dt>
         <dd>
          <p>
           <a id="para_a8028517-a4cf-49ca-9eb1-09c3dbb39df4" shape="rect">
           </a>
           open contour containing non-coplanar points
          </p>
         </dd>
         <dt>
          <span class="term">
           CLOSED_PLANAR
          </span>
         </dt>
         <dd>
          <p>
           <a id="para_45b2eb68-f3a3-4cde-8b26-cb699f4a3c96" shape="rect">
           </a>
           closed contour (polygon) containing coplanar points
          </p>
         </dd>
         <dt>
          <span class="term">
           CLOSEDPLANAR_XOR
          </span>
         </dt>
         <dd>
          <p>
           <a id="para_40e943a2-07b1-4ff0-b12a-259aceee9171" shape="rect">
           </a>
           closed contour (polygon) containing coplanar points of an inner or outer contour combined using an XOR operator
          </p>
         </dd>
        </dl>
       </div>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_e9423481-47ea-4972-8008-cffb0baa8086" shape="rect">
        </a>
        &gt;&gt;Number of Contour Points
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_4612ca11-477f-48f0-87aa-d102e52634d0" shape="rect">
        </a>
        (3006,0046)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_9b138bde-064b-4661-8bad-6294b75a2287" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_f1811bbb-d9eb-4d40-8108-958c2856e7e1" shape="rect">
        </a>
        Number of points (triplets) in Contour Data (3006,0050).
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_74f33656-9ca3-48da-9950-b5848204b25c" shape="rect">
        </a>
        &gt;&gt;Contour Data
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_a1cdf87f-08a6-4c4a-ab24-cb606d0b8dec" shape="rect">
        </a>
        (3006,0050)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_1ab22818-e605-4482-94b9-7967009f277e" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_6c43c3f1-7783-4b16-8a12-81d330da3f3e" shape="rect">
        </a>
        Sequence of (x,y,z) triplets defining a contour in the Patient-Based Coordinate System described in
        <a class="xref" href="#sect_C.*******.1" shape="rect" title="C.*******.1 Image Position and Image Orientation">
         Section C.*******.1
        </a>
        (mm). See
        <a class="xref" href="#sect_C.*******" shape="rect" title="C.******* Contour Geometric Type">
         Section C.*******
        </a>
        and
        <a class="xref" href="#sect_C.*******" shape="rect" title="C.******* Representing Inner and Outer Contours">
         Section C.*******
        </a>.
       </p>
       <p>
        <a id="para_f5df3c25-4af4-407a-b3e9-5c780301a3f1" shape="rect">
        </a>
        See
        <a class="xref" href="#sect_C.*******" shape="rect" title="C.******* Source Pixel Planes Characteristics">
         Section C.*******
        </a>.
       </p>
       <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
        <h3 class="title">
         Note
        </h3>
        <p>
         <a id="para_23e73451-d6b6-436b-a13a-76d0e3b341b6" shape="rect">
         </a>
         If the Value Length of this Data Element exceeds 65534 bytes and an Explicit VR Transfer Syntax is used, then the Value Representation UN can be used for this Data Element. See
         <a class="olink" href="part05.html#sect_6.2.2" shape="rect">
          PS3.5 Section 6.2.2
         </a>.
        </p>
       </div>
      </td>
     </tr>
    </tbody>
   </table>
  </div>
 </div>
 <br class="table-break" clear="none"/>
 <div class="section">
  <div class="titlepage">
   <div>
    <div>
     <h5 class="title">
      <a id="sect_C.*******" shape="rect">
      </a>
      C.******* Contour Geometric Type
     </h5>
    </div>
   </div>
  </div>
  <p>
   <a id="para_8cfa02b0-0fd6-4ef7-81d5-10a4f55bf357" shape="rect">
   </a>
   A contour can be one of the following geometric types:
  </p>
  <div class="itemizedlist">
   <ul class="itemizedlist" style="list-style-type: disc; ">
    <li class="listitem">
     <p>
      <a id="para_e65e35c3-1b98-4b61-98f6-e5f27984ca7b" shape="rect">
      </a>
      A Contour Geometric Type (3006,0042) of POINT indicates that the contour is a single point, defining a specific location of significance.
     </p>
    </li>
    <li class="listitem">
     <p>
      <a id="para_6585f4e6-ef90-43d6-a373-b21dc9d82c22" shape="rect">
      </a>
      A Contour Geometric Type (3006,0042) of OPEN_PLANAR indicates that the last vertex shall not be connected to the first point, and that all points in Contour Data (3006,0050) shall be coplanar.
     </p>
    </li>
    <li class="listitem">
     <p>
      <a id="para_598b0539-d9f4-45b0-a051-29e8ba448d51" shape="rect">
      </a>
      A Contour Geometric Type (3006,0042) of OPEN_NONPLANAR indicates that the last vertex shall not be connected to the first point, and that the points in Contour Data (3006,0050) may be non-coplanar. Contours having a Geometric Type (3006,0042) of OPEN_NONPLANAR can be used to represent objects best described by a single, possibly non-coplanar curve, such as a brachytherapy applicator.
     </p>
    </li>
    <li class="listitem">
     <p>
      <a id="para_04e4e58c-522d-44c8-9d35-c4e8a8dc6079" shape="rect">
      </a>
      A Contour Geometric Type (3006,0042) of CLOSED_PLANAR indicates that the last point shall be connected to the first point, where the first point is not repeated in Contour Data (3006,0050). All points in Contour Data (3006,0050) shall be coplanar.
     </p>
    </li>
    <li class="listitem">
     <p>
      <a id="para_45b3dac3-ca52-4265-807d-f4a8448995ac" shape="rect">
      </a>
      A Contour Geometric Type (3006,0042) of CLOSEDPLANAR_XOR indicates that the last point shall be connected to the first point, where the first is not repeated in Contour Data (3006,0050). All points in Contour Data (3006,0050) shall be coplanar. More than one Contour is used to describe an ROI and these Contours are combined by geometric exclusive disjunction, see
      <a class="xref" href="#sect_C.*******" shape="rect" title="C.******* Representing Inner and Outer Contours">
       Section C.*******
      </a>. If any of the Contours within an ROI is of Contour Geometric Type (3006,0042) CLOSEDPLANAR_XOR, all Contours of that ROI shall be of the same type.
     </p>
    </li>
   </ul>
  </div>
 </div>
 <div class="section">
  <div class="titlepage">
   <div>
    <div>
     <h5 class="title">
      <a id="sect_C.8.8.6.2" shape="rect">
      </a>
      C.8.8.6.2 Contour Slab Thickness (Retired)
     </h5>
    </div>
   </div>
  </div>
  <p>
   <a id="para_e8a8aa7e-34a4-473e-b924-d11e9df4e0c4" shape="rect">
   </a>
   Retired. See
   <a class="link" href="http://dicom.nema.org/medical/dicom/2020e/output/pdf/part03.pdf" shape="rect" target="_top">
    PS3.3-2020e
   </a>.
  </p>
 </div>
 <div class="section">
  <div class="titlepage">
   <div>
    <div>
     <h5 class="title">
      <a id="sect_C.*******" shape="rect">
      </a>
      C.******* Representing Inner and Outer Contours
     </h5>
    </div>
   </div>
  </div>
  <p>
   <a id="para_9eef0dfb-9129-407b-9eae-bc2741836c33" shape="rect">
   </a>
   Inner and Outer Contours can be represented by two different techniques:
  </p>
  <p>
   <a id="para_4717f0b2-824c-4c35-a532-4c1b3f88d069" shape="rect">
   </a>
   Using the "keyhole" technique, an ROI with an excluded inner part is represented with a single planar Contour. In this method, an arbitrarily narrow channel is used to connect the outer contour to the inner contour, so that it is drawn as a single contour. An example of such a structure is shown in
   <a class="xref" href="#figure_C.8.8.6-1" shape="rect" title="Figure C.8.8.6-1. Example of ROI with excluded inner volume">
    Figure C.8.8.6-1
   </a>
   with the channel at roughly the 12 o'clock position.
  </p>
  <p>
   <a id="para_76e61d5a-affc-4165-87e8-68ee542e68fb" shape="rect">
   </a>
   Points in space lying along the path defined by the contour are considered to be part of the ROI.
  </p>
  <p>
   <a id="para_23450dac-0dcc-4498-a3d7-cd7f197cb70e" shape="rect">
   </a>
  </p>
  <div class="figure">
   <a id="figure_C.8.8.6-1" shape="rect">
   </a>
   <div class="figure-contents">
    <div class="mediaobject">
     <img alt="Example of ROI with excluded inner volume" src="figures/PS3.3_C.8.8.6-1.svg"/>
    </div>
   </div>
   <p class="title">
    <strong>
     Figure C.8.8.6-1. Example of ROI with excluded inner volume
    </strong>
   </p>
  </div>
  <p>
   <br class="figure-break" clear="none"/>
  </p>
  <p>
   <a id="para_bd3adeab-9df0-4542-b27c-919c34887dc8" shape="rect">
   </a>
   Using the "XOR" technique, an ROI with an excluded inner part is represented by two planar Contours that are combined by a geometric exclusive disjunction, thus extracting the inner from the outer Contour, see
   <a class="xref" href="#figure_C.8.8.6-2" shape="rect" title="Figure C.8.8.6-2. Example of ROI with contours exclusively added">
    Figure C.8.8.6-2
   </a>. The contours have the Contour Geometric Type (3006,0042) CLOSEDPLANAR_XOR.
  </p>
  <p>
   <a id="para_944e505a-22c7-42d2-8c8e-74302ae85323" shape="rect">
   </a>
  </p>
  <div class="figure">
   <a id="figure_C.8.8.6-2" shape="rect">
   </a>
   <div class="figure-contents">
    <div class="mediaobject">
     <table border="0" style="cellpadding: 0; cellspacing: 0;" summary="manufactured viewport for HTML img" width="50%">
      <tr>
       <td colspan="1" rowspan="1">
        <img alt="Example of ROI with contours exclusively added" src="figures/PS3.3_C.8.8.6-2.svg" width="100%"/>
       </td>
      </tr>
     </table>
    </div>
   </div>
   <p class="title">
    <strong>
     Figure C.8.8.6-2. Example of ROI with contours exclusively added
    </strong>
   </p>
  </div>
  <p>
   <br class="figure-break" clear="none"/>
  </p>
  <p>
   <a id="para_05145cb0-b1a3-4474-8ddd-61be239c2307" shape="rect">
   </a>
   Using this technique, it is also possible to create an ROI that includes disjoint parts of the ROI within an interior void. When two or more Contours are present, two Contours are combined using a geometric exclusive disjunction ("XOR"). Then this result is combined by an XOR operation with a third Contour, and so on for all other Contours of this ROI. The order of combination does not matter. An example of the result of an XOR operation of three Contours is visualized in
   <a class="xref" href="#figure_C.8.8.6-3" shape="rect" title="Figure C.8.8.6-3. Example of ROI with disjoint parts">
    Figure C.8.8.6-3
   </a>.
  </p>
  <p>
   <a id="para_31d3beeb-efa6-4bbd-adb2-83b8f6c45ec8" shape="rect">
   </a>
  </p>
  <div class="figure">
   <a id="figure_C.8.8.6-3" shape="rect">
   </a>
   <div class="figure-contents">
    <div class="mediaobject">
     <table border="0" style="cellpadding: 0; cellspacing: 0;" summary="manufactured viewport for HTML img" width="50%">
      <tr>
       <td colspan="1" rowspan="1">
        <img alt="Example of ROI with disjoint parts" src="figures/PS3.3_C.8.8.6-3.svg" width="100%"/>
       </td>
      </tr>
     </table>
    </div>
   </div>
   <p class="title">
    <strong>
     Figure C.8.8.6-3. Example of ROI with disjoint parts
    </strong>
   </p>
  </div>
  <p>
   <br class="figure-break" clear="none"/>
  </p>
 </div>
 <div class="section">
  <div class="titlepage">
   <div>
    <div>
     <h5 class="title">
      <a id="sect_C.*******" shape="rect">
      </a>
      C.******* Source Pixel Planes Characteristics
     </h5>
    </div>
   </div>
  </div>
  <p>
   <a id="para_7afd11ad-3bfa-4edf-bca0-335717d9cc67" shape="rect">
   </a>
   The Source Pixel Planes Characteristics Sequence (3006,004A) defines a stack of Source Pixel Planes on the originating system from which the Contour data of an ROI was derived. This stack of Source Pixel Planes does not need to correspond to actual Image Storage SOP Instances.
  </p>
  <p>
   <a id="para_2f782942-f6f2-4e92-ad17-2d934924a982" shape="rect">
   </a>
   If a receiving system also utilizes a pixel-based representation of Contours, the information in this Sequence may be utilized to define the same pixel grid as the originating system to reduce the magnitude of errors caused by different sampling rates.
  </p>
  <p>
   <a id="para_170d5adc-3c94-459f-bca8-0ffe6709e988" shape="rect">
   </a>
   If Source Pixel Planes Characteristics Sequence (3006,004A) is present for an ROI in the ROI Contour Sequence (3006,0039) the following apply:
  </p>
  <div class="itemizedlist">
   <ul class="itemizedlist" style="list-style-type: disc; ">
    <li class="listitem">
     <p>
      <a id="para_3730dd13-6109-410d-a3a4-b6c44cc64ba9" shape="rect">
      </a>
      Contours are specified on Source Pixel Planes defined by the characteristics in the Source Pixel Planes Characteristics Sequence (3006,004A).
     </p>
    </li>
    <li class="listitem">
     <p>
      <a id="para_26562fc8-0e2e-40c4-8372-9d63f2526eaf" shape="rect">
      </a>
      A Source Pixel Plane is not required to coincide with or be parallel to an actual image plane (i.e., Contour Image Sequence (3006,0016) is not required to be present, and if it is present, the referenced images need to not correspond to the characteristics of Source Pixel Planes).
     </p>
    </li>
    <li class="listitem">
     <p>
      <a id="para_2e30f380-5c3d-4d95-a92f-3669f3cd37cf" shape="rect">
      </a>
      The x, y, z triplets of Contour Data (3006,0050) shall be defined on the Source Pixel Planes defined by Source Pixel Planes Characteristics Sequence (3006,004A).
     </p>
    </li>
    <li class="listitem">
     <p>
      <a id="para_95629403-3eb3-443f-b71b-7fba2808ac02" shape="rect">
      </a>
      Contour Data (3006,0050) may have a different (e.g., higher) sampling than the Pixel Spacing (0028,0030).
     </p>
    </li>
    <li class="listitem">
     <p>
      <a id="para_1fd35075-9954-4ebc-890d-02c604a10e0e" shape="rect">
      </a>
      Source Pixel Planes for all Contours of the ROI will be parallel (since only one Image Orientation (Patient) is specified).
     </p>
    </li>
    <li class="listitem">
     <p>
      <a id="para_87831a0b-5362-41d9-a012-9dc8d945af71" shape="rect">
      </a>
      Source Pixel Planes will be equidistantly spaced (since only one Spacing Between Slices is specified).
     </p>
    </li>
    <li class="listitem">
     <p>
      <a id="para_c5d3a373-19a3-484f-9629-2bbd94fa73dd" shape="rect">
      </a>
      Contours of an ROI shall be specified on every Source Pixel Plane where the ROI is present.
     </p>
    </li>
    <li class="listitem">
     <p>
      <a id="para_0ab79240-c79e-4719-b1d8-5d872c8d3f68" shape="rect">
      </a>
      If no Contour Data is specified for a given Source Pixel Plane of an ROI, the ROI is defined to be absent on that Source Pixel Plane (i.e., a Source Pixel Plane without corresponding Contour Data defines a "gap").
     </p>
    </li>
   </ul>
  </div>
 </div>
</div>
