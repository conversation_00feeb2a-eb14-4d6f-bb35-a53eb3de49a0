#### C.8.8.15 RT Brachy Application Setups Module
The RT Brachy Application Setups Module describes the application of a brachytherapy radiotherapy treatment. It contains one or more sources, each associated with one or more Channels. A Channel is a device by which a source is placed in its intended treatment position or positions. A Channel may consist of a Source Applicator plus a Transfer Tube, a Source Applicator alone, a rigid or flexible linear source, or a seed. A number of Channels (for example applicators, sources or seeds) are generally arranged in an Application Setup, which may be considered a "logical" device. It is important not to confuse Application Setup with Applicator. The model used here has been primarily built around the concept of remote afterloading, but extended to support other brachytherapy applications such as manual applicators and molds, seeds, and sources. Additional devices that are not Channels are described as Brachy Accessory Devices. Examples of Accessory Devices include shields that modify the dose distribution from all sources in the treatment. However, Channel shields modify the dose only for the source(s) in that Channel.
The data in the Module are arranged as follows:
| Treatment Machine Sequence | ;treatment machine information (single Item) |
| Source Sequence | ;library of sources used in brachy application |
| Application Setup Sequence | ;one or more applicators, sources, seeds etc |
| &gt;Brachy Accessory Device Sequence | ;application level shields etc |
| &gt;Channel Sequence | ;applicator, line source(s), seed(s) etc |
| &gt;&gt;Channel Shield Sequence | ;channel-specific shields |
| &gt;&gt;Brachy Control Point Sequence | ;mechanism to support individual source dwell times |
**Table C.8-51. RT Brachy Application Setups Module Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Brachy Treatment Technique | (300A,0200) | 1 | Type of brachytherapy treatment technique. **Enumerated Values:** INTRALUMENARY INTRACAVITARY INTERSTITIAL CONTACT INTRAVASCULAR PERMANENT See Section C.********. |
| Brachy Treatment Type | (300A,0202) | 1 | Type of brachytherapy treatment. **Defined Terms:** MANUAL manually positioned HDR High dose rate MDR Medium dose rate LDR Low dose rate PDR Pulsed dose rate |
| Treatment Machine Sequence | (300A,0206) | 1 | A Sequence describing treatment machine to be used for treatment delivery. Only a single Item shall be included in this Sequence. |
| &gt;Treatment Machine Name | (300A,00B2) | 2 | User-defined name identifying treatment machine to be used for treatment delivery. |
| &gt;Manufacturer | (0008,0070) | 3 | Manufacturer of the equipment to be used for treatment delivery. |
| &gt;Institution Name | (0008,0080) | 3 | Institution where the equipment is located that is to be used for treatment delivery. |
| &gt;Institution Address | (0008,0081) | 3 | Mailing address of the institution where the equipment is located that is to be used for treatment delivery. |
| &gt;Institutional Department Name | (0008,1040) | 3 | Department in the institution where the equipment is located that is to be used for treatment delivery. |
| &gt;Institutional Department Type Code Sequence | (0008,1041) | 3 | A coded description of the type of Department or Service within the healthcare facility. Only a single Item is permitted in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |   |   | B [CID 7030 Institutional Department/Unit/Service](part16.html#sect_CID_7030). |
| &gt;Manufacturer's Model Name | (0008,1090) | 3 | Manufacturer's model name of the equipment that is to be used for treatment delivery. |
| &gt;Device Serial Number | (0018,1000) | 3 | Manufacturer's serial number of the equipment that is to be used for treatment delivery. |
| &gt;Date of Manufacture | (0018,1204) | 3 | The date the equipment that is to be used for treatment delivery was originally manufactured or re-manufactured (as opposed to refurbished). |
| &gt;Date of Installation | (0018,1205) | 3 | The date the equipment that is to be used for treatment delivery was installed in its current location. The equipment may or may not have been used prior to installation in its current location. |
| Source Sequence | (300A,0210) | 1 | Sequence of Sources to be used within Application Setups. One or more Items shall be included in this Sequence. |
| &gt;Source Number | (300A,0212) | 1 | Identification number of the Source. The Value of Source Number (300A,0212) shall be unique within the RT Plan in which it is created. |
| &gt;Source Serial Number | (3008,0105) | 3 | Identifier for the Source Instance. Identifies the actual source Instance of the source, to which Source Strength Reference Date (300A,022C) and Source Strength Reference Time (300A,022E) refer. |
| &gt;Source Model ID | (300A,021B) | 3 | Identifier for the Source Model. Identifies the model, the source Instance belongs to. |
| &gt;Source Description | (300A,021C) | 3 | Description of the source. |
| &gt;Source Type | (300A,0214) | 1 | Type of Source. **Defined Terms:** POINT LINE CYLINDER SPHERE |
| &gt;Source Manufacturer | (300A,0216) | 3 | Manufacturer of Source. |
| &gt;Active Source Diameter | (300A,0218) | 3 | Diameter of active Source (mm). |
| &gt;Active Source Length | (300A,021A) | 3 | Length of active Source (mm). |
| &gt;Material ID | (300A,00E1) | 3 | User-supplied identifier for encapsulation material of active Source. |
| &gt;Source Encapsulation Nominal Thickness | (300A,0222) | 3 | Nominal thickness of wall of encapsulation (mm). See Section C.*********. |
| &gt;Source Encapsulation Nominal Transmission | (300A,0224) | 3 | Nominal transmission through wall of encapsulation (between 0 and 1). See Section C.********* |
| &gt;Source Isotope Name | (300A,0226) | 1 | Name of Isotope. |
| &gt;Source Isotope Half Life | (300A,0228) | 1 | Half-life of Isotope (days). |
| &gt;Source Strength Units | (300A,0229) | 1C | Measurement unit of Source Strength. Required if the source is not a gamma-emitting (photon) source. May be present otherwise. **Enumerated Values:** AIR_KERMA_RATE Air Kerma Rate if Source is Gamma emitting Isotope. DOSE_RATE_WATER Dose Rate in Water if Source is Beta emitting Isotope. |
| &gt;Reference Air Kerma Rate | (300A,022A) | 1 | Air Kerma Rate in air of Isotope specified at Source Strength Reference Date (300A,022C) and Source Strength Reference Time (300A,022E) (in Gy h -1 at 1 m). Value shall be zero for non-gamma sources. |
| &gt;Source Strength | (300A,022B) | 1C | Source Strength of Isotope at Source Strength Reference Date (300A,022C) and Source Strength Reference Time (300A,022E), in units specified in Source Strength Units (300A,0229). Required if the source is not a gamma-emitting (photon) source. See Section C.********3. |
| &gt;Source Strength Reference Date | (300A,022C) | 1 | Reference date for Reference Air Kerma Rate (300A,022A) or Source Strength (300A,022B) of Isotope. |
| &gt;Source Strength Reference Time | (300A,022E) | 1 | Reference time for Air Kerma Rate (300A,022A) or Source Strength (300A,022B) of Isotope. |
| Application Setup Sequence | (300A,0230) | 1 | Sequence of Application Setups for current RT Plan. One or more Items shall be included in this Sequence. |
| &gt;Application Setup Type | (300A,0232) | 1 | Type of Application Setup. **Defined Terms:** FLETCHER_SUIT DELCLOS BLOEDORN JOSLIN_FLYNN CHANDIGARH MANCHESTER HENSCHKE NASOPHARYNGEAL OESOPHAGEAL ENDOBRONCHIAL SYED_NEBLETT ENDORECTAL PERINEAL |
| &gt;Application Setup Number | (300A,0234) | 1 | Identification number of the Application Setup. The Value of Application Setup Number (300A,0234) shall be unique within the RT Plan in which it is created. |
| &gt;Application Setup Name | (300A,0236) | 3 | User-defined name for Application Setup. |
| &gt;Application Setup Manufacturer | (300A,0238) | 3 | Manufacturer of Application Setup. |
| &gt;Template Number | (300A,0240) | 3 | Identification number of the Template. The Value of Template Number (300A,0240) shall be unique within the Application Setup in which it is created. |
| &gt;Template Type | (300A,0242) | 3 | User-defined type for Template Device. |
| &gt;Template Name | (300A,0244) | 3 | User-defined name for Template Device. |
| &gt;Referenced Reference Image Sequence | (300C,0042) | 3 | Sequence of reference images used for validation of current Application Setup. One or more Items are permitted in this Sequence. |
| &gt;&gt;Include Table 10-11 SOP Instance Reference Macro Attributes |   |   |   |
| &gt;Total Reference Air Kerma | (300A,0250) | 1 | Total Reference Air Kerma for current Application Setup, i.e., the product of Air Kerma Rate of all Sources in all Channels with their respective Channel Times ( Gy at 1 m). Value shall be zero for non-gamma sources. |
| &gt;Brachy Accessory Device Sequence | (300A,0260) | 3 | Sequence of Brachy Accessory Devices associated with current Application Setup. One or more Items are permitted in this Sequence. |
| &gt;&gt;Brachy Accessory Device Number | (300A,0262) | 2 | Identification number of the Brachy Accessory Device. The Value of Brachy Accessory Device Number (300A,0262) shall be unique within the Application Setup in which it is created. |
| &gt;&gt;Brachy Accessory Device ID | (300A,0263) | 2 | User or machine supplied identifier for Brachy Accessory Device. |
| &gt;&gt;Brachy Accessory Device Type | (300A,0264) | 1 | Type of Brachy Accessory Device. **Defined Terms:** SHIELD DILATATION MOLD PLAQUE FLAB |
| &gt;&gt;Brachy Accessory Device Name | (300A,0266) | 3 | User-defined name for Brachy Accessory Device. |
| &gt;&gt;Material ID | (300A,00E1) | 3 | User-supplied identifier for material of Brachy Accessory Device. See Note. |
| &gt;&gt;Brachy Accessory Device Nominal Thickness | (300A,026A) | 3 | Nominal thickness of Brachy Accessory Device (mm). See Section C.*********. |
| &gt;&gt;Brachy Accessory Device Nominal Transmission | (300A,026C) | 3 | Nominal Transmission through Brachy Accessory Device (between 0 and 1). See Section C.*********. |
| &gt;&gt;Referenced ROI Number | (3006,0084) | 2 | Uniquely identifies ROI representing the Brachy Accessory specified by ROI Number (3006,0022) in Structure Set ROI Sequence (3006,0020) in Structure Set Module within RT Structure Set referenced by Referenced RT Structure Set Sequence (300C,0060) in RT General Plan Module. See Section C.********. |
| &gt;Channel Sequence | (300A,0280) | 1 | Sequence of Channels for current Application Setup. One or more Items shall be included in this Sequence. |
| &gt;&gt;Channel Number | (300A,0282) | 1 | Identification number of the Channel. The Value of Channel Number (300A,0282) shall be unique within the Application Setup in which it is created. |
| &gt;&gt;Channel Length | (300A,0284) | 2 | Length of Channel (mm). See Section C.8.8.15.3. |
| &gt;&gt;Channel Total Time | (300A,0286) | 1 | Total amount of time between first and final Control Points of the Brachy Control Point Sequence (300A,02D0) for current Channel (sec). Channel Total Time calculation is based upon the Reference Air Kerma Rate (300A,022A) of the Referenced Source Number (300C,000E). |
| &gt;&gt;Source Movement Type | (300A,0288) | 1 | Type of Source movement. See Section C.8.8.15.4. **Defined Terms:** STEPWISE FIXED OSCILLATING UNIDIRECTIONAL |
| &gt;&gt;Number of Pulses | (300A,028A) | 1C | Number of Pulses per fraction for current Channel. Required if Brachy Treatment Type (300A,0202) is PDR. |
| &gt;&gt;Pulse Repetition Interval | (300A,028C) | 1C | Pulse repetition interval (sec) for current Channel. Required if Brachy Treatment Type (300A,0202) is PDR. |
| &gt;&gt;Source Applicator Number | (300A,0290) | 3 | Identification number of the Source Applicator. The Value of Source Applicator Number (300A,0290) shall be unique within the Channel in which it is created. |
| &gt;&gt;Source Applicator ID | (300A,0291) | 2C | User or machine supplied identifier for Source Applicator. Required if Source Applicator Number (300A,0290) is present. |
| &gt;&gt;Source Applicator Type | (300A,0292) | 1C | Type of Source Applicator. Required if Source Applicator Number (300A,0290) is present. **Defined Terms:** FLEXIBLE RIGID |
| &gt;&gt;Source Applicator Name | (300A,0294) | 3 | User-defined name for Source Applicator. |
| &gt;&gt;Source Applicator Length | (300A,0296) | 1C | Length of Source Applicator (mm), defined as the distance between the connector of the applicator and the distal-most position of the source. Required if Source Applicator Number (300A,0290) is present. |
| &gt;&gt;Source Applicator Tip Length | (300A,0274) | 2C | Length of Source Applicator Tip (in mm), defined as the distance between the outer tip of the applicator and the center of the distal-most possible position of the source. Required if Channel Effective Length (300A,0271) is present. See Section C.********6. |
| &gt;&gt;Source Applicator Manufacturer | (300A,0298) | 3 | Manufacturer of Source Applicator. |
| &gt;&gt;Material ID | (300A,00E1) | 3 | User-supplied identifier for material of Source Applicator wall. See Note. |
| &gt;&gt;Source Applicator Wall Nominal Thickness | (300A,029C) | 3 | Nominal Thickness of Source Applicator wall (mm). See Section C.*********. |
| &gt;&gt;Source Applicator Wall Nominal Transmission | (300A,029E) | 3 | Nominal Transmission through Source Applicator wall (between 0 and 1). See Section C.*********. |
| &gt;&gt;Source Applicator Step Size | (300A,02A0) | 1C | Distance of path along channel (in mm) between adjacent (potential) dwell positions. Required if Source Movement Type (300A,0288) is STEPWISE. |
| &gt;&gt;Applicator Shape Referenced ROI Number | (300A,02A1) | 3 | Uniquely identifies the ROI representing the Applicator shape surrounding the Channel. Defined in Structure Set ROI Sequence (3006,0020) within the RT Structure Set referenced by Referenced RT Structure Set Sequence (300C,0060). |
| &gt;&gt;Referenced ROI Number | (3006,0084) | 2C | Uniquely identifies the ROI representing the Channel shape. Specified by ROI Number (3006,0022) in Structure Set ROI Sequence (3006,0020) in Structure Set Module within RT Structure Set referenced by Referenced RT Structure Set Sequence (300C,0060) in RT General Plan Module. Required if Source Applicator Number (300A,0290) is present. See Section C.********. |
| &gt;&gt;Transfer Tube Number | (300A,02A2) | 2 | Identification number of the Transfer Tube. The Value of Transfer Tube Number (300A,02A2) shall be unique within the Channel in which it is created. |
| &gt;&gt;Transfer Tube Length | (300A,02A4) | 2C | Length of Transfer Tube of current afterloading Channel (mm). Required if Value of Transfer Tube Number (300A,02A2) is non-null. |
| &gt;&gt;Channel Effective Length | (300A,0271) | 3 | Length of Channel (in mm) defined as the distance between the connector on the afterloader and the center of the distal-most possible position of the source. See Section C.********6. |
| &gt;&gt;Channel Inner Length | (300A,0272) | 2C | The total physical inner length of channel (in mm). Specifies the distance between the connector on the afterloader and the end of the channel. Required if Channel Effective Length (300A,0271) is present. See Section C.********6.1. |
| &gt;&gt;Afterloader Channel ID | (300A,0273) | 3 | Identification of the Channel connection on the afterloader. See Section C.********6.2. |
| &gt;&gt;Channel Shield Sequence | (300A,02B0) | 3 | Sequence of Channel Shields associated with current Channel. One or more Items are permitted in this Sequence. See Section C.********. |
| &gt;&gt;&gt;Channel Shield Number | (300A,02B2) | 1 | Identification number of the Channel Shield. The Value of Channel Shield Number (300A,02B2) shall be unique within the Channel in which it is created. |
| &gt;&gt;&gt;Channel Shield ID | (300A,02B3) | 2 | User or machine supplied identifier for Channel Shield. |
| &gt;&gt;&gt;Channel Shield Name | (300A,02B4) | 3 | User-defined name for Channel Shield. |
| &gt;&gt;&gt;Material ID | (300A,00E1) | 3 | User-supplied identifier for material of Channel Shield. See Note. |
| &gt;&gt;&gt;Channel Shield Nominal Thickness | (300A,02B8) | 3 | Nominal Thickness of Channel Shield (mm). See Section C.*********. |
| &gt;&gt;&gt;Channel Shield Nominal Transmission | (300A,02BA) | 3 | Nominal Transmission of Channel Shield (between 0 and 1). See Section C.*********. |
| &gt;&gt;&gt;Referenced ROI Number | (3006,0084) | 2 | Uniquely identifies ROI representing the Channel Shield specified by ROI Number (3006,0022) in Structure Set ROI Sequence (3006,0020) in Structure Set Module within RT Structure Set referenced by Referenced RT Structure Set Sequence (300C,0060) in RT General Plan Module. See Section C.********. |
| &gt;&gt;Referenced Source Number | (300C,000E) | 1 | Uniquely identifies the referenced Source within Source Sequence (300A,0210) for current Application Setup. |
| &gt;&gt;Number of Control Points | (300A,0110) | 1 | Number of control points in Channel. For an N-segment Channel there will be 2N (stepwise movement) or N+1 (continuous movement) control points. |
| &gt;&gt;Final Cumulative Time Weight | (300A,02C8) | 1C | Value of Cumulative Time Weight (300A,02D6) for final Control Point in Brachy Control Point Sequence (300A,02D0). Required if Cumulative Time Weight (300A,02D6) is non-null in Control Points specified within Brachy Control Point Sequence (300A,02D0). See Section C.********. |
| &gt;&gt;Brachy Control Point Sequence | (300A,02D0) | 1 | Sequence of machine configurations describing this Channel. The number of Items in this Sequence shall equal the Value of Number of Control Points (300A,0110). See Section C.********. |
| &gt;&gt;&gt;Control Point Index | (300A,0112) | 1 | Index of current Control Point, starting at 0 for first Control Point. |
| &gt;&gt;&gt;Cumulative Time Weight | (300A,02D6) | 2 | Cumulative time weight to current Control Point (where the weighting is proportional to time values delivered). Cumulative Time Weight for first Item in Brachy Control Point Sequence (300A,02D0) is always zero. See Section C.******** and Section C.8.8.15.8. |
| &gt;&gt;&gt;Control Point Relative Position | (300A,02D2) | 1 | Distance between current Control Point Position and the center of the distal-most possible Source position in current Channel (mm). See Section C.8.8.15.9. |
| &gt;&gt;&gt;Control Point 3D Position | (300A,02D4) | 3 | Coordinates (x, y, z) of Control Point in the Patient-Based Coordinate System described in Section C.7.6.2.1.1 (mm). See Section C.********0. |
| &gt;&gt;&gt;Control Point Orientation | (300A,0412) | 3 | (x,y,z) component of the direction vector of the brachy source or seed at the Control Point 3D Position (300A,02D4). See Section C.********4. |
| &gt;&gt;&gt;Brachy Referenced Dose Reference Sequence | (300C,0055) | 3 | A Sequence of Dose References for current Channel. One or more Items are permitted in this Sequence. |
| &gt;&gt;&gt;&gt;Referenced Dose Reference Number | (300C,0051) | 1 | Uniquely identifies Dose Reference described in Dose Reference Sequence. (300A,0010) within RT Prescription Module of current RT Plan. |
| &gt;&gt;&gt;&gt;Cumulative Dose Reference Coefficient | (300A,010C) | 1 | Coefficient used to calculate cumulative dose contribution from this Source to the referenced Dose Reference at the current Control Point. See Section C.********1. |
### Note
Material ID (300A,00E1) may also be specified within a referenced ROI, if an ROI is used to describe the object.
##### C.******** Permanent Implants
In permanent implant techniques the Value for Channel Total Time (300A,0286) shall be mean life time of the isotope. The Brachy Control Point Sequence (300A,02D0) shall consist of two Items: the first having Cumulative Time Weight (300A,02D6) = 0 and the second having Cumulative Time Weight (300A,02D6) = Final Cumulative Time Weight (300A,02C8).
##### C.******** Referenced ROI Number
The Structure Set ROI shall be used in the RT Brachy Application Setups Module to describe the 3D coordinates of Accessory Devices, Applicators and Channel Shields, but not individual source positions (see Section C.8.8.15.9 and Section C.********0 ).
##### C.8.8.15.3 Channel Length
If specified, Channel Length (300A,0284) shall be the sum of the Source Applicator Length (300A,0296) and Transfer Tube Length (300A,02A4).
##### C.8.8.15.4 Oscillating Source Movement
In brachytherapy treatment techniques involving oscillating source movement (i.e., when Source Movement Type (300A,0288) is OSCILLATING), the Brachy Control Point Sequence (300A,02D0) shall consist of two Items. The first Control Point shall have Cumulative Time Weight (300A,02D6) = 0, and Control Point Relative Position (300A,02D2) equal to one end point of the oscillation. The second Control Point shall have Cumulative Time Weight (300A,02D6) = Final Cumulative Time Weight (300A,02C8), and Control Point Relative Position (300A,02D2) equal to the other end point of the oscillation. Transit time shall not be modeled explicitly for oscillating techniques.
##### C.******** Channel Shields
The effect of Channel Shields on dose contributions shall be specific to the Channel for which they are specified. There shall be no effect of these shields on the dose contributions from any other Channels.
##### C.******** Time Calculations
The treatment time at a given Control Point is equal to the Channel Total Time (300A,0286), multiplied by the Cumulative Time Weight (300A,02D6) for the Control Point, divided by the Final Cumulative Time Weight (300A,02C8). If the calculation for treatment time results in a time value that is not an exact multiple of the timer resolution, then the result shall be rounded to the nearest allowed timer value (i.e., less than a half resolution unit shall be rounded down to the nearest resolution unit, and equal or greater than half a resolution unit shall be rounded up to the nearest resolution unit).
Note also that if Final Cumulative Time Weight (300A,02C8) is equal to 100, then Cumulative Time Weight (300A,02D6) becomes equivalent to the percentage of Channel Total Time (300A,0286) delivered at each control point. If Final Cumulative Time Weight (300A,02C8) is equal to Channel Total Time (300A,0286), then the Cumulative Time Weight (300A,02D6) at each control point becomes equal to the cumulative treatment time delivered at that control point.
If Treatment Type (300A,0202) is PDR, then the Channel Total Time (3008,0286) shall specify the duration of a single pulse.
##### C.******** Brachy Control Point Sequence
The Control Points shall be arranged such that the first Control Point for a particular Channel describes the first dwell position and the final Control Point for the Channel describes the final dwell position. If Brachy Treatment Type (300A,0202) is PDR, the Brachy Control Point Sequence (300A,02D0) shall specify the sequence of machine configurations for a single pulse. Similarly, if Source Movement Type (300A,0288) is OSCILLATING, the Brachy Control Point Sequence (300A,02D0) shall specify the sequence of machine configurations for a single period.
Some examples of Brachytherapy specification using control points are as follows:
a) Stepwise motion; Four equally weighted dwell positions; Step size = 10; Final Cumulative Time Weight = 100: 
Control Point 0: Control Point Relative Position = 30, Cumulative Time Weight = 0
Control Point 1: Control Point Relative Position = 30, Cumulative Time Weight = 25
Control Point 2: Control Point Relative Position = 20, Cumulative Time Weight = 25
Control Point 3: Control Point Relative Position = 20, Cumulative Time Weight = 50
Control Point 4: Control Point Relative Position = 10, Cumulative Time Weight = 50
Control Point 5: Control Point Relative Position = 10, Cumulative Time Weight = 75
Control Point 6: Control Point Relative Position = 0, Cumulative Time Weight = 75
Control Point 7: Control Point Relative Position = 0, Cumulative Time Weight = 100
b) Fixed (manually placed) sources; Final Cumulative Time Weight = 100: 
Control Point 0: Control Point Relative Position = 0, Control Point 3D Position = (x,y,z), Cumulative Time Weight = 0
Control Point 1: Control Point Relative Position = 0, Control Point 3D Position = (x,y,z), Cumulative Time Weight = 100
c) Oscillating movement; Final Cumulative Time Weight = 100  :
Control Point 0: Control Point Relative Position = 100, Cumulative Time Weight = 0
Control Point 1: Control Point Relative Position = 0, Cumulative Time Weight = 100
d) Unidirectional movement; Final Cumulative Time Weight = 100  :
Control Point 0: Control Point Relative Position = 0, Cumulative Time Weight = 0
Control Point 1: Control Point Relative Position = 100, Cumulative Time Weight = 100
e) Stepwise motion with consideration of source transit times between dwell positions; Three equally weighted dwell positions; Step size = 10; Final Cumulative Time Weight = 79: 
Control Point 0: Control Point Relative Position = 30, Cumulative Time Weight = 0
Control Point 1: Control Point Relative Position = 30, Cumulative Time Weight = 25
Control Point 2: Control Point Relative Position = 20, Cumulative Time Weight = 27
Control Point 3: Control Point Relative Position = 20, Cumulative Time Weight = 52
Control Point 4: Control Point Relative Position = 10, Cumulative Time Weight = 54
Control Point 5: Control Point Relative Position = 10, Cumulative Time Weight = 79
f) Stepwise motion with consideration of source transit times between dwell positions and to first and from last dwell position; Three equally weighted dwell positions; Step size = 10; Final Cumulative Time Weight = 383: 
Control Point 0: Control Point Relative Position = 1200, Cumulative Time Weight = 0
Control Point 1: Control Point Relative Position = 30, Cumulative Time Weight = 150
Control Point 2: Control Point Relative Position = 30, Cumulative Time Weight = 175
Control Point 3: Control Point Relative Position = 20, Cumulative Time Weight = 177
Control Point 4: Control Point Relative Position = 20, Cumulative Time Weight = 202
Control Point 5: Control Point Relative Position = 10, Cumulative Time Weight = 204
Control Point 6: Control Point Relative Position = 10, Cumulative Time Weight = 229
Control Point 7: Control Point Relative Position = 1200, Cumulative Time Weight = 383
##### C.8.8.15.8 Source Transit Time
The Source transit times between dwell positions of a remote afterloader may be considered by specifying a non-zero increment in the Cumulative Time Weight (300A,02D6) when the Source moves between Control Points. In this case the Channel Total Time (300A,0286) shall include the overall Source transit time for the Channel.
##### C.8.8.15.9 Control Point Relative Position
Control Point Relative Position (300A,02D2) shall describe where the center of a given source in a channel is located with respect to the end of the channel, i.e. to the center of the distal-most possible dwell position in the channel.
##### C.********0 Control Point 3D Position
Control Point 3D Position (300A,02D4) shall describe the absolute 3D coordinates of a source. This position shall correspond to the  center  of a source in an applicator during a remote or manually controlled afterloading treatment.
##### C.********1 Cumulative Dose Reference Coefficient
The Cumulative Dose Reference Coefficient (300A,010C) is the value by which Brachy Application Setup Dose (300A,00A4) is multiplied to obtain the dose to the referenced dose reference site at the current control point (and after previous control points have been successfully administered). The Cumulative Dose Reference Coefficient (300A,010C) is by definition zero for the initial control point. The Cumulative Dose Reference Coefficient (300A,010C) of the final control point multiplied by Brachy Application Setup Dose (300A,00A4) results in the final dose to the referenced dose reference site for the current channel. Dose calculation for dose reference sites other than points is not well defined.
If Treatment Type (300A,0202) is PDR, then the Cumulative Dose Reference Coefficient (3008,010C) shall specify the dose delivered to the dose reference during a single pulse. The total dose delivered to the dose reference shall then be expressed by Cumulative Dose Reference Coefficient (3008,010C) multiplied by Number of Pulses (300A,028A) multiplied by Brachy Application Setup Dose (300A,00A4).
##### C.********* Nominal Thickness and Nominal Transmission
If provided, Source Encapsulation Nominal Thickness (300A,0222), Brachy Accessory Device Nominal Thickness (300A,026A), Source Applicator Wall Nominal Thickness (300A,029C), and Channel Shield Nominal Thickness (300A,02B8) shall indicate that the related objects are of uniform thickness with the specified value. If this is not the case, these Attributes shall not be provided.
If provided, Source Encapsulation Nominal Transmission (300A,0224), Brachy Accessory Device Nominal Transmission (300A,026C), Source Applicator Wall Nominal Transmission (300A,029E), and Channel Shield Nominal Transmission (300A,02BA) shall indicate that the related objects are of uniform transmission with the specified value. If this is not the case, these Attributes shall not be provided.
No assumptions are made about the source characteristics beyond the parameters specified here.
##### C.********3 Reference Point for Calibration of Beta Emitting Isotopes
For beta emitting isotopes, Source Strength (300A,022B) shall be defined at reference point (r0,0), where r0 is the radial distance of 2 mm from the source longitudinal axis, and 0 is the angle of 90 degrees between the source longitudinal axis and the line defined by the center of the source and the reference point. Refer to:
- IEC 60601-2-17 (Medical electrical equipment - Particular requirements for the safety of automatically-controlled brachytherapy afterloading equipment), where the beta source strength is defined as: ABSORBED DOSE RATE [Gy s-1] in water at 2 mm along the perpendicular bisector from a RADIOACTIVE SOURCE emitting beta RADIATION.
- Nath et. al.: Intravascular brachytherapy physics: Report of the AAPM Radiation Therapy Committee Task Group No. 60, Med. Phys 26 (2) Feb 1999, pp 119-152.
##### C.********4 Orientation of Brachy Sources
The Control Point Orientation (300A,0412) shall be used to define the orientation of an anisotropic brachytherapy source or seed for the purpose of calculating the effect of the anisotropy on the dose calculation. The Control Point Orientation (300A,0412) shall be given by the direction vector of the long axis of the Brachy source or seed in the insertion direction, in the DICOM Patient-Based Coordinate System. The direction vector shall be oriented from the source center as defined by the Control Point 3D Position (300A,02D4) along the long axis of the source and in the insertion direction.
##### C.********5 Source Model ID
The Source Model ID (300A,021B) ties together the physical source properties. The creator of the plan has used this ID to select the radioactive source model for the plan. An example would be the name of the used TG-43 data.
##### C.********6 Geometric Parameters
A Channel is the combination of the Transfer Tube and the Source Applicator. A Channel is physically connected to one of the connection sockets of the afterloader. Afterloader Channel ID (300A,0273) unambiguously identifies the afterloader channel to which the applicator is connected; see Section C.********6.2. Both Afterloader Channel ID (300A,0273) and Source Applicator ID (300A,0291) describe the connection between the afterloader and the applicator (so called channel mapping). Channel Number (300A,0282) is used to index the Items within the Channel Sequence (300A,0280) and does not serve as the identification of the channel socket at the afterloader.
**Figure C.8.8.15-1. RT Brachy Channel Geometric Parameters**
### Note
Channel Length (300A,0284) has been interpreted in different ways. Channel Effective Length (300A,0271) should be used instead, as the distance from the afterloader to the center of the distal-most possible dwell position following the definition in Figure C.8.8.15-1.
Additionally, the Transfer Tube Length (300A,02A4) may be specified. If Transfer Tube Length (300A,02A4) is absent or has no Value, the distance from the Source Applicator connector to the center of the distal-most possible dwell position is equal to the Value of the Channel Effective Length (300A,0271). If Transfer Tube Length (300A,02A4) has a Value, the distance from the Source Applicator connector to the center of the distal-most possible dwell position is equal to the Channel Effective Length (300A,0271) minus the Transfer Tube Length (300A,02A4). The concept of Source Applicator Length (300A,0296) should not be used any longer.
###### C.********6.1 Channel Inner Length
The Channel Inner Length (300A,0272) is the measurable physical inner length of the channel. It specifies the distance between the connector on the afterloader and the inner end of the channel. In the RT Brachy Session Record Module it shall be specified as the measured or verified length.
###### C.********6.2 Afterloader Channel ID
The Afterloader Channel ID (300A,0273) is the connector socket identification on the afterloader where the channel is connected.
**Figure C.8.8.15-2. RT Brachy Channel and Applicator IDs**