#### C.8.2.1 CT Image Module
Table C.8-3 specifies the Attributes of the CT Image Module, which describe CT images.
**Table C.8-3. CT Image Module Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Image Type | (0008,0008) | 1 | Image identification characteristics. See Section C.*******.1 for specialization. |
| Multi-energy CT Acquisition | (0018,9361) | 3 | Indicates whether the image is created by means of Multi-energy technique. **Enumerated Values:** YES NO |
| Samples per Pixel | (0028,0002) | 1 | Number of samples (planes) in this image. See Section C.*******.2 for specialization. |
| Photometric Interpretation | (0028,0004) | 1 | Specifies the intended interpretation of the pixel data. See Section C.*******.3 for specialization. |
| Bits Allocated | (0028,0100) | 1 | Number of bits allocated for each pixel sample. Each sample shall have the same number of bits allocated. See Section C.*******.4 for specialization. |
| Bits Stored | (0028,0101) | 1 | Number of bits stored for each pixel sample. Each sample shall have the same number of bits stored. See Section C.*******.5 for specialization. |
| High Bit | (0028,0102) | 1 | Most significant bit for pixel sample data. Each sample shall have the same high bit. See Section C.*******.6 for specialization. |
| Rescale Intercept | (0028,1052) | 1 | The value b in relationship between stored values (SV) and the output units. Output units = m*SV+b If Image Type (0008,0008) Value 1 is ORIGINAL and Value 3 is not LOCALIZER, and Multi-energy CT Acquisition (0018,9361) is either absent or NO, output units shall be Hounsfield Units (HU). |
| Rescale Slope | (0028,1053) | 1 | m in the equation specified in Rescale Intercept (0028,1052). |
| Rescale Type | (0028,1054) | 1C | Specifies the output units of Rescale Slope (0028,1053) and Rescale Intercept (0028,1052). See Section C.******** for Defined Terms and further explanation. Required if the Rescale Type is not HU (Hounsfield Units), or Multi-energy CT Acquisition (0018,9361) is YES. May be present otherwise. |
| KVP | (0018,0060) | 2 | Peak kilo voltage output of the X-Ray generator used. Shall be empty if this Attribute is present in Multi-energy CT Acquisition Sequence (0018,9362) and the Value of this Attribute is not the same in all Items of the Multi-energy CT Acquisition Sequence (0018,9362). ### Note In the context of a Multi-energy acquisition the concerned energy spectrum needs to be considered on multiple layers: - Energy spectrum emitted by the source, see KVP (0018,0060) Value in this Module and Values in Table C.8-125 CT X-Ray Details Macro Attributes. - Energy spectrum effectively consumed by the detector, see Table C.8.2.2-3 Multi-energy CT X-Ray Detector Macro Attributes. - Nominal energy level associated with the image if applicable - see Monoenergetic Energy Equivalent (0018,937C) in Table C.*********-1 Multi-energy CT Characteristics Macro Attributes. In case there is only one KVP value for a Multi-energy acquisition (for example, Twin Beam, Photon-Counting, Dual-Layer-Detectors), the effective energies contributing to the image might be derived from the information in Table C.8.2.2-3 Multi-energy CT X-Ray Detector Macro Attributes.. |
| Acquisition Number | (0020,0012) | 2 | A number identifying the single continuous gathering of data over a period of time that resulted in this image. |
| Scan Options | (0018,0022) | 3 | Parameters of scanning sequence. |
| Data Collection Diameter | (0018,0090) | 3 | The diameter in mm of the region over which data were collected. Shall not be present if this Attribute is present in Multi-energy CT Acquisition Sequence (0018,9362) and the Value of this Attribute is not the same in all Items of the Multi-energy CT Acquisition Sequence (0018,9362). |
| Data Collection Center (Patient) | (0018,9313) | 3 | The x, y, and z coordinates (in the Patient-Based Coordinate System) in mm of the center of the region in which data were collected. See Section C.8.15.3.6.1. |
| Reconstruction Diameter | (0018,1100) | 3 | Diameter in mm of the region from within which data were used in creating the reconstruction of the image. Data may exist outside this region and portions of the patient may exist outside this region. The diameter defines a circular region that is entirely contained within the encoded Pixel Data (7FE0,0010), unless the encoded image has been cropped after reconstruction. ### Note If not cropped or padded, for square images with square pixels, both Values of Pixel Spacing (0028,0030) will be equal and equal to Reconstruction Diameter (0018,1100) / Rows (0028,0010) and Reconstruction Diameter (0018,1100) / Columns (0028,0011). |
| Reconstruction Target Center (Patient) | (0018,9318) | 3 | The x, y, and z coordinates (in the Patient-Based Coordinate System) of the reconstruction center target point as used for reconstruction in mm. See Section C.8.15.3.6.1. ### Note If the reconstructed image is not magnified or panned the Value corresponds with the Data Collection Center (Patient) (0018,9313). |
| Distance Source to Detector | (0018,1110) | 3 | Distance in mm from source to detector center. ### Note This value is traditionally referred to as Source Image Receptor Distance (SID). Shall not be present if this Attribute is present in Multi-energy CT Acquisition Sequence (0018,9362) and the Value of this Attribute is not the same in all Items of the Multi-energy CT Acquisition Sequence (0018,9362). |
| Distance Source to Patient | (0018,1111) | 3 | Distance in mm from source to isocenter (center of field of view). ### Note This value is traditionally referred to as Source Object Distance (SOD). |
| Gantry/Detector Tilt | (0018,1120) | 3 | Nominal angle of tilt in degrees of the scanning gantry. Not intended for mathematical computations. |
| Table Height | (0018,1130) | 3 | The distance in mm of the top of the patient table to the center of rotation; below the center is positive. |
| Rotation Direction | (0018,1140) | 3 | Direction of rotation of the source when relevant, about nearest principal axis of equipment. **Enumerated Values:** CW clockwise CC counter clockwise |
| Exposure Time | (0018,1150) | 3 | Time of X-Ray exposure in msec. If Acquisition Type (0018,9302) equals SPIRAL, the Value of this Attribute shall be Revolution Time (0018,9305) divided by the Spiral Pitch Factor (0018,9311). See Section C.********.1 and Section C.********.1. Shall not be present if the corresponding Attribute, Exposure Time in ms (0018,9328), is present in Multi-energy CT Acquisition Sequence (0018,9362) and the Value of this Attribute is not the same in all Items of the Multi-energy CT Acquisition Sequence (0018,9362). |
| X-Ray Tube Current | (0018,1151) | 3 | X-Ray Tube Current in mA. Shall not be present if the corresponding Attribute, X-Ray Tube Current in mA (0018,9330), is present in Multi-energy CT Acquisition Sequence (0018,9362) and the Value of this Attribute is not the same in all Items of the Multi-energy CT Acquisition Sequence (0018,9362). |
| Exposure | (0018,1152) | 3 | The exposure expressed in mAs, for example calculated from Exposure Time and X-Ray Tube Current. Shall not be present if the corresponding Attribute, Exposure in mAs (0018,9332), is present in Multi-energy CT Acquisition Sequence (0018,9362) and the Value of this Attribute is not the same in all Items of the Multi-energy CT Acquisition Sequence (0018,9362). |
| Exposure in As | (0018,1153) | 3 | The exposure expressed in As, for example calculated from Exposure Time and X-Ray Tube Current. Shall not be present if the corresponding Attribute, Exposure in mAs (0018,9332), is present in Multi-energy CT Acquisition Sequence (0018,9362) and the Value of this Attribute is not the same in all Items of the Multi-energy CT Acquisition Sequence (0018,9362). |
| Filter Type | (0018,1160) | 3 | Label for the type of filter inserted into the X-Ray beam. Shall not be present if this Attribute is present in Multi-energy CT Acquisition Sequence (0018,9362) and the Value of this Attribute is not the same in all Items of the Multi-energy CT Acquisition Sequence (0018,9362). |
| Filter Material | (0018,7050) | 3 | The X-Ray absorbing material used in the filter. May be multi-valued. See [Annex P Correspondence of X-Ray Filter Material Codes and Defined Terms in PS3.16](part16.html#chapter_P) for Defined Terms. |
| Generator Power | (0018,1170) | 3 | Power in kW to the X-Ray generator. Shall not be present if this Attribute is present in Multi-energy CT Acquisition Sequence (0018,9362) and the Value of this Attribute is not the same in all Items of the Multi-energy CT Acquisition Sequence (0018,9362). |
| Focal Spot(s) | (0018,1190) | 3 | Size of the focal spot in mm. For devices with variable focal spot or multiple focal spots, small dimension followed by large dimension. Shall not be present if this Attribute is present in Multi-energy CT Acquisition Sequence (0018,9362) and the Value of this Attribute is not the same in all Items of the Multi-energy CT Acquisition Sequence (0018,9362). |
| Convolution Kernel | (0018,1210) | 3 | A label describing the convolution kernel or algorithm used to reconstruct the data. |
| Revolution Time | (0018,9305) | 3 | The time in seconds of a complete revolution of the source around the gantry orbit. |
| Single Collimation Width | (0018,9306) | 3 | The width of a single row of acquired data (in mm). ### Note Adjacent physical detector rows may have been combined to form a single effective acquisition row. Shall not be present if this Attribute is present in Multi-energy CT Acquisition Sequence (0018,9362) and the Value of this Attribute is not the same in all Items of the Multi-energy CT Acquisition Sequence (0018,9362). |
| Total Collimation Width | (0018,9307) | 3 | The width of the total collimation (in mm) over the area of active X-Ray detection. ### Note This will be equal the number of effective detector rows multiplied by single collimation width. Shall not be present if this Attribute is present in Multi-energy CT Acquisition Sequence (0018,9362) and the Value of this Attribute is not the same in all Items of the Multi-energy CT Acquisition Sequence (0018,9362). |
| Table Speed | (0018,9309) | 3 | The distance in mm that the table moves in one second during the gathering of data that resulted in this image. Table motion is relative to the gantry Frame of Reference, thus if the gantry is moving, the distance value represents the net motion. This Attribute also applies to patient support equipment other than tables. |
| Table Feed per Rotation | (0018,9310) | 3 | Motion of the table (in mm) during a complete revolution of the source around the gantry orbit. Table motion is relative to the gantry Frame of Reference, thus if the gantry is moving, the feed value represents the net motion. This Attribute also applies to patient support equipment other than tables. |
| Spiral Pitch Factor | (0018,9311) | 3 | Ratio of the Table Feed per Rotation (0018,9310) to the Total Collimation Width (0018,9307). |
| Exposure Modulation Type | (0018,9323) | 3 | A label describing the type of exposure modulation used for the purpose of limiting the dose. **Defined Terms:** NONE |
| CTDIvol | (0018,9345) | 3 | Computed Tomography Dose Index (CTDI vol ), in mGy according to the principles described in [ IEC 60601-2-44 ] . It describes the average dose for this image for the selected CT conditions of operation. |
| CTDI Phantom Type Code Sequence | (0018,9346) | 3 | The type of phantom used for CTDI measurement. Only a single Item is permitted in this Sequence. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |   |   | D [CID 4053 CTDI Phantom Device](part16.html#sect_CID_4053). |
| Water Equivalent Diameter | (0018,1271) | 3 | The diameter, in mm, of a cylinder of water having the same X-Ray attenuation as the patient for this reconstructed slice (e.g., as described in [ AAPM Report 220 ] ). |
| Water Equivalent Diameter Calculation Method Code Sequence | (0018,1272) | 1C | The method of calculation of Water Equivalent Diameter (0018,1271). Required if Water Equivalent Diameter (0018,1271) is present. Only a single Item is permitted in this Sequence. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |   |   | D [CID 10024 Water Equivalent Diameter Method](part16.html#sect_CID_10024). |
| Image and Fluoroscopy Area Dose Product | (0018,115E) | 3 | X-Ray dose, measured in dGy*cm*cm, to which the patient was exposed for the acquisition of the entire Irradiation Event from which this image was reconstructed. ### Note - All of the images reconstructed from the same Irradiation Event will have the same Value for this Attribute, which is the total for the Irradiation Event, which is repeated in each image, regardless of whether or not the Irradiation Event UID (0008,3010) is present with a Value in the Multi-frame Dimension Module. I.e., the values for each image should not be summed. The sum of the area dose products of all encoded Irradiation Events may not result in the total area dose product to which the patient was exposed. - This may be an estimated value based on assumptions about the Patients body size and habitus. - This value is required by [ IEC 60601-2-63 ] . |
| Include Table 10-7 General Anatomy Optional Macro Attributes |   |   | Anatomic Region Sequence D [CID 4030 CT, MR and PET Anatomy Imaged](part16.html#sect_CID_4030). |
| Include Table 10-25 Optional View and Slice Progression Direction Macro Attributes |   |   |   |
| Calcium Scoring Mass Factor Patient | (0018,9351) | 3 | The calibration factor for the calcium mass score. These factors incorporate the effects of - KV value of the CT image - the patient size. - machine specific corrections See Section C.*******.7. |
| Calcium Scoring Mass Factor Device | (0018,9352) | 3 | The calibration factors for the calcium mass score of the device. These factors incorporate the effects of - KV value of the CT image - machine specific corrections This a multi-valued Attribute, the first Value specifies the mass factor for a small patient size, the second Value for a medium patient size and The third Value for a large patient size. See Section C.*******.7. |
| Energy Weighting Factor | (0018,9353) | 1C | The weighting factor of the data from the primary source in a multiple energy composition image. This factor incorporates the effects of - the specific X-Ray source and kV value - examination specific characteristics. Required if one Derivation Code Sequence (0008,9215) Item value is [(113097, DCM, "Multi-energy proportional weighting")](part16.html#DCM_113097). May be present otherwise. |
| CT Additional X-Ray Source Sequence | (0018,9360) | 3 | Acquisition parameters for X-Ray sources beyond the primary X-Ray source, which is specified in other Attributes of this Module. One or more Items are permitted in this Sequence. ### Note This Sequence is superseded for multi-energy acquisitions by the Multi-energy CT Acquisition Sequence (0018,9362), however implementations may encounter instances that still use this Sequence, in which case Multi-energy CT Acquisition (0018,9361) will be absent or will be set to NO. Shall not be present if Multi-energy CT Acquisition (0018,9361) is YES. |
| &gt;KVP | (0018,0060) | 1 | Peak kilo voltage output of the X-Ray generator used. |
| &gt;X-Ray Tube Current in mA | (0018,9330) | 1 | Nominal X-Ray tube current in milliamperes. |
| &gt;Data Collection Diameter | (0018,0090) | 1 | The diameter in mm of the region over which data were collected. |
| &gt;Focal Spot(s) | (0018,1190) | 1 | Used nominal size of the focal spot in mm. |
| &gt;Filter Type | (0018,1160) | 1 | Type of filter(s) inserted into the X-Ray beam. |
| &gt;Filter Material | (0018,7050) | 1 | The X-Ray absorbing material used in the filter. May be multi-valued. See [Annex P Correspondence of X-Ray Filter Material Codes and Defined Terms in PS3.16](part16.html#chapter_P) for Defined Terms. |
| &gt;Exposure in mAs | (0018,9332) | 3 | The exposure expressed in milliampere seconds, for example calculated from exposure time and X-Ray tube current. |
| &gt;Energy Weighting Factor | (0018,9353) | 1C | The weighting factor of the data from this additional source in a multiple energy composition image. This factor incorporates the effects of - the specific X-Ray source and kV value - examination specific characteristics. Required if one Derivation Code Sequence (0008,9215) Item value is [(113097, DCM, "Multi-energy proportional weighting")](part16.html#DCM_113097). May be present otherwise. |
| Include Table C.*********-1 RT Equipment Mapping and Plan Reference Macro Attributes |   |   |   |
| Include Table C.********-2 3D RT Cone-Beam Imaging Geometry Optional Macro Attributes |   |   |   |
### Note
Section 10.23 RT Equipment Correlation Macro (Retired) was previously included in this Module but has been retired. See [PS3.3-2024b](http://medical.nema.org/MEDICAL/Dicom/2024b/output/pdf/part03.pdf).
##### C.******* CT Image Module Attribute Descriptions
###### C.*******.1 Image Type
For CT Images, Image Type (0008,0008) is specified to be Type 1.
**Defined Terms for Value 3:**
AXIAL
a CT Cross-sectional Image
LOCALIZER
a CT Localizer Image
### Note
Cross-sectional images include transverse, coronal, sagittal and oblique images.
Image Type (0008,0008) Value 4 shall be present if Multi-energy CT Acquisition (0018,9361) has a Value of YES.
**Defined Terms for Value 4 for Multi-energy CT Images:**
VMI
a Virtual Monoenergetic Image. Each real-world value mapped pixel represents CT Hounsfield units and is analogous to a CT image created by a monoenergetic (of a specific keV value) X-Ray beam.
MAT_SPECIFIC
a Material-Specific Image. Each real-world value mapped pixel value represents a property of a material such as attenuation, concentration or density.
MAT_REMOVED
An image with the attenuation contribution of one or more materials removed. For pixels that did not contain any of the removed material(s), the pixel values are unchanged.
MAT_FRACTIONAL
a Material-Fractional Image. Each real-world value mapped pixel represents the fraction of a voxel occupied by a material.
EFF_ATOMIC_NUM
an Effective Atomic Number Image. Each real-world value mapped pixel represents Effective Atomic Number of the materials in the voxel.
ELECTRON_DENSITY
an Electron Density Image. Each real-world value mapped pixel represents the number of electrons per unit volume or the electron density relative to water.
MAT_MODIFIED
a Material-Modified Image. CT Image where real-world value mapped pixels have been modified to highlight a certain target material (either by partially suppressing the background or by enhancing the target material), or to partially suppress the target material.
MAT_VALUE_BASED
a Value-Based Image. CT Image where real-world value mapped pixels represent a certain value for a specified material
### Note
Multi-energy CT images are not necessarily DERIVED and may be ORIGINAL\PRIMARY.
When an image is created by a generic transformation an implementation-specific Value 4 may be provided.
###### C.*******.2 Samples per Pixel
**Enumerated Values:**
1
###### C.*******.3 Photometric Interpretation
**Enumerated Values:**
MONOCHROME1
MONOCHROME2
See Section C.7.6.3.1.2 for definition of these terms.
###### C.*******.4 Bits Allocated
**Enumerated Values:**
16
###### C.*******.5 Bits Stored
**Enumerated Values:**
12
13
14
15
16
###### C.*******.6 High Bit
For CT Images, High Bit (0028,0102) shall be one less than the Value of Bits Stored (0028,0101).
###### C.*******.7 Calcium Scoring Mass Factor Patient and Device
The calibration factors for the Calcium Scoring Mass Factor Patient (0018,9351) and Calcium Scoring Mass Factor Device (0018,9352) are defined by the International Consortium for Multi-Detector CT Evaluation of Coronary Calcium, see McCollough, C.H. "A multi-institutional, multi-manufacturer, international standard for the quantification of coronary artery calcium using cardiac CT".