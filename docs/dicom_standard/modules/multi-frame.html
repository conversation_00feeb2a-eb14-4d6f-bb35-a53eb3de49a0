<div class="section">
 <div class="titlepage">
  <div>
   <div>
    <h4 class="title">
     <a id="sect_C.7.6.6" shape="rect">
     </a>
     C.7.6.6 Multi-frame Module
    </h4>
   </div>
  </div>
 </div>
 <p>
  <a id="para_a3ab971a-a8b7-4205-b3a4-dae6fab9a931" shape="rect">
  </a>
  <a class="xref" href="#table_C.7-14" shape="rect" title="Table C.7-14. Multi-frame Module Attributes">
   Table C.7-14
  </a>
  specifies the Attributes of the
  <a class="xref" href="#sect_C.7.6.6" shape="rect" title="C.7.6.6 Multi-frame Module">
   Multi-frame Module
  </a>, which describe a Multi-frame pixel data Image.
 </p>
 <div class="table">
  <a id="table_C.7-14" shape="rect">
  </a>
  <p class="title">
   <strong>
    Table C.7-14. Multi-frame Module Attributes
   </strong>
  </p>
  <div class="table-contents">
   <table frame="box" rules="all">
    <thead>
     <tr valign="top">
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_0215757a-84e2-4bc2-9ee3-0bfde4e3e09a" shape="rect">
        </a>
        Attribute Name
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_30cf5c7b-d8a8-42fa-a344-25430cc63dcf" shape="rect">
        </a>
        Tag
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_e0015bb9-e34e-40cc-8570-034f0aa9ffef" shape="rect">
        </a>
        Type
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_99064f22-5951-46a0-9d99-cd80be1e069c" shape="rect">
        </a>
        Attribute Description
       </p>
      </th>
     </tr>
    </thead>
    <tbody>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_c6798678-5f49-4bf5-8b42-465ea686c28c" shape="rect">
        </a>
        Number of Frames
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_37c045d9-4757-4c9c-b4ae-e3a6eb483edb" shape="rect">
        </a>
        (0028,0008)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_db393087-b151-4f48-976c-ccf996e74fc5" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_cf13eb17-5479-4d78-bc50-1918596fd8f3" shape="rect">
        </a>
        Number of Frames in a Multi-frame Image. See
        <a class="xref" href="#sect_C.*******.1" shape="rect" title="C.*******.1 Number of Frames">
         Section C.*******.1
        </a>
        for further explanation.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_ca4b7856-dda6-414a-a2aa-bd0d8dfe2ff8" shape="rect">
        </a>
        Frame Increment Pointer
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_3441e9ce-46d7-4330-8f65-f69839efee02" shape="rect">
        </a>
        (0028,0009)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_ce8fcc4d-aec2-4b82-b61f-6058d90b5728" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_107c2923-faa4-4342-b772-98382661c906" shape="rect">
        </a>
        Contains the Data Element Tag of the Attribute that is used as the Frame increment in Multi-frame pixel data. See
        <a class="xref" href="#sect_C.*******.2" shape="rect" title="C.*******.2 Frame Increment Pointer">
         Section C.*******.2
        </a>
        for further explanation.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_ac40fcff-904c-495f-aff1-8b93e50f0b9e" shape="rect">
        </a>
        Stereo Pairs Present
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_fa7c039e-883a-4247-a86a-6d7140d09d12" shape="rect">
        </a>
        (0022,0028)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_350f5ad6-866b-4d23-a281-7e969ce49fdc" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_0251e2c5-8c72-4aba-bd0d-604ba34e9350" shape="rect">
        </a>
        The multi-frame pixel data consists of left and right stereoscopic pairs. See
        <a class="xref" href="#sect_C.*******.3" shape="rect" title="C.*******.3 Stereoscopic Pairs Present">
         Section C.*******.3
        </a>
        for further explanation.
       </p>
       <div class="variablelist">
        <p class="title">
         <strong>
          Enumerated Values:
         </strong>
        </p>
        <dl class="variablelist compact">
         <dt>
          <span class="term">
           YES
          </span>
         </dt>
         <dd>
          <p>
           <a id="para_14271dd2-44cf-4d5a-ba7a-c0ea14dba01a" shape="rect">
           </a>
          </p>
         </dd>
         <dt>
          <span class="term">
           NO
          </span>
         </dt>
         <dd>
          <p>
           <a id="para_67284beb-013b-4b20-8d6f-3771b2408e18" shape="rect">
           </a>
          </p>
         </dd>
        </dl>
       </div>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_a42c5caf-0873-42f4-b915-d8eef63dab70" shape="rect">
        </a>
        Encapsulated Pixel Data Value Total Length
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_b7ab9a7d-68f8-48a2-ab00-409ef522ac02" shape="rect">
        </a>
        (7FE0,0003)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_9ca5088d-0842-45d1-82e5-1440f78a81d0" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_ab512734-5ea8-4d4a-9262-084fd439ecb4" shape="rect">
        </a>
        The length of the pixel data bit stream encapsulated in Pixel Data (7FE0,0010), in bytes, when all the fragments have been combined, not including any trailing padding to even length in the last Fragment added for encapsulation.
       </p>
       <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
        <h3 class="title">
         Note
        </h3>
        <p>
         <a id="para_b5c2f2ca-53f5-43ab-a7d1-881a30ca1f15" shape="rect">
         </a>
         This Value will depend on the Transfer Syntax in which the Pixel Data (7FE0,0010) is encoded, and may need to be updated depending on the Transfer Syntax negotiated and selected for a particular transfer. See
         <a class="olink" href="part05.html#sect_8.2" shape="rect">
          PS3.5 Section 8.2 Native or Encapsulated Format Encoding
         </a>.
        </p>
       </div>
      </td>
     </tr>
    </tbody>
   </table>
  </div>
 </div>
 <br class="table-break" clear="none"/>
 <div class="section">
  <div class="titlepage">
   <div>
    <div>
     <h5 class="title">
      <a id="sect_C.*******" shape="rect">
      </a>
      C.******* Multi-frame Module Attribute Descriptions
     </h5>
    </div>
   </div>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h6 class="title">
       <a id="sect_C.*******.1" shape="rect">
       </a>
       C.*******.1 Number of Frames
      </h6>
     </div>
    </div>
   </div>
   <p>
    <a id="para_d3d36f49-02b9-40f8-bce7-c52aa298a3d3" shape="rect">
    </a>
    A Multi-frame Image is defined as a Image whose pixel data consists of a sequential set of individual Image Pixel Frames. A Multi-frame Image is transmitted as a single contiguous stream of pixels. Frame headers do not exist within the data stream.
   </p>
   <p>
    <a id="para_9f634dd5-ee84-4c51-8b3b-3f8ce3fa43b6" shape="rect">
    </a>
    Each individual Frame shall be defined (and thus can be identified) by the Attributes in the
    <a class="xref" href="#sect_C.7.6.3" shape="rect" title="C.7.6.3 Image Pixel Module">
     Image Pixel Module
    </a>
    (see
    <a class="xref" href="#sect_C.7.6.3" shape="rect" title="C.7.6.3 Image Pixel Module">
     Section C.7.6.3
    </a>
    ). All Image IE Attributes shall be related to the first Frame in the Multi-frame Image.
   </p>
   <p>
    <a id="para_1d5f78ad-b288-49f7-87e7-9dcf7c9e3ad0" shape="rect">
    </a>
    The total number of Frames contained within a Multi-frame Image is conveyed in the Number of Frames (0028,0008). Number of Frames (0028,0008) shall have a Value greater than zero.
   </p>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h6 class="title">
       <a id="sect_C.*******.2" shape="rect">
       </a>
       C.*******.2 Frame Increment Pointer
      </h6>
     </div>
    </div>
   </div>
   <p>
    <a id="para_33f1c01e-84c0-4c3a-924e-2b3988c9f688" shape="rect">
    </a>
    The Frames within a Multi-frame Image shall be conveyed as a logical sequence. The information that determines the sequential order of the Frames shall be identified by the Data Element Tag or Tags conveyed by the Frame Increment Pointer (0028,0009). Each specific Image IOD that supports the
    <a class="xref" href="#sect_C.7.6.6" shape="rect" title="C.7.6.6 Multi-frame Module">
     Multi-frame Module
    </a>
    specializes the Frame Increment Pointer (0028,0009) to identify the Attributes that may be used as sequences.
   </p>
   <p>
    <a id="para_d1dcda5a-27cc-42bb-8c38-35e53725020e" shape="rect">
    </a>
    Even if only a single Frame is present, Frame Increment Pointer (0028,0009) is still required to be present and have at least one Value, each of which shall point to an Attribute that is also present in the Data Set and has a Value.
   </p>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <p>
     <a id="para_e1e2d560-c885-4ba6-a6fa-79d8847ba7a4" shape="rect">
     </a>
     For example, in single-frame Instance of an IOD that is required to or may contain the
     <a class="xref" href="#sect_C.7.6.5" shape="rect" title="C.7.6.5 Cine Module">
      Cine Module
     </a>, it may be appropriate for Frame Time (0018,1063) to be present with a Value of 0, and be the only target of Frame Increment Pointer (0028,0009).
    </p>
   </div>
   <p>
    <a id="para_275bba6e-0a30-438d-9ea3-85843b5be6f7" shape="rect">
    </a>
    When the IOD permits the use of Multi-frame Functional Groups as a Standard or Standard Extended SOP Class, Frame Increment Pointer may contain the single Value of Per-Frame Functional Groups Sequence (5200,9230) to indicate that the Functional Groups contain the descriptors of the Frames.
   </p>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <p>
     <a id="para_ef4c8643-6bc9-4a26-9453-b506d301e341" shape="rect">
     </a>
     For example, the
     <a class="xref" href="#sect_A.8.4" shape="rect" title="A.8.4 Multi-frame Grayscale Word Secondary Capture Image IOD">
      Multi-frame Grayscale Word Secondary Capture Image IOD
     </a>
     requires the
     <a class="xref" href="#sect_C.7.6.6" shape="rect" title="C.7.6.6 Multi-frame Module">
      Multi-frame Module
     </a>
     but also permits the Multi-frame Functional Groups, for example, to describe the plane position of each Frame.
    </p>
   </div>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h6 class="title">
       <a id="sect_C.*******.3" shape="rect">
       </a>
       C.*******.3 Stereoscopic Pairs Present
      </h6>
     </div>
    </div>
   </div>
   <p>
    <a id="para_0a60dd75-8234-427a-936c-0f95e3837db4" shape="rect">
    </a>
    Stereo Pairs Present (0022,0028) shall have the Value of YES when Frames within a Multi-frame Image are encoded as left and right stereoscopic pairs.
   </p>
   <p>
    <a id="para_19b83dd0-63d0-453f-85b8-eb7300652755" shape="rect">
    </a>
    When Stereoscopic Pairs are present, and the pixel data is uncompressed, or compressed with a Transfer Syntax that does not explicitly convey the semantics of stereo pairs, the first and subsequent odd Frames (Frames numbered from 1) are the left Frame of each pair, and the second and subsequent even Frames are the right Frame of each pair.
   </p>
   <p>
    <a id="para_12e2d108-adda-4928-9d35-1a1f39fa09a4" shape="rect">
    </a>
    If the pixel data is compressed with a Transfer Syntax that does explicitly convey the semantics of stereo pairs, then the identification of the left and right Frames in the compressed pixel data will be as defined in the compressed bit stream.
   </p>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <div class="orderedlist">
     <ol class="orderedlist" type="1">
      <li class="listitem">
       <p>
        <a id="para_c8b41af3-fb3d-4931-8e6e-5c9c6791498c" shape="rect">
        </a>
        For example, the MPEG-4 AVC/H.264 Supplemental Enhancement Information (SEI) Frame Packing Arrangement (FPA) field defines various methods of encoding stereo pairs. See
        <a class="olink" href="part05.html#sect_8.2.8" shape="rect">
         PS3.5 Section 8.2.8 MPEG-4 AVC/H.264 High Profile / Level 4.2 Video Compression
        </a>. Videos encoded with this Transfer Syntax are used for what is colloquially referred to as "3D Television" applications.
        <a class="olink" href="part05.html#sect_8.2.9" shape="rect">
         PS3.5 Section 8.2.9 MPEG-4 AVC/H.264 Stereo High Profile / Level 4.2 Video Compression
        </a>
        defines a method of encoding stereo pairs without Frame packing and with 2D backwards compatibility.
       </p>
      </li>
      <li class="listitem">
       <p>
        <a id="para_fe5fdf00-4a59-4ba8-90f4-abe078e49c18" shape="rect">
        </a>
        The presence of Stereo Pairs Present (0022,0028) is independent of the use of Instances of the
        <a class="xref" href="#sect_A.43" shape="rect" title="A.43 Stereometric Relationship IOD">
         Stereometric Relationship IOD
        </a>. In particular, no further description of the method of acquisition of the stereoscopic pairs is required, such as might be present in Attributes of the Stereo Pairs Sequence (0022,0020) of the
        <a class="xref" href="#sect_A.43" shape="rect" title="A.43 Stereometric Relationship IOD">
         Stereometric Relationship IOD
        </a>. The definition of the references to left and right pairs in that IOD prohibit the encoding of the left and right pairs in the same Instance, as distinct for the usage here.
       </p>
      </li>
      <li class="listitem">
       <p>
        <a id="para_c9621b25-6577-4203-ac52-87314a1b9367" shape="rect">
        </a>
        Not all multi-frame IODs are sufficiently generic in their description to permit the presence of stereoscopic pairs. E.g., the Video Endoscopic Image IOD, Video Microscopic IOD and Video Photographic IODs are, since they do not specify any conflicting constraints on the meaning of the Frames.
       </p>
      </li>
     </ol>
    </div>
   </div>
  </div>
 </div>
</div>
