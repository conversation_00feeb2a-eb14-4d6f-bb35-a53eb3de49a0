#### C.8.8.3 RT Dose Module
The RT Dose Module is used to convey 2D or 3D radiation dose data generated from treatment planning systems or similar devices. The Attributes defined within the Module support dose for a single radiation beam (potentially comprised of multiple segments, as delivered in a dynamic treatment) or a group of beams comprising either a fraction group (see Section C.8.8.13 ) or a complete treatment plan (potentially the sum of multiple fraction groups).
The RT Dose Module provides the mechanism to transmit a 3D array of dose data as a set of 2D dose planes that may or may not be related to CT or MR image planes. This mechanism works via the DICOM Multi-frame Module that is required if multi-frame pixel data are present.
**Table C.8-39. RT Dose Module Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Samples per Pixel | (0028,0002) | 1C | Number of samples (planes) in this image. See Section C.*******.1 for specialization. Required if Pixel Data (7FE0,0010) is present. |
| Photometric Interpretation | (0028,0004) | 1C | Specifies the intended interpretation of the pixel data. See Section C.*******.2 for specialization. Required if Pixel Data (7FE0,0010) is present. |
| Bits Allocated | (0028,0100) | 1C | Number of bits allocated for each pixel sample. Each sample shall have the same number of bits allocated. See Section C.*******.3 for specialization. Required if Pixel Data (7FE0,0010) is present. |
| Bits Stored | (0028,0101) | 1C | Number of bits stored for each pixel sample. Each sample shall have the same number of bits stored. See Section C.*******.4 for specialization. Required if Pixel Data (7FE0,0010) is present. |
| High Bit | (0028,0102) | 1C | Most significant bit for each pixel sample. Each sample shall have the same high bit. See Section C.*******.5 for specialization. Required if Pixel Data (7FE0,0010) is present. |
| Pixel Representation | (0028,0103) | 1C | Data representation of the pixel samples. Each sample shall have the same pixel representation. See Section C.*******.6 for specialization. Required if Pixel Data (7FE0,0010) is present. |
| Content Date | (0008,0023) | 3 | The date the content of this Module was created. |
| Content Time | (0008,0033) | 3 | The time the content of this Module was created. |
| Dose Units | (3004,0002) | 1 | Units used to describe dose. **Enumerated Values:** GY Gray RELATIVE dose relative to implicit reference value |
| Dose Type | (3004,0004) | 1 | Type of dose. **Defined Terms:** PHYSICAL physical dose EFFECTIVE physical dose after correction for biological effect using user-defined modeling technique ERROR difference between desired and planned dose See Section C.8.8.3.6. |
| Spatial Transform of Dose | (3004,0005) | 3 | The use of transformation in the calculation of the combined dose. **Defined Terms:** NONE No transformation. Calculated on the original image set RIGID Only Rigid transform used (see definition in Section C.20.2.1.2 ) NON_RIGID Any other transform used |
| Referenced Spatial Registration Sequence | (0070,0404) | 2C | A reference to a Spatial Registration SOP Instance or a Deformable Spatial Registration SOP Instance, which defines the transformation used to transform the dose. Required, if Spatial Transform of Dose (3004,0005) is provided and has a Value of RIGID or NON_RIGID. Zero or more Items shall be included in this Sequence. See Section C.8.8.3.5 |
| &gt;Include Table 10-11 SOP Instance Reference Macro Attributes |   |   |   |
| Instance Number | (0020,0013) | 3 | A number that identifies this object Instance. |
| Entity Long Label | (3010,0038) | 3 | User-defined label for dose data. See Section 10.32.2.1. |
| Dose Comment | (3004,0006) | 3 | User-defined comments for dose data. |
| Normalization Point | (3004,0008) | 3 | Coordinates (x, y, z) of normalization point in the patient-Based Coordinate System described in Section C.7.6.2.1.1 (mm). See Section C.8.8.3.1. |
| Dose Summation Type | (3004,000A) | 1 | Type of dose summation. **Defined Terms:** PLAN dose calculated for entire delivery of all fraction groups of RT Plan MULTI_PLAN dose calculated for entire delivery of 2 or more RT Plans PLAN_OVERVIEW dose calculated with respect to plan overview parameters FRACTION dose calculated for entire delivery of a single Fraction Group within RT Plan BEAM dose calculated for entire delivery of one or more Beams within RT Plan BRACHY dose calculated for entire delivery of one or more Brachy Application Setups within RT Plan FRACTION_SESSION dose calculated for a single session ("fraction") of a single Fraction Group within RT Plan BEAM_SESSION dose calculated for a single session ("fraction") of one or more Beams within RT Plan BRACHY_SESSION dose calculated for a single session ("fraction") of one or more Brachy Application Setups within RT Plan CONTROL_POINT dose calculated for one or more Control Points within a Beam for a single fraction RECORD dose calculated for RT Beams Treatment Record |
| Referenced RT Plan Sequence | (300C,0002) | 1C | Sequence describing RT Plan associated with dose. Required if Dose Summation Type (3004,000A) is PLAN, MULTI_PLAN, FRACTION, BEAM, BRACHY, FRACTION_SESSION, BEAM_SESSION, BRACHY_SESSION or CONTROL_POINT. May be present if Dose Summation Type (3004,000A) is PLAN_OVERVIEW. Only a single Item shall be included in this Sequence, unless Dose Summation Type (3004,000A) is MULTI_PLAN, in which case two or more Items shall be included in this Sequence. See Note 1. |
| &gt;Include Table 10-11 SOP Instance Reference Macro Attributes |   |   |   |
| &gt;Referenced Plan Overview Index | (300C,0118) | 1C | The Value of Plan Overview Index (300C,0117) from the Plan Overview Sequence (300C,0116) to which this RT Plan corresponds. Shall be unique, i.e., not be duplicated within another Item of this Referenced RT Plan Sequence (300C,0002). Required if Plan Overview Sequence (300C,0116) is present. |
| &gt;Referenced Fraction Group Sequence | (300C,0020) | 1C | Sequence of one Fraction Group containing beams or brachy application setups contributing to dose. Required if Dose Summation Type (3004,000A) is FRACTION, BEAM, BRACHY, FRACTION_SESSION, BEAM_SESSION, BRACHY_SESSION or CONTROL_POINT. Only a single Item shall be included in this Sequence. See Note 1. |
| &gt;&gt;Referenced Fraction Group Number | (300C,0022) | 1 | Uniquely identifies Fraction Group specified by Fraction Group Number (300A,0071) in Fraction Group Sequence of RT Fraction Scheme Module within RT Plan referenced in Referenced RT Plan Sequence (300C,0002). |
| &gt;&gt;Referenced Beam Sequence | (300C,0004) | 1C | Sequence of Beams in current Fraction Group contributing to dose. Required if Dose Summation Type (3004,000A) is BEAM, BEAM_SESSION or CONTROL_POINT. One or more Items shall be included in this Sequence. |
| &gt;&gt;&gt;Referenced Beam Number | (300C,0006) | 1 | Uniquely identifies Beam specified by Beam Number (300A,00C0) in Beam Sequence (300A,00B0) of RT Beams Module within RT Plan referenced in Referenced RT Plan Sequence (300C,0002) or in Ion Beam Sequence (300A,03A2) of RT Ion Beams Module within RT Ion Plan referenced in Referenced RT Plan Sequence (300C,0002). |
| &gt;&gt;&gt;Referenced Control Point Sequence | (300C,00F2) | 1C | Sequence defining the Control Points in current Beam contributing to dose. Required if Dose Summation Type (3004,000A) is CONTROL_POINT. Only a single Item shall be included in this Sequence. |
| &gt;&gt;&gt;&gt;Referenced Start Control Point Index | (300C,00F4) | 1 | Identifies Control Point specified by Control Point Index (300A,0112) within Beam referenced by Referenced Beam Number (300C,0006). This is the first of the two Control Points from which the Dose contribution to the Control Point can be calculated. |
| &gt;&gt;&gt;&gt;Referenced Stop Control Point Index | (300C,00F6) | 1 | Identifies Control Point specified by Control Point Index (300A,0112) within Beam referenced by Referenced Beam Number (300C,0006). This is the second of the two Control Points from which the Dose contribution to the Control Point can be calculated. The Control Point Index (300A,0112) referenced by Referenced Stop Control Point Index (300C,00F6) shall be the Control Point Index (300A,0112) immediately following the Control Point Index (300A,0112) referenced by Referenced Start Control Point Index (300C,00F4) within the Referenced Beam Number (300C,0006). |
| &gt;&gt;Referenced Brachy Application Setup Sequence | (300C,000A) | 1C | Sequence of Brachy Application Setups in current Fraction Group contributing to dose. Required if Dose Summation Type (3004,000A) is BRACHY or BRACHY_SESSION. One or more Items shall be included in this Sequence. |
| &gt;&gt;&gt;Referenced Brachy Application Setup Number | (300C,000C) | 1 | Uniquely identifies Brachy Application Setup specified by Brachy Application Setup Number (300A,0234) in Brachy Application Setup Sequence (300A,0230) of RT Brachy Application Setups Module within RT Plan referenced in Referenced RT Plan Sequence (300C,0002). |
| Referenced Treatment Record Sequence | (3008,0030) | 1C | Sequence describing RT Beams Treatment Record associated with dose. One or more Items shall be included in this Sequence. Required if Dose Summation Type (3004,000A) is RECORD. |
| &gt;Include Table 10-11 SOP Instance Reference Macro Attributes |   |   |   |
| &gt;Referenced Beam Sequence | (300C,0004) | 1C | Sequence of Beams contributing to dose. One or more Items shall be included in this Sequence. Required, if the dose does not apply to the complete RT Beams Treatment Record referenced in the Referenced Treatment Record Sequence (3008,0030). |
| &gt;&gt;Referenced Beam Number | (300C,0006) | 1 | Uniquely identifies Beam specified by Referenced Beam Number (300C,0006) in Treatment Session Beam Sequence (3008,0020) of RT Beams Session Record Module within RT Beams Treatment Record referenced in the Referenced Treatment Record Sequence (3008,0030) or in Treatment Session Ion Beam Sequence (3008,0021) of RT Ion Beams Session Record Module within RT Ion Beams Treatment Record referenced in the Referenced Treatment Record Sequence (3008,0030). |
| Grid Frame Offset Vector | (3004,000C) | 1C | An array that contains the dose image plane offsets (in mm) of the dose image Frames in a multi-frame dose. Required if multi-frame pixel data are present and Frame Increment Pointer (0028,0009) points to Grid Frame Offset Vector (3004,000C). See Section C.*******. |
| Dose Grid Scaling | (3004,000E) | 1C | Scaling factor that when multiplied by the dose grid data found in Pixel Data (7FE0,0010) of the Image Pixel Module, yields grid doses in the dose units as specified by Dose Units (3004,0002). Required if Pixel Data (7FE0,0010) is present. |
| Tissue Heterogeneity Correction | (3004,0014) | 3 | Specifies a list of patient heterogeneity characteristics used for calculating dose. This Attribute shall be multi-valued if beams used to compute the dose have differing correction techniques. **Enumerated Values:** IMAGE image data ROI_OVERRIDE one or more ROI densities override image or water values where they exist WATER entire volume treated as water equivalent |
| Recommended Isodose Level Sequence | (3004,0016) | 3 | Recommended isodose levels that can be used in the visualization of the dose distribution. One or more Items are permitted in this Sequence. |
| &gt;Dose Value | (3004,0012) | 1 | Value for the isodose given in units defined by Dose Units (3004,0002). |
| &gt;Recommended Display CIELab Value | (0062,000D) | 1 | A default color triplet value in which it is recommended that the isodose level be rendered on a color display. The units are specified in PCS-Values, and the value is encoded as CIELab. See Section C.********. |
| Derivation Code Sequence | (0008,9215) | 3 | A coded description of how this dose was derived from other RT Dose and/or RT Plan objects. One or more Items are permitted in this Sequence. More than one Item indicates that successive derivation steps have been applied. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |   |   | D [CID 7220 RT Dose Derivation](part16.html#sect_CID_7220). |
| Referenced Instance Sequence | (0008,114A) | 3 | The set of SOP Instances used to derive this RT Dose SOP Instance from other RT Dose and/or RT Plan objects. One or more Items are permitted in this Sequence. |
| &gt;Include Table 10-11 SOP Instance Reference Macro Attributes |   |   |   |
| &gt;Purpose of Reference Code Sequence | (0040,A170) | 1 | Code describing the purpose of the reference to the Instance(s). Only a single Item is permitted in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |   |   | D [CID 7221 RT Dose Purpose of Reference](part16.html#sect_CID_7221). |
| Plan Overview Sequence | (300C,0116) | 1C | Parameters providing an overview of the plan used to create this RT Dose Instance. Required if Dose Summation Type (3004,000A) is PLAN_OVERVIEW. May be present if Dose Summation Type (3004,000A) is PLAN, MULTI_PLAN or RECORD. One or more Items shall be included in this Sequence if Dose Summation Type (3004,000A) is PLAN_OVERVIEW. Only one Item shall be included in this Sequence if Dose Summation Type (3004,000A) is PLAN or RECORD. Two or more Items shall be included in this Sequence if Dose Summation Type (3004,000A) is MULTI_PLAN. |
| &gt;Plan Overview Index | (300C,0117) | 1 | The index of the Plan Overview within this Sequence. The Value shall start at 1 and increase monotonically by 1. |
| &gt;RT Plan Label | (300A,0002) | 2 | User-defined label of treatment plan. |
| &gt;Number of Fractions Included | (300C,0119) | 1C | Number of fractions of the plan included in this RT Dose Instance. Required if Dose Summation Type (3004,000A) is PLAN_OVERVIEW, PLAN or MULTI_PLAN. If Dose Summation Type is PLAN or MULTI_PLAN the Number of Fractions Included (300C,0119) shall equal the Number of Fractions Planned (300A,0078) of the referenced RT Plan Instance. |
| &gt;Current Fraction Number | (3008,0022) | 1C | Fraction Number of the fraction included in this RT Dose Instance. Required if Dose Summation Type (3004,000A) is RECORD. |
| &gt;Treatment Site | (3010,0077) | 2 | A free-text label describing the anatomical treatment site. |
| &gt;Treatment Site Code Sequence | (3010,0078) | 2 | Coded description of the treatment site. Zero or more Items are permitted in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |   |   | B [CID 4 Anatomic Region](part16.html#sect_CID_4). |
| &gt;&gt;Treatment Site Modifier Code Sequence | (3010,0089) | 3 | Coded description of the laterality of the treatment site. Only a single Item is permitted in this Sequencee. |
| &gt;&gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |   |   | B [CID 244 Laterality](part16.html#sect_CID_244). |
| &gt;Prescription Overview Sequence | (300C,0114) | 2 | Prescription parameters for evaluation of the dose matrix. Zero or more Items shall be included in this Sequence. |
| &gt;&gt;Entity Long Label | (3010,0038) | 1C | Label identifying the Prescription Overview. For example, this may be the name of the related target ROI or the description of a Dose Reference. Required if Prescription Overview Sequence (300C,0114) has more than one Item. May be present otherwise. |
| &gt;&gt;Total Prescription Dose | (300C,0115) | 1 | Prescribed total dose in Gy for all fractions for the dose type defined in Dose Type (3004,0004). |
| &gt;&gt;Referenced ROI Number | (3006,0084) | 3 | ROI for which the prescription parameters in this Sequence apply, specified by ROI Number (3006,0022) in the Instance referenced by Referenced Structure Set Sequence (300C,0060). |
| &gt;Referenced Structure Set Sequence | (300C,0060) | 1C | Structure Set containing structures that were used to calculate the RT Dose. Required if Referenced Image Sequence (0008,1140) is not present. Only a single Item shall be included in this Sequence. |
| &gt;&gt;Include Table 10-11 SOP Instance Reference Macro Attributes |   |   |   |
| &gt;Referenced Image Sequence | (0008,1140) | 1C | Images used to calculate the RT Dose. Required if Referenced Structure Set Sequence (300C,0060) is not present. One or more Items shall be included in this Sequence. |
| &gt;&gt;Include Table 10-11 SOP Instance Reference Macro Attributes |   |   |   |
### Note
- In order to prevent misrepresentation of the dose summation: If the Dose Summation Type (3004,000A) is PLAN, then only a single Instance of RT Plan is referenced and the dose will be for the entire plan (i.e., it is not viable to combine only certain fraction groups of different plans).
- If the Dose Summation Type (3004,000A) is MULTI_PLAN, then 2 or more Instances of RT Plan may be referenced. As above, each reference will be for the entire plan.
- If the Dose Summation Type (3004,000A) is FRACTION or FRACTION_SESSION, then only a single Instance of RT PLAN and a single Fraction Group are referenced (i.e., component beams or brachy application setups are not referenced).
##### C.8.8.3.1 Normalization Point
Normalization Point (3004,0008) aids in the interpretation and subsequent use of the transmitted data. If used, it shall be a point receiving dose contributions from all referenced components of the dose summation.
##### C.******* Grid Frame Offset Vector
Grid Frame Offset Vector (3004,000C) shall be provided if a dose distribution is encoded as a Multi-frame Image. Values of the Grid Frame Offset Vector (3004,000C) shall vary monotonically and are to be interpreted as follows:
- If Grid Frame Offset Vector (3004,000C) is present and its first element is zero, this Attribute contains an array of n elements indicating the plane location of the data in the right-handed image coordinate system, relative to the position of the first dose plane transmitted, i.e., the point at which Image Position (patient) (0020,0032) is defined, with positive offsets in the direction of the cross product of the row and column directions.
- If Grid Frame Offset Vector (3004,000C) is present, its first element is equal to the third element of Image Position (Patient) (0020,0032), and Image Orientation (Patient) (0020,0037) has the Value (1,0,0,0,1,0), then Grid Frame Offset Vector contains an array of n elements indicating the plane location (patient z coordinate) of the data in the Patient-Based Coordinate System.
In future implementations, use of option a) is strongly recommended.
This Attribute is conditional since the RT Dose Module may be included even if pixel doses are not being transmitted, or the image may be a Single-frame Image. If the Multi-frame Module is present, Frame Increment Pointer (0028,0009) shall have the Enumerated Value of 3004000C (Grid Frame Offset Vector).
### Note
Option (a) can represent a rectangular-parallelepiped dose grid with any orientation with respect to the patient, while option (b) can only represent a rectangular-parallelepiped dose grid whose planes are in the transverse patient dimension and whose x- and y-axes are parallel to the patient x- and y-axes.
Example: Figure C.8.8.3-1 shows an example of plane positions for a dose grid with transverse planes.
**Figure C.8.8.3-1. Dose Grid Frame Example**
For this example, Table C.8-39b gives the Values of elements in the Grid Frame Offset Vector (3004,000C) for both relative (option (a)) and absolute (option (b)) interpretations, under the following conditions:
- The Value of Image Orientation (Patient) (0020,0037) is (1,0,0,0,1,0). I.e., the dose grid is transverse with x- and y-axes parallel to the patient x- and y-axes;
- The Value of Image Position (Patient) (0020,0032), i.e., the position of the first element of the dose grid, is (4, 5, 6); and
- The spacing between adjacent dose grid planes is 2mm (uniform).
**Table C.8-39b. Values of Dose Grid Frame Offset Vector Under Relative (A) and Absolute (B) Interpretations**
| Grid Frame Offset Vector Element | Option (a) Relative Coordinates | Option (b) Absolute Coordinates |
| --- | --- | --- |
| Z1 | 0 | 6 |
| Z2 | 2 | 8 |
| Z3 | 4 | 10 |
| ZN | 2(N-1) | 6 + 2(N-1) |
##### C.8.8.3.3 Dose Units
Dose Units are specified in the RT Dose Module. The Attribute Dose Type present in the RT Dose Module shall apply to all doses present in the RT Dose IOD.
##### C.******* Image Pixel Attributes
###### C.*******.1 Samples per Pixel
**Enumerated Values:**
1
###### C.*******.2 Photometric Interpretation
**Enumerated Values:**
MONOCHROME2
###### C.*******.3 Bits Allocated
**Enumerated Values:**
16
32
###### C.*******.4 Bits Stored
For RT Doses, Bits Stored (0028,0101) shall be equal to Bits Allocated (0028,0100).
###### C.*******.5 High Bit
For RT Doses, High Bit (0028,0102) shall be one less than the Value of Bits Stored (0028,0101).
###### C.*******.6 Pixel Representation
**Enumerated Values when Dose Type (3004,0004) = ERROR:**
0001H
two's complement integer,
**Enumerated Values when Dose Type (3004,0004) not ERROR:**
0000H
unsigned integer, otherwise.
##### C.8.8.3.5 Referenced Spatial Registration Sequence
This Sequence lists the registrations used to create the dose. It is important to note, that this Sequence does not make any statement about how the dose was calculated or about the scope of objects contributing to a summed dose.
##### C.8.8.3.6 Dose Type
If the Derivation Code Sequence (0008,9215) contains a value of [(DCM, 121377, "Composed with radiobiological effects")](part16.html#DCM_121377) then the Value of Dose Type (3004,0004) shall be EFFECTIVE.