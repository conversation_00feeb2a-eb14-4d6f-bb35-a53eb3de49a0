#### C.8.8.10 RT Prescription Module
**Table C.8-46. RT Prescription Module Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Prescription Description | (300A,000E) | 3 | User-defined description of treatment prescription. |
| Dose Reference Sequence | (300A,0010) | 3 | Sequence of Dose References. One or more Items are permitted in this Sequence. |
| &gt;Dose Reference Number | (300A,0012) | 1 | Identification number of the Dose Reference. The Value of Dose Reference Number (300A,0012) shall be unique within the RT Plan in which it is created. |
| &gt;Dose Reference UID | (300A,0013) | 3 | A unique identifier for a Dose Reference that can be used to link the same entity across multiple RT Plan objects. |
| &gt;Dose Reference Structure Type | (300A,0014) | 1 | Structure type of Dose Reference. **Defined Terms:** POINT dose reference point specified as ROI VOLUME dose reference volume specified as ROI COORDINATES point specified by Dose Reference Point Coordinates (300A,0018) SITE dose reference clinical site |
| &gt;Dose Value Purpose | (300A,061D) | 3 | Purpose(s) for which dose values in this Sequence Item are provided. **Defined Terms:** TRACKING The dose values are used for tracking. QA The dose values are used for quality assurance. See Section C.******** and Section C.********. |
| &gt;Dose Value Interpretation | (300A,068B) | 3 | Interpretation of the dose values. **Enumerated Values:** NOMINAL ACTUAL See Section C.******** and Section C.********. |
| &gt;Dose Reference Description | (300A,0016) | 3 | User-defined description of Dose Reference. |
| &gt;Referenced ROI Number | (3006,0084) | 1C | Uniquely identifies ROI representing the dose reference specified by ROI Number (3006,0022) in Structure Set ROI Sequence (3006,0020) in Structure Set Module within RT Structure Set in Referenced Structure Set Sequence (300C,0060) in RT General Plan Module. Required if Dose Reference Structure Type (300A,0014) is POINT or VOLUME. |
| &gt;Dose Reference Point Coordinates | (300A,0018) | 1C | Coordinates (x,y,z) of Reference Point in the Patient-Based Coordinate System described in Section C.*******.1 (mm). Required if Dose Reference Structure Type (300A,0014) is COORDINATES. |
| &gt;Nominal Prior Dose | (300A,001A) | 3 | Dose (in Gy) from prior treatment to this Dose Reference (e.g., from a previous course of treatment). |
| &gt;Dose Reference Type | (300A,0020) | 1 | Type of Dose Reference. **Defined Terms:** TARGET treatment target (corresponding to GTV, PTV, or CTV in [ ICRU Report 50 ] ) ORGAN_AT_RISK Organ at Risk (as defined in [ ICRU Report 50 ] ) |
| &gt;Constraint Weight | (300A,0021) | 3 | Relative importance of satisfying constraint, where high values represent more important constraints. |
| &gt;Delivery Warning Dose | (300A,0022) | 3 | The dose (in Gy) that when reached or exceeded should cause some action to be taken. |
| &gt;Delivery Maximum Dose | (300A,0023) | 3 | The maximum dose (in Gy) that can be delivered to the dose reference. |
| &gt;Target Minimum Dose | (300A,0025) | 3 | Minimum permitted dose (in Gy) to Dose Reference if Dose Reference Type (300A,0020) is TARGET. |
| &gt;Target Prescription Dose | (300A,0026) | 3 | Prescribed dose (in Gy) to Dose Reference if Dose Reference Type (300A,0020) is TARGET. |
| &gt;Target Maximum Dose | (300A,0027) | 3 | Maximum permitted dose (in Gy) to Dose Reference if Dose Reference Type (300A,0020) is TARGET. |
| &gt;Target Underdose Volume Fraction | (300A,0028) | 3 | Maximum permitted fraction (in percent) of Target to receive less than the Target Prescription Dose if Dose Reference Type (300A,0020) is TARGET and Dose Reference Structure Type (300A,0014) is VOLUME. See Section C.********. |
| &gt;Organ at Risk Full-volume Dose | (300A,002A) | 3 | Maximum dose (in Gy) to entire Dose Reference if Dose Reference Type (300A,0020) is ORGAN_AT_RISK and Dose Reference Structure Type (300A,0014) is VOLUME. |
| &gt;Organ at Risk Limit Dose | (300A,002B) | 3 | Maximum permitted dose (in Gy) to any part of Dose Reference if Dose Reference Type (300A,0020) is ORGAN_AT_RISK and Dose Reference Structure Type (300A,0014) is VOLUME. |
| &gt;Organ at Risk Maximum Dose | (300A,002C) | 3 | Maximum dose (in Gy) to non-overdosed part of Dose Reference if Dose Reference Type (300A,0020) is ORGAN_AT_RISK and Dose Reference Structure Type (300A,0014) is VOLUME. |
| &gt;Organ at Risk Overdose Volume Fraction | (300A,002D) | 3 | Maximum permitted fraction (in percent) of the Organ at Risk to receive more than the Organ at Risk Maximum Dose if Dose Reference Type (300A,0020) is ORGAN_AT_RISK and Dose Reference Structure Type (300A,0014) is VOLUME. |
##### C.******** Target Underdose Volume Fraction
If the Target Underdose Volume Fraction (300A,0028) is not present, it shall be interpreted as zero.
##### C.******** Dose Value Purpose
The Dose Value Purpose (300A,061D) allows consuming systems to identify the dose contributions that are relevant to their use-case.
Other use cases may be defined using additional Defined Terms and shall be documented in the Conformance Statement of the application.
##### C.******** Dose Value Interpretation
The dose references listed in the Dose Reference Sequence (300A,0010) may receive dose contributions accumulated from different beams. Whereas the Beam Dose (300A,0084) that is used to determine the dose contribution of a beam to the Dose Reference may be calculated individually for each beam or on a fraction level carrying a nominal dose only, the accumulated dose values may also be interpreted as nominal or actual. For potential use cases, see Table C.8.8.10.3-1, for an example, and see Section C.********.
The Enumerated Value NOMINAL indicates that the dose value associated with the dose reference is a nominal value that was provided as an input for treatment planning. Such a Value may be used to track delivered dose over a number of delivered RT Treatment Fractions in a Treatment Management System, or eventually to match against a prescribed dose value in a Hospital Information System for billing purposes.
The Enumerated Value ACTUAL indicates that the dose value associated with the dose reference was calculated during planning. Such a Value may be used for quality assurance purposes where the plan parameters are used to re-calculate the dose and where a re-calculated value is expected to match the dose value provided by the Dose Reference.
### Note
This differs from the usage of ACTUAL when recording measured values in treatment records.
**Table C.8.8.10.3-1. Dose Value Interpretation Example Use Cases**
|   | Dose Reference Structure Type (300A,0014) | VOLUME/SITE | POINT/COORDINATE |
| --- | --- | --- | --- |
| Dose Value Interpretation (300A,068B) | NOMINAL | Tracking a nominal (e.g., prescribed) dose. | Tracking a nominal (e.g., prescribed) dose ("prescribed point dose"). |
| Dose Value Interpretation (300A,068B) | ACTUAL | Tracking an actual (calculated) dose. | Comparison of calculated/measured dose values. |