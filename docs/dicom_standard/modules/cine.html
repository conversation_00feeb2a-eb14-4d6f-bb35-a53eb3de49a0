<div class="section">
 <div class="titlepage">
  <div>
   <div>
    <h4 class="title">
     <a id="sect_C.7.6.5" shape="rect">
     </a>
     C.7.6.5 Cine Module
    </h4>
   </div>
  </div>
 </div>
 <p>
  <a id="para_f2e0a899-28f2-47c2-9118-5594e60e9aaa" shape="rect">
  </a>
  <a class="xref" href="#table_C.7-13" shape="rect" title="Table C.7-13. Cine Module Attributes">
   Table C.7-13
  </a>
  specifies the Attributes of the
  <a class="xref" href="#sect_C.7.6.5" shape="rect" title="C.7.6.5 Cine Module">
   Cine Module
  </a>, which describe a Multi-frame Cine Image.
 </p>
 <div class="table">
  <a id="table_C.7-13" shape="rect">
  </a>
  <p class="title">
   <strong>
    Table C.7-13. Cine Module Attributes
   </strong>
  </p>
  <div class="table-contents">
   <table frame="box" rules="all">
    <thead>
     <tr valign="top">
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_da76d991-9d36-461e-86af-1d07cde14a9a" shape="rect">
        </a>
        Attribute Name
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_2ed664d8-63bf-4e50-8f61-baf5f698f8c2" shape="rect">
        </a>
        Tag
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_1acf3ee1-5ce8-46e6-b2d6-f2b65443bdc5" shape="rect">
        </a>
        Type
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_1679c939-8c37-4e90-91bf-b666a2c26c1a" shape="rect">
        </a>
        Attribute Description
       </p>
      </th>
     </tr>
    </thead>
    <tbody>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_1e8257da-6ed1-4f9a-ad49-b87fc40f76cd" shape="rect">
        </a>
        Preferred Playback Sequencing
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_318e241f-ef42-4b9d-bb3f-f5a51564572c" shape="rect">
        </a>
        (0018,1244)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_8675b7a2-6dfd-4771-b2ec-57783661d95b" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_80d74576-39d9-491b-ac89-cefbca62e1ca" shape="rect">
        </a>
        Describes the preferred playback sequencing for a Multi-frame Image.
       </p>
       <div class="variablelist">
        <p class="title">
         <strong>
          Enumerated Values:
         </strong>
        </p>
        <dl class="variablelist compact">
         <dt>
          <span class="term">
           0
          </span>
         </dt>
         <dd>
          <p>
           <a id="para_ee4b0758-02ef-4ada-a259-f0b301fc6719" shape="rect">
           </a>
           Looping (1,2n,1,2,n,1,2,.n,)
          </p>
         </dd>
         <dt>
          <span class="term">
           1
          </span>
         </dt>
         <dd>
          <p>
           <a id="para_0ce6a3bf-2511-48bb-b450-a9d6835097a9" shape="rect">
           </a>
           Sweeping (1,2,n,n-1,2,1,2,n,)
          </p>
         </dd>
        </dl>
       </div>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_8a29afec-57ef-4f52-9599-010618e60d35" shape="rect">
        </a>
        Frame Time
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_9946ed32-9ecd-42cf-9d01-0d82d870eb1e" shape="rect">
        </a>
        (0018,1063)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_7dfa6bb5-e81e-4b22-b18b-deadf562ef56" shape="rect">
        </a>
        1C
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_31799d3f-7bb4-40d1-9b60-d73523148d71" shape="rect">
        </a>
        Nominal time (in msec) per individual Frame. See
        <a class="xref" href="#sect_C.*******.1" shape="rect" title="C.*******.1 Frame Time">
         Section C.*******.1
        </a>
        for further explanation. Required if Frame Increment Pointer (0028,0009) points to Frame Time.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_d7fda583-fbaa-4c13-92c0-4862fbf8759c" shape="rect">
        </a>
        Frame Time Vector
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_b21dde5c-ae59-4fbf-9b9d-0d296fb75cae" shape="rect">
        </a>
        (0018,1065)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_9be4e90a-fb10-4225-8f00-ae3f867c2d31" shape="rect">
        </a>
        1C
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_811aad54-13b4-44e0-962f-69e09780108b" shape="rect">
        </a>
        An array that contains the real time increments (in msec) between Frames for a Multi-frame Image. See
        <a class="xref" href="#sect_C.*******.2" shape="rect" title="C.*******.2 Frame Time Vector">
         Section C.*******.2
        </a>
        for further explanation. Required if Frame Increment Pointer (0028,0009) points to Frame Time Vector.
       </p>
       <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
        <h3 class="title">
         Note
        </h3>
        <p>
         <a id="para_00128047-7815-4707-8295-c0dff1a814e3" shape="rect">
         </a>
         If the Value Length of this Data Element exceeds 65534 bytes and an Explicit VR Transfer Syntax is used, then the Value Representation UN can be used for this Data Element. See
         <a class="olink" href="part05.html#sect_6.2.2" shape="rect">
          PS3.5 Section 6.2.2
         </a>.
        </p>
       </div>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_c31442af-9d65-4761-9671-8eed330fa9c7" shape="rect">
        </a>
        Start Trim
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_4ce1be5f-1e4d-4861-b151-2f733e859989" shape="rect">
        </a>
        (0008,2142)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_987dbdfb-4a67-4b35-b221-7b5e1ae08545" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_4d9497ef-bde8-4246-957c-0f3c1bf4244d" shape="rect">
        </a>
        The Frame number of the first Frame of the Multi-frame Image to be displayed.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_904a95c9-ca50-4ba6-9f3e-5e2386880df7" shape="rect">
        </a>
        Stop Trim
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_ab88340e-ac18-4001-9ea0-cbf7f9576b4f" shape="rect">
        </a>
        (0008,2143)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_0f7772d3-f02a-4695-8164-d9a2dbd04e37" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_bb051c56-faca-454f-8d6b-326e4176252a" shape="rect">
        </a>
        The Frame Number of the last Frame of a Multi-frame Image to be displayed.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_0cd5e7ab-5626-44ad-be37-c6be843fad0d" shape="rect">
        </a>
        Recommended Display Frame Rate
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_855b8adc-e2fa-4016-91db-22ccb099f2bc" shape="rect">
        </a>
        (0008,2144)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_317f3b8c-f3ad-4f0b-b7de-904103fa9f7d" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_17fa8ff6-e22e-469a-9408-72e203e1ca99" shape="rect">
        </a>
        Recommended rate at which the Frames of a Multi-frame Image should be displayed in Frames/second.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_e2d4061e-e782-497a-9bc6-89451852a33e" shape="rect">
        </a>
        Cine Rate
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_f2cf36f7-06b0-43ec-8a44-1ebe75e69cea" shape="rect">
        </a>
        (0018,0040)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_9d16a866-9d52-4c0f-a18a-26f1950ab561" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_9e147fe0-59ba-4e02-aeca-b267e707078d" shape="rect">
        </a>
        Number of Frames per second.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_c0f9a68f-5e99-4ad7-94ce-f22b641bdfb4" shape="rect">
        </a>
        Frame Delay
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_9a3914f7-437e-49ca-9516-526c201c8316" shape="rect">
        </a>
        (0018,1066)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_c3fdbe49-3c4d-4d45-ab08-c5339d2842de" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_5c88baa7-ef1e-4ba1-89cc-8a4f9c18195f" shape="rect">
        </a>
        Time (in msec) from Content Time (0008,0033) to the start of the first Frame in a Multi-frame Image.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_3746d94b-7984-4305-84e8-40655297bbbc" shape="rect">
        </a>
        Image Trigger Delay
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_74a5f5bd-b6c2-46d5-b6c9-109098374bb9" shape="rect">
        </a>
        (0018,1067)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_d0ee9893-9bba-405d-bdb5-8f37733627f8" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_01adbbdc-17d9-4ef6-812d-328feb070f21" shape="rect">
        </a>
        Delay time in milliseconds from trigger (e.g., X-Ray on pulse) to the first Frame of a Multi-frame Image.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_d2ddd035-de85-403c-a6cb-ceebfdbd5355" shape="rect">
        </a>
        Effective Duration
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_8ef6560f-4ef1-42b0-b43b-623ec11eb09d" shape="rect">
        </a>
        (0018,0072)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_412864e3-d0c5-4c1f-9f68-a18a7298f70a" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_d674b316-2409-467a-ae93-b303f11b48d4" shape="rect">
        </a>
        Total time in seconds that data was actually taken for the entire Multi-frame Image.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_58d355f8-4d8e-4f28-b2bd-e419fa9d783c" shape="rect">
        </a>
        Actual Frame Duration
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_6ff8e0ec-3150-44cc-b944-6d49694197f2" shape="rect">
        </a>
        (0018,1242)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_520fbee9-1bcc-4659-83dd-d51efe817022" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_554f96b8-5bc8-4f98-b725-2022b33a7d13" shape="rect">
        </a>
        Elapsed time of data acquisition in msec per each Frame.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_07e216e2-debf-4ea0-b9dd-11607805fd7f" shape="rect">
        </a>
        Multiplexed Audio Channels Description Code Sequence
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_36a4a626-b99c-45cc-b917-fcab0aecfdc8" shape="rect">
        </a>
        (003A,0300)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_18945f8b-5c44-422b-b42e-ccdc99fab3cb" shape="rect">
        </a>
        2C
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_ca08b959-0963-43b9-82d0-893073e5e64c" shape="rect">
        </a>
        Description of any multiplexed audio channels. See
        <a class="xref" href="#sect_C.*******.3" shape="rect" title="C.*******.3 Multiplexed Audio">
         Section C.*******.3
        </a>.
       </p>
       <p>
        <a id="para_569419ee-db58-45da-8348-eee2c0f036ba" shape="rect">
        </a>
        Zero or more Items may be included in this Sequence.
       </p>
       <p>
        <a id="para_e8c2fc0e-fa7a-4536-8542-459ff0f61163" shape="rect">
        </a>
        Required if the Transfer Syntax used to encode the Multi-frame Image contains multiplexed (interleaved) audio channels, such as is possible with MPEG2 Systems (see
        <a class="xref" href="#biblio_ISOIEC13818-1" shape="rect" title="Information technology - Generic coding of moving pictures and associated audio information: Systems">
         [
         <abbr class="abbrev">
          ISO/IEC 13818-1
         </abbr>
         ]
        </a>
        ).
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_b7c869db-2ef3-41a3-9a9b-69a9fa1946c9" shape="rect">
        </a>
        &gt;Channel Identification Code
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_1bf92099-6a4f-4171-89d4-ff612437e762" shape="rect">
        </a>
        (003A,0301)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_68e36d26-fb54-4890-bea0-2c9bf30b1e91" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_beb87815-d663-44ec-9ef6-cb00ec9288db" shape="rect">
        </a>
        A reference to the audio channel as identified within Transfer Syntax encoded bit stream (1 for the main channel, 2 for the second channel and 3 to 9 to the complementary channels).
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_1278b8db-baf1-45f4-967f-31debb016639" shape="rect">
        </a>
        &gt;Channel Mode
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_80d7b1c2-5fb1-4a9f-8c73-e81cb488bde7" shape="rect">
        </a>
        (003A,0302)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_3ff2a498-c078-4219-a874-007768e55742" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_2377f446-8589-4e0d-8c79-d21a2b064228" shape="rect">
        </a>
        A coded descriptor qualifying the mode of the channel:
       </p>
       <div class="variablelist">
        <p class="title">
         <strong>
          Enumerated Values:
         </strong>
        </p>
        <dl class="variablelist compact">
         <dt>
          <span class="term">
           MONO
          </span>
         </dt>
         <dd>
          <p>
           <a id="para_0605f111-3fb4-4809-a291-5af5d8c68392" shape="rect">
           </a>
           1 signal
          </p>
         </dd>
         <dt>
          <span class="term">
           STEREO
          </span>
         </dt>
         <dd>
          <p>
           <a id="para_092ae269-e7b8-4a21-8569-fb2c986fea1b" shape="rect">
           </a>
           2 simultaneously acquired (left and right) signals
          </p>
         </dd>
        </dl>
       </div>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_e368517d-7bf3-4dd6-ab66-5fa55413a33e" shape="rect">
        </a>
        &gt;Channel Source Sequence
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_68790a1c-2a56-4dbf-92f5-7523314dcbaf" shape="rect">
        </a>
        (003A,0208)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_033794a5-ddb7-4443-9973-5ee97f1d3cec" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_37c961fa-65ff-47ee-8ed2-3d6115e74e36" shape="rect">
        </a>
        A coded descriptor of the audio channel source.
       </p>
       <p>
        <a id="para_2433b5cf-5695-4d00-ae43-70f030d4a3ec" shape="rect">
        </a>
        Only a single Item shall be included in this Sequence.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="3" rowspan="1">
       <p>
        <a id="para_e401b4c9-1f40-4921-9365-fd9bd2b4e2af" shape="rect">
        </a>
        <span class="italic">
         &gt;&gt;Include
         <a class="xref" href="#table_8.8-1" shape="rect" title="Table 8.8-1. Code Sequence Macro Attributes">
          Table 8.8-1 Code Sequence Macro Attributes
         </a>
        </span>
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_29bf3d99-28ad-402d-862d-7c0adf5106ee" shape="rect">
        </a>
        <span class="italic">
         D
         <a class="olink" href="part16.html#sect_CID_3000" shape="rect">
          CID 3000 Audio Channel Source
         </a>.
        </span>
       </p>
      </td>
     </tr>
    </tbody>
   </table>
  </div>
 </div>
 <br class="table-break" clear="none"/>
 <div class="section">
  <div class="titlepage">
   <div>
    <div>
     <h5 class="title">
      <a id="sect_C.*******" shape="rect">
      </a>
      C.******* Cine Module Attribute Descriptions
     </h5>
    </div>
   </div>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h6 class="title">
       <a id="sect_C.*******.1" shape="rect">
       </a>
       C.*******.1 Frame Time
      </h6>
     </div>
    </div>
   </div>
   <p>
    <a id="para_9e457687-f3d2-4ec0-9c78-b5ab35af2774" shape="rect">
    </a>
    Frame Time (0018,1063) is the nominal time (in milliseconds) between individual Frames of a Multi-frame Image. If the Frame Increment Pointer points to this Attribute, Frame Time shall be used in the following manner to calculate 'the relative time' for each Frame:
   </p>
   <p>
    <a id="para_837d3907-1518-4d5e-b13e-3a14ff1e73cc" shape="rect">
    </a>
    Frame 'Relative Time' (n) = Frame Delay + Frame Time * (n-1)
   </p>
   <p>
    <a id="para_21239c4e-8bc2-4805-87dd-c1267b6c2dc3" shape="rect">
    </a>
    where: n = number of Frame within the Multi-frame Image and the first Frame number is one
   </p>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <p>
     <a id="para_85322a09-6497-4470-b6a1-20ded6dd435e" shape="rect">
     </a>
     When there is only one Frame present, Frame Time (0018,1063) may have either a Value of 0, or a nominal value that would apply if there were multiple Frames.
    </p>
   </div>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h6 class="title">
       <a id="sect_C.*******.2" shape="rect">
       </a>
       C.*******.2 Frame Time Vector
      </h6>
     </div>
    </div>
   </div>
   <p>
    <a id="para_8db2627c-36cd-4386-803c-56c28a0d26a4" shape="rect">
    </a>
    Frame Time Vector (0018,1065) is an array that contains the time increments (in milliseconds) between the nth Frame and the previous Frame for a Multi-frame Image. The first Frame always has a time increment of 0. If the Frame Increment Pointer points to this Attribute, the Frame Time Vector shall be used in the following manner to calculate 'relative time'
    <span class="italic">
     T(n)
    </span>
    for Frame
    <span class="italic">
     n
    </span>
    :
   </p>
   <p>
    <a id="para_bba149d1-ec8f-42b3-b116-ab9bb6192df8" shape="rect">
    </a>
   </p>
   <div class="informalequation">
    <object data="figures/part03_withmml_image_3.svg" style="position: relative; bottom: -15.701550000000001px;" type="image/svg+xml">
     <param name="src" value="image_3.svg"/>
    </object>
   </div>
   <p>
   </p>
   <p>
    <a id="para_0a8890f2-4280-4063-a9ec-3eac1e2cc424" shape="rect">
    </a>
    where t
    <sub>
     i
    </sub>
    is the i
    <sup>
     th
    </sup>
    Frame Time Vector component.
   </p>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h6 class="title">
       <a id="sect_C.*******.3" shape="rect">
       </a>
       C.*******.3 Multiplexed Audio
      </h6>
     </div>
    </div>
   </div>
   <p>
    <a id="para_f17601b0-55ee-4184-8657-4dbdd154b48c" shape="rect">
    </a>
    During a video acquisition, audio may be used for voice commentary of what is being observed, as well as to record sound-based physiological information such as Doppler audio.
   </p>
   <p>
    <a id="para_2cf35cd3-9b39-4ce5-8169-7e67175c16ae" shape="rect">
    </a>
    Some Transfer Syntaxes allow for the multiplexing of interleaved audio with video data, and the Attributes of the
    <a class="xref" href="#sect_C.7.6.5" shape="rect" title="C.7.6.5 Cine Module">
     Cine Module
    </a>
    support this encoding paradigm. They are not intended to describe audio acquired simultaneously when it is encoded in other SOP Instances or within Attributes other than Pixel Data (7FE0,0010) of the same SOP Instance.
   </p>
   <p>
    <a id="para_920da8ca-0bf2-4d81-8ad2-bce5da14421c" shape="rect">
    </a>
    Synchronization between audio and video is assumed to be encoded at the Transfer Syntax level (i.e., within the encoded bit stream).
   </p>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <p>
     <a id="para_e02b5805-11ad-47e5-9147-a0069918207d" shape="rect">
     </a>
     If no audio was recorded, the Multiplexed Audio Channels Description Code Sequence (003A,0300) will be present and contain no Items.
    </p>
   </div>
  </div>
 </div>
</div>
