#### C.7.2.1 General Study Module
Table C.7-3 specifies the Attributes, which identify and describe the Study performed upon the Patient.
**Table C.7-3. General Study Module Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Study Instance UID | (0020,000D) | 1 | Unique identifier for the Study. |
| Study Date | (0008,0020) | 2 | Date the Study started. |
| Study Time | (0008,0030) | 2 | Time the Study started. |
| Referring Physician's Name | (0008,0090) | 2 | Name of the Patient's referring physician. |
| Referring Physician Identification Sequence | (0008,0096) | 3 | Identification of the Patient's referring physician. Only a single Item is permitted in this Sequence. |
| &gt;Include Table 10-1 Person Identification Macro Attributes |   |   |   |
| Consulting Physician's Name | (0008,009C) | 3 | Consulting physician(s) for this Patient Visit. |
| Consulting Physician Identification Sequence | (0008,009D) | 3 | Identification of the consulting physician(s). One or more Items are permitted in this Sequence. If more than one Item, the number and order shall correspond to the Value of Consulting Physician's Name (0008,009C), if present. |
| &gt;Include Table 10-1 Person Identification Macro Attributes |   |   |   |
| Study ID | (0020,0010) | 2 | User or equipment generated Study identifier. |
| Accession Number | (0008,0050) | 2 | A departmental Information System generated number that identifies the Imaging Service Request. |
| Issuer of Accession Number Sequence | (0008,0051) | 3 | Identifier of the Assigning Authority that issued the Accession Number. Only a single Item is permitted in this Sequence. |
| &gt;Include Table 10-17 HL7v2 Hierarchic Designator Macro Attributes |   |   |   |
| Study Description | (0008,1030) | 3 | Institution-generated description or classification of the Study performed. |
| Physician(s) of Record | (0008,1048) | 3 | Names of the physician(s) who are responsible for overall Patient care at time of Study (see Section C.7.3.1 for Performing Physician). |
| Physician(s) of Record Identification Sequence | (0008,1049) | 3 | Identification of the physician(s) who are responsible for overall Patient care at time of Study. One or more Items are permitted in this Sequence. If more than one Item, the number and order shall correspond to the Value of Physician(s) of Record (0008,1048), if present. |
| &gt;Include Table 10-1 Person Identification Macro Attributes |   |   |   |
| Name of Physician(s) Reading Study | (0008,1060) | 3 | Names of the physician(s) reading the Study. |
| Physician(s) Reading Study Identification Sequence | (0008,1062) | 3 | Identification of the physician(s) reading the Study. One or more Items are permitted in this Sequence. If more than one Item, the number and order shall correspond to the Value of Name of Physician(s) Reading Study (0008,1060), if present. |
| &gt;Include Table 10-1 Person Identification Macro Attributes |   |   |   |
| Requesting Service | (0032,1033) | 3 | Institutional department, unit or service where the request initiated. See Note 1 and Note 2. |
| Requesting Service Code Sequence | (0032,1034) | 3 | Institutional department, unit or service where the request initiated. Only a single Item is permitted in this Sequence. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |   |   | D [CID 7030 Institutional Department/Unit/Service](part16.html#sect_CID_7030). |
| Referenced Study Sequence | (0008,1110) | 3 | A Sequence that provides reference to a Study. One or more Items are permitted in this Sequence. See Section 10.6.1. |
| &gt;Include Table 10-11 SOP Instance Reference Macro Attributes |   |   |   |
| Procedure Code Sequence | (0008,1032) | 3 | A Sequence that conveys the type of procedure performed. One or more Items are permitted in this Sequence. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |   |   | B [CID 101 Imaging Procedure](part16.html#sect_CID_101). |
| Reason For Performed Procedure Code Sequence | (0040,1012) | 3 | Coded reason(s) for performing this procedure. ### Note May differ from the Values in Reason for the Requested Procedure (0040,100A) in Request Attribute Sequence (0040,0275), for example if what was performed differs from what was requested. One or more Items are permitted in this Sequence. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |   |   | No Baseline CID is defined. |
### Note
- Both Requesting Service (0032,1033) and Requesting Service Code Sequence (0032,1034) are defined in this table; the latter is preferred, but the former may be copied from an HL7 message whether or not there is also the ability to map it to a coded form (e.g., with only ORC-17 component 1 valued).
- Requesting Service (0032,1033) and/or Requesting Service Code Sequence (0032,1034) may be used in the absence of a specific request or order, to describe the service involved or associated with acquiring the Study.
##### C.******* General Study Module Attribute Descriptions
###### C.*******.1 Referring Physician, Physician of Record, Physician Reading Study, Consulting Physician
The model used for application of Attributes related to different functions of Physicians involved in the care of a Patient is shown in Figure C.7.2-1. Figure C.7.2-1 shows some of the relationships and information flows between physician roles that might be present in some healthcare business models.
**Figure C.7.2-1. Functions of Physicians**
There can be an overlap of functions provided by any given physician. In this case, the various Attributes specifying the different roles would convey the same physician name.