#### C.7.4.2 Synchronization Module
Table C.7-7 specifies the Attributes of the Synchronization Module, which are necessary to uniquely identify a Frame of Reference that establishes the temporal relationship of SOP Instances. A synchronized environment may be established based on a shared time of day clock, and/or on a shared trigger event or synchronization waveform channel.
### Note
Within a synchronized environment, different devices may use the shared data differently. An electrical pulse, for example, may be treated as a trigger event by one device (e.g., an X-Ray imaging system), but may be recorded as a synchronization waveform by another device (e.g., a hemodynamics system).
**Table C.7-7. Synchronization Module Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Synchronization Frame of Reference UID | (0020,0200) | 1 | UID of common synchronization environment. See Section C.*******.1. |
| Synchronization Trigger | (0018,106A) | 1 | Data acquisition synchronization with external equipment **Enumerated Values:** SOURCE this equipment provides synchronization channel or trigger to other equipment EXTERNAL this equipment receives synchronization channel or trigger from other equipment PASSTHRU this equipment receives synchronization channel or trigger and forwards it NO TRIGGER data acquisition not synchronized by common channel or trigger |
| Trigger Source or Type | (0018,1061) | 3 | Specifies equipment ID of trigger source and/or type of trigger. |
| Synchronization Channel | (0018,106C) | 1C | Identifier of waveform channel that records the synchronization channel or trigger, see Section C.*******.3. Required if synchronization channel or trigger is encoded in a waveform in this SOP Instance. |
| Acquisition Time Synchronized | (0018,1800) | 1 | Acquisition DateTime (0008,002A) synchronized with external time reference. **Enumerated Values:** Y N See Section C.*******.4 |
| Time Source | (0018,1801) | 3 | ID of equipment or system providing time reference. |
| Time Distribution Protocol | (0018,1802) | 3 | Method of time distribution used to synchronize this equipment. **Enumerated Values:** NTP Network Time Protocol IRIG Inter Range Instrumentation Group GPS Global Positioning System SNTP Simple Network Time Protocol PTP IEEE 1588 Precision Time Protocol |
| NTP Source Address | (0018,1803) | 3 | IP Address of NTP, SNTP, or PTP time source. IPv4 addresses shall be in dotted decimal (e.g., ***********). The IPv6 addresses shall be in colon separated hexadecimal (e.g., 12:34:56:78:9a:bc:de:f0). ### Note Identity of this value in two Instances acquired contemporaneously implies a common time base. The NTP Source Address might not persist over time. |
##### C.******* Synchronization Module Attribute Descriptions
###### C.*******.1 Synchronization Frame of Reference UID
A set of equipment may share a common acquisition synchronization environment, which is identified by a Synchronization Frame of Reference UID (0020,0200). All SOP Instances that share the same Synchronization Frame of Reference UID (0020,0200) shall be temporally related to each other. If a Synchronization Frame of Reference UID (0020,0200) is present, all SOP Instances in the Series must share the same Frame of Reference.
The UTC Synchronization UID, 1.2.840.10008.15.1.1, may be used when the equipment is synchronized to the international standard UTC. In this case the quality of synchronization may be determined by means of the Time Distribution Protocol (0018,1802) and NTP Source Address (0018,1803).
### Note
- The Synchronization Frame of Reference UID (0020,0200) defines an equipment synchronization environment, and does not need to be changed for each unrelated acquisition. SOP Instances may therefore share a Synchronization Frame of Reference UID (0020,0200), but be clinically unrelated (e.g., apply to different Patients).
- When a synchronization environment is recalibrated, a new Synchronization Frame of Reference UID (0020,0200) must be issued.
- The method of distributing the Synchronization Frame of Reference UID (0020,0200) to multiple devices is not specified.
###### C.*******.2 Time Source and Time Distribution Protocol
Time may originate with a primary source (e.g., a national standards bureau) and be distributed through a chain of secondary distribution systems until reaching the imaging equipment. Time Distribution Protocol (0018,1802) specifies the immediate (last link) method used by the equipment to receive time from the immediately prior Time Source (0018,1801). It does not specify the ultimate time reference from which the Time Source may derive its synchronization.
### Note
The time value distributed through the specified Time Distribution Protocol may need to be corrected to align with UTC. For example, GPS does not compensate for leap seconds.
###### C.*******.3 Synchronization Channel
Synchronization Channel (0018,106C) is specified as a pair of Values (M,C), where the first Value is the ordinal of the Sequence Item of Waveform Sequence (5400,0100) (i.e., the Multiplex Group), and the second Value is the ordinal of the Sequence Item of the Channel Definition Sequence (003A,0200) (i.e., the Waveform Channel Number) within the multiplex group.
###### C.*******.4 Acquisition Time Synchronized
The Acquisition Time Synchronized (0018,1800) specifies whether Acquisition DateTime (0008,002A) of the Waveform Identification Module or the General Image Module represents an accurate synchronized timestamp for the acquisition of the waveform and/or image data. For triggered Multi-frame Images, the Acquisition DateTime applies to the trigger for the first image Frame (see Attribute Image Trigger Delay (0018,1067) in the Cine Module ).
### Note
The degree of precision of the Acquisition DateTime and its accuracy relative to the external clock are not specified, but need to be appropriate for the clinical application.
For IODs that include the SR Document Content Module, the Acquisition Time Synchronized (0018,1800) specifies whether Observation DateTime (0040,A032) of Items in Content Sequence (0040,A730) of the SR Document Content Module represents an accurate synchronized timestamp for the Item.
For IODs that include the RT Radiation Record Common Module, the Acquisition Time Synchronized (0018,1800) specifies whether the following Attributes represent a synchronized timestamp.
- Recorded RT Control Point DateTime (300A,073A)
- Interlock DateTime (300A,0741)