<div class="section">
 <div class="titlepage">
  <div>
   <div>
    <h3 class="title">
     <a id="sect_C.9.2" shape="rect">
     </a>
     C.9.2 Overlay Plane Module
    </h3>
   </div>
  </div>
 </div>
 <p>
  <a id="para_0f685bbe-20ca-450d-9a78-a6e44e0f8dda" shape="rect">
  </a>
  <a class="xref" href="#table_C.9-2" shape="rect" title="Table C.9-2. Overlay Plane Module Attributes">
   Table C.9-2
  </a>
  specifies the Attributes of the
  <a class="xref" href="#sect_C.9.2" shape="rect" title="C.9.2 Overlay Plane Module">
   Overlay Plane Module
  </a>, which describe characteristics of an Overlay Plane.
 </p>
 <p>
  <a id="para_8b875843-35e8-40f1-8f87-6496b3f06538" shape="rect">
  </a>
  An Overlay Plane describes graphics or bit-mapped text that is associated with an Image. It may also describe a Region of Interest in an Image.
 </p>
 <p>
  <a id="para_573ff924-6677-4461-bd3d-2597f1ab1c05" shape="rect">
  </a>
  Each Overlay Plane is one bit deep. Sixteen separate Overlay Planes may be associated with an Image.
 </p>
 <p>
  <a id="para_6e2554d6-88e1-4810-bc1d-b8b77fe91ad5" shape="rect">
  </a>
  Overlay data is stored in Overlay Data (60xx,3000). See the Section Repeating Groups in
  <a class="olink" href="part05.html#PS3.5" shape="rect">
   PS3.5
  </a>
  for a description of permitted values of 60xx.
 </p>
 <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
  <h3 class="title">
   Note
  </h3>
  <p>
   <a id="para_271e05ae-a62f-4a9d-9043-625434386149" shape="rect">
   </a>
   Overlay data stored in unused bit planes of Pixel Data (7FE0,0010) with Samples per Pixel (0028,0002) of 1 was previously described in DICOM. This usage has now been retired. See
   <a class="link" href="http://medical.nema.org/MEDICAL/Dicom/2004/printed/04_03pu3.pdf" shape="rect" target="_top">
    PS3.3-2004
   </a>
   and
   <a class="link" href="http://medical.nema.org/MEDICAL/Dicom/2004/printed/04_05pu.pdf" shape="rect" target="_top">
    PS3.5-2004
   </a>.
  </p>
 </div>
 <p>
  <a id="para_92786362-31e6-4ce6-b83d-8b1630d2b0c4" shape="rect">
  </a>
  Attributes describing display of grayscale and color overlays were defined in a previous release of the DICOM Standard. These have now been retired. How an Overlay Plane is rendered is undefined; specifically there is no mechanism to specify with what color or intensity an Overlay Plane is to be displayed, except when rendered under the control of a Softcopy Presentation State SOP Instance.
 </p>
 <div class="table">
  <a id="table_C.9-2" shape="rect">
  </a>
  <p class="title">
   <strong>
    Table C.9-2. Overlay Plane Module Attributes
   </strong>
  </p>
  <div class="table-contents">
   <table frame="box" rules="all">
    <thead>
     <tr valign="top">
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_a47e60eb-135d-4352-b716-fedc0d248574" shape="rect">
        </a>
        Attribute Name
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_8189019d-5b18-43a2-bc49-89a105a4642b" shape="rect">
        </a>
        Tag
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_f849d0df-82b5-4f3f-95b5-0c9d8423bf31" shape="rect">
        </a>
        Type
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_d0c9410f-4436-4de5-a5f7-a6b5a51087dd" shape="rect">
        </a>
        Attribute Description
       </p>
      </th>
     </tr>
    </thead>
    <tbody>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_7aec5639-7fe2-4351-9e77-a9f403caac41" shape="rect">
        </a>
        Overlay Rows
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_99c381f2-f551-48ae-8731-704c71d8e0d8" shape="rect">
        </a>
        (60xx,0010)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_62dcbc95-c1bf-4f6f-9d43-5e6f25b3c7b5" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_063d54b5-5662-4c2f-bb6d-a209a4fa7b86" shape="rect">
        </a>
        Number of Rows in Overlay.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_865ded6e-ad4e-4ce3-8181-965b59b4c0f6" shape="rect">
        </a>
        Overlay Columns
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_8b3c04de-0437-414b-ac2e-f767378f626f" shape="rect">
        </a>
        (60xx,0011)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_0f8ad061-8c8d-472a-915b-e3cb45bd609a" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_0fdca81b-b731-4019-a10e-b5dd7a71231b" shape="rect">
        </a>
        Number of Columns in Overlay.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_828b8dfb-3049-4a2b-921e-c493e936a985" shape="rect">
        </a>
        Overlay Type
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_14bad9fd-5d2d-4b79-95ed-1a7b9b60c959" shape="rect">
        </a>
        (60xx,0040)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_faa492d9-94c4-449d-9249-2b590b3807ab" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_66eee04c-0c16-4782-9f12-4197f7910919" shape="rect">
        </a>
        Indicates whether this overlay represents a region of interest or other graphics.
       </p>
       <div class="variablelist">
        <p class="title">
         <strong>
          Enumerated Values:
         </strong>
        </p>
        <dl class="variablelist compact">
         <dt>
          <span class="term">
           G
          </span>
         </dt>
         <dd>
          <p>
           <a id="para_549cfbdf-46a4-4ac5-82a1-34fe9e4787e9" shape="rect">
           </a>
           Graphics
          </p>
         </dd>
         <dt>
          <span class="term">
           R
          </span>
         </dt>
         <dd>
          <p>
           <a id="para_28f4080b-23b9-4b39-8d24-1d226508d609" shape="rect">
           </a>
           ROI
          </p>
         </dd>
        </dl>
       </div>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_d6994ab3-6ae0-4f97-bef0-9116262d2bf0" shape="rect">
        </a>
        Overlay Origin
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_6d2e4911-06a5-4b71-9e24-458d2aff43c7" shape="rect">
        </a>
        (60xx,0050)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_3da8669e-e9c9-4344-8d97-f7f967304889" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_e5de24f9-4501-48a1-b784-401e19d56de8" shape="rect">
        </a>
        Location of first overlay point with respect to pixels in the image, given as row\column.
       </p>
       <p>
        <a id="para_13218178-0a93-4a89-804f-f82638513ab5" shape="rect">
        </a>
        The upper left pixel of the image has the coordinate 1\1.
       </p>
       <p>
        <a id="para_66a321d5-c594-4ed4-970b-8009542fa01e" shape="rect">
        </a>
        Column values greater than 1 indicate the overlay plane origin is to the right of the image origin. Row values greater than 1 indicate the overlay plane origin is below the image origin. Values less than 1 indicate the overlay plane origin is above or to the left of the image origin.
       </p>
       <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
        <h3 class="title">
         Note
        </h3>
        <p>
         <a id="para_698fb6e3-4667-46b4-9886-07e33aabd8b4" shape="rect">
         </a>
         Values of 0\0 indicate that the overlay pixels start 1 row above and one column to the left of the image pixels.
        </p>
       </div>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_481f8752-394b-4f92-ab76-c1a8261baeb4" shape="rect">
        </a>
        Overlay Bits Allocated
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_5fa12727-aa27-4355-bc38-23fafe257625" shape="rect">
        </a>
        (60xx,0100)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_e512220e-7413-4f40-8420-ecacc3246875" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_4d81201a-6597-45eb-998c-3482d60bf762" shape="rect">
        </a>
        Number of Bits Allocated in the Overlay.
       </p>
       <p>
        <a id="para_6cabe573-c29a-40c1-abe0-d93e396a3a26" shape="rect">
        </a>
        The Value of this Attribute shall be 1.
       </p>
       <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
        <h3 class="title">
         Note
        </h3>
        <p>
         <a id="para_2b05bb80-945d-4097-b644-005f3c89d1f9" shape="rect">
         </a>
         Formerly the Standard described embedding the overlay data in the Image Pixel Data (7FE0,0010), in which case the Value of this Attribute was required to be the same as Bits Allocated (0028,0100). This usage has been retired. See
         <a class="link" href="http://medical.nema.org/MEDICAL/Dicom/2004/printed/04_03pu3.pdf" shape="rect" target="_top">
          PS3.3-2004
         </a>.
        </p>
       </div>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_ca04b160-82b2-4e61-9667-27601ebe057c" shape="rect">
        </a>
        Overlay Bit Position
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_f8f90260-fc7d-46f2-a7bf-cac664f5f309" shape="rect">
        </a>
        (60xx,0102)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_fce3f145-72be-45f2-bb94-4c0c58f255e4" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_d3856479-df00-4e27-9086-98a5ddd42982" shape="rect">
        </a>
        The Value of this Attribute shall be 0.
       </p>
       <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
        <h3 class="title">
         Note
        </h3>
        <p>
         <a id="para_897da523-8536-4e28-8b0b-10feebf42802" shape="rect">
         </a>
         Formerly the Standard described embedding the overlay data in the Image Pixel Data (7FE0,0010), in which case the Value of this Attribute specified the bit in which the overlay was stored. This usage has been retired. See
         <a class="link" href="http://medical.nema.org/MEDICAL/Dicom/2004/printed/04_03pu3.pdf" shape="rect" target="_top">
          PS3.3-2004
         </a>.
        </p>
       </div>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_50f8913a-e324-44fc-92ce-7708907f9217" shape="rect">
        </a>
        Overlay Data
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_dfe64a56-aa5a-4c0e-a67d-2cb8acea1605" shape="rect">
        </a>
        (60xx,3000)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_06d68e4b-3755-47c7-852b-c1eaf739a28b" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_236695fb-da03-4a57-8c1c-122ce920a48c" shape="rect">
        </a>
        Overlay pixel data.
       </p>
       <p>
        <a id="para_add2f169-9f95-4ffa-a700-a6d2228178e2" shape="rect">
        </a>
        The order of pixels encoded for each overlay is left to right, top to bottom, i.e., the upper left pixel is encoded first followed by the remainder of the first row, followed by the first pixel of the 2
        <sup>
         nd
        </sup>
        row, then the remainder of the 2
        <sup>
         nd
        </sup>
        row and so on.
       </p>
       <p>
        <a id="para_a5a728b6-6330-4e8a-97c8-7ef8eba2192c" shape="rect">
        </a>
        Overlay data shall be contained in this Attribute.
       </p>
       <p>
        <a id="para_072485a4-7923-4c81-ba70-67bcb917e94d" shape="rect">
        </a>
        See
        <a class="xref" href="#sect_C.*******" shape="rect" title="C.******* Overlay Type">
         Section C.*******
        </a>
        for further explanation.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_c8babe41-a782-4737-a175-94613dc66c64" shape="rect">
        </a>
        Overlay Description
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_712ff8ef-ddd7-4b5f-8077-06f723118baf" shape="rect">
        </a>
        (60xx,0022)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_cfdc82cc-a0e7-459c-80e8-c8319b6e769c" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_2fb590a1-2535-4377-a52c-79c401c1bc7b" shape="rect">
        </a>
        User-defined comments about the overlay.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_d81a9c88-43c3-4672-8f5d-23079e2f4943" shape="rect">
        </a>
        Overlay Subtype
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_36dc31ab-0e5d-49ed-b512-a9dd6fd35805" shape="rect">
        </a>
        (60xx,0045)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_fe70ba38-3423-47ca-9a10-44b2b95c0063" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_00ff9b7f-a198-4298-9ef8-df09fbd64df1" shape="rect">
        </a>
        Defined Term that identifies the intended purpose of the Overlay Type. See
        <a class="xref" href="#sect_C.9.2.1.3" shape="rect" title="C.9.2.1.3 Overlay Subtype">
         Section C.9.2.1.3
        </a>
        for further explanation.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_508d1e38-264b-479a-9fc8-661ef0ce251f" shape="rect">
        </a>
        Overlay Label
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_43fd443a-982a-4870-b129-94cc4371ca93" shape="rect">
        </a>
        (60xx,1500)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_ce322d8e-e9fd-4384-8d8f-cee517ea0714" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_ad545918-254c-4be6-8cb1-68e603ab1ef5" shape="rect">
        </a>
        A user defined text string that may be used to label or name this overlay.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_6219fa2e-1606-4bad-9f9c-702bd585e634" shape="rect">
        </a>
        ROI Area
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_99e79595-050c-44ea-9fb9-3d3af50cf3d7" shape="rect">
        </a>
        (60xx,1301)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_81a6b9e2-c5c7-49d2-96bd-44da5f63c666" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_5e95c0bb-e70f-4e53-aec1-0d7d71a5e156" shape="rect">
        </a>
        Number of pixels in ROI area.
       </p>
       <p>
        <a id="para_9ecf8940-61a6-4c66-8645-b6aaed04bf7e" shape="rect">
        </a>
        See
        <a class="xref" href="#sect_C.*******" shape="rect" title="C.******* ROI Area, ROI Mean and ROI Standard Deviation">
         Section C.*******
        </a>
        for further explanation.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_9017e0cb-38b6-48cd-b89d-2b47711f63c2" shape="rect">
        </a>
        ROI Mean
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_04f0c826-c4ce-46df-8f60-7cc5b76a3b8f" shape="rect">
        </a>
        (60xx,1302)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_5eeeab75-96d2-4ecf-b23c-eba66b8c9873" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_b7e07ae9-5336-4cd3-adad-59a1f6e91836" shape="rect">
        </a>
        ROI Mean.
       </p>
       <p>
        <a id="para_a6e67422-f72d-4abd-8a47-89fdf3730d0b" shape="rect">
        </a>
        See
        <a class="xref" href="#sect_C.*******" shape="rect" title="C.******* ROI Area, ROI Mean and ROI Standard Deviation">
         Section C.*******
        </a>
        for further explanation.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_bda11df8-a020-45e5-8028-250e0e47c6f8" shape="rect">
        </a>
        ROI Standard Deviation
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_664d3772-cfbc-4a3e-8326-5b989fbe1179" shape="rect">
        </a>
        (60xx,1303)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_bb006083-50b4-403c-969b-b8988c28e710" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_701da280-10a7-4a08-94ab-f761a15f2328" shape="rect">
        </a>
        ROI standard deviation.
       </p>
       <p>
        <a id="para_a0b41c69-e72f-433e-84b4-3e3bc4c4da82" shape="rect">
        </a>
        See
        <a class="xref" href="#sect_C.*******" shape="rect" title="C.******* ROI Area, ROI Mean and ROI Standard Deviation">
         Section C.*******
        </a>
        for further explanation.
       </p>
      </td>
     </tr>
    </tbody>
   </table>
  </div>
 </div>
 <br class="table-break" clear="none"/>
 <div class="section">
  <div class="titlepage">
   <div>
    <div>
     <h4 class="title">
      <a id="sect_C.9.2.1" shape="rect">
      </a>
      C.9.2.1 Overlay Plane Module Attribute Descriptions
     </h4>
    </div>
   </div>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h5 class="title">
       <a id="sect_C.*******" shape="rect">
       </a>
       C.******* Overlay Type
      </h5>
     </div>
    </div>
   </div>
   <p>
    <a id="para_13e2b022-0b8b-497d-a3d4-8db9e535c2a6" shape="rect">
    </a>
    There are two specific types of overlays. The type is specified in this Attribute.
   </p>
   <p>
    <a id="para_7ac7088c-b294-43c9-85a1-0dcc4c185358" shape="rect">
    </a>
    A Region of Interest (ROI) is a specific use of an Overlay. The overlay bits corresponding to all the pixels included in the ROI shall be set to 1. All other bits are set to 0. This is used to specify an area of the image of particular interest.
   </p>
   <p>
    <a id="para_001f97a9-0325-400f-a844-2a7ec17d8a04" shape="rect">
    </a>
    A Graphics overlay may express reference marks, graphic annotation, or bit mapped text, etc. A Graphics overlay may be used to mark the boundary of a ROI. If this is the case and the ROI statistical parameters are used, they will only refer to the pixels under the boundaries, not those in the included regions.
   </p>
   <p>
    <a id="para_d7f414cf-0d50-43f0-baed-0ffff581bf9d" shape="rect">
    </a>
    The overlay bits corresponding to all the pixels included in the Graphics shall be set to 1. All other bits are set to 0.
   </p>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h5 class="title">
       <a id="sect_C.*******" shape="rect">
       </a>
       C.******* ROI Area, ROI Mean and ROI Standard Deviation
      </h5>
     </div>
    </div>
   </div>
   <p>
    <a id="para_a6e24dd5-167e-47a9-8cec-a79e190ec702" shape="rect">
    </a>
    These Attributes contain the statistical parameters of the ROI. The values of these parameters are for the overlay pixel values set to 1.
   </p>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h5 class="title">
       <a id="sect_C.9.2.1.3" shape="rect">
       </a>
       C.9.2.1.3 Overlay Subtype
      </h5>
     </div>
    </div>
   </div>
   <div class="variablelist">
    <p class="title">
     <strong>
      Defined Terms:
     </strong>
    </p>
    <dl class="variablelist compact">
     <dt>
      <span class="term">
       USER
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_ed800201-f3a5-4ac6-96ef-9e0d151c2702" shape="rect">
       </a>
       User created graphic annotation (e.g., operator)
      </p>
     </dd>
     <dt>
      <span class="term">
       AUTOMATED
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_60d6c250-f11f-4f85-b4a8-59372a3761b0" shape="rect">
       </a>
       Machine or algorithm generated graphic annotation, such as output of a Computer Assisted Diagnosis algorithm
      </p>
     </dd>
     <dt>
      <span class="term">
       ACTIVE IMAGE AREA
      </span>
     </dt>
     <dd>
      <p>
       <a id="para_a57b87cd-bfbb-4d0a-b13a-2a3f761dafcc" shape="rect">
       </a>
       Identification of the active area of an image
      </p>
     </dd>
    </dl>
   </div>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <p>
     <a id="para_2ef6265d-b820-4790-a357-1c2911f29813" shape="rect">
     </a>
     Additional or alternative Defined Terms may be specified in modality specific Modules, such as the specific use of "ACTIVE 2D/BMODE IMAGE AREA" as described in the
     <a class="xref" href="#sect_C.8.5.6.1.11" shape="rect" title="C.8.5.6.1.11 Overlay Subtype">
      Section C.8.5.6.1.11 Overlay Subtype
     </a>.
    </p>
   </div>
   <p>
    <a id="para_4fef2eb7-4cfc-4221-b34d-2e866739472a" shape="rect">
    </a>
    An active image area overlay identifies all pixels in the Pixel Data that are generated from image data acquisition. Each pixel in the active area shall have an overlay bit value of 1; all other bits are set to 0. Any area of burned in annotation (not generated from image data acquisition) shall be excluded from the active image area.
   </p>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <div class="orderedlist">
     <ol class="orderedlist" type="1">
      <li class="listitem">
       <p>
        <a id="para_9e7da800-e641-4959-a4c8-dbfbaca0f8b8" shape="rect">
        </a>
        For example, the active image area overlay may delineate a non-rectangular (e.g., fan) shaped acquisition by an ultrasound transducer, or a circular reconstructed field of view from a tomographic acquisition.
       </p>
      </li>
      <li class="listitem">
       <p>
        <a id="para_5a5968e1-a18c-4be4-bb91-6a2a4253e9e7" shape="rect">
        </a>
        Whether or not the excluded area of burned in annotation extends beyond the specifically modified pixels, e.g. to exclude an entire bounding box area around the text or graphic annotation, is not specified by the Standard.
       </p>
      </li>
      <li class="listitem">
       <p>
        <a id="para_441e3268-d0ad-4cbb-b4ca-bd50a7756f48" shape="rect">
        </a>
        The active image area for projection X-ray is specified by the
        <a class="xref" href="#sect_C.8.7.3" shape="rect" title="C.8.7.3 X-Ray Collimator Module">
         Section C.8.7.3 X-Ray Collimator Module
        </a>.
       </p>
      </li>
     </ol>
    </div>
   </div>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h5 class="title">
       <a id="sect_C.9.2.1.4" shape="rect">
       </a>
       C.9.2.1.4 Multi-frame Image
      </h5>
     </div>
    </div>
   </div>
   <p>
    <a id="para_c4b68573-8bf3-492f-9a72-d39b2356ed02" shape="rect">
    </a>
    When an overlay is part of a Multi-frame Image and is not a Multi-frame Overlay (Number of Frames in Overlay (60xx,0015) and Image Frame Origin (60xx,0051) are absent), the overlay shall be applied to all Frames in the image.
   </p>
  </div>
 </div>
</div>
