#### C.7.6.5 Cine Module
Table C.7-13 specifies the Attributes of the Cine Module, which describe a Multi-frame Cine Image.
**Table C.7-13. Cine Module Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Preferred Playback Sequencing | (0018,1244) | 3 | Describes the preferred playback sequencing for a Multi-frame Image. **Enumerated Values:** 0 Looping (1,2n,1,2,n,1,2,.n,) 1 Sweeping (1,2,n,n-1,2,1,2,n,) |
| Frame Time | (0018,1063) | 1C | Nominal time (in msec) per individual Frame. See Section C.*******.1 for further explanation. Required if Frame Increment Pointer (0028,0009) points to Frame Time. |
| Frame Time Vector | (0018,1065) | 1C | An array that contains the real time increments (in msec) between Frames for a Multi-frame Image. See Section C.*******.2 for further explanation. Required if Frame Increment Pointer (0028,0009) points to Frame Time Vector. ### Note If the Value Length of this Data Element exceeds 65534 bytes and an Explicit VR Transfer Syntax is used, then the Value Representation UN can be used for this Data Element. See [PS3.5 Section 6.2.2](part05.html#sect_6.2.2). |
| Start Trim | (0008,2142) | 3 | The Frame number of the first Frame of the Multi-frame Image to be displayed. |
| Stop Trim | (0008,2143) | 3 | The Frame Number of the last Frame of a Multi-frame Image to be displayed. |
| Recommended Display Frame Rate | (0008,2144) | 3 | Recommended rate at which the Frames of a Multi-frame Image should be displayed in Frames/second. |
| Cine Rate | (0018,0040) | 3 | Number of Frames per second. |
| Frame Delay | (0018,1066) | 3 | Time (in msec) from Content Time (0008,0033) to the start of the first Frame in a Multi-frame Image. |
| Image Trigger Delay | (0018,1067) | 3 | Delay time in milliseconds from trigger (e.g., X-Ray on pulse) to the first Frame of a Multi-frame Image. |
| Effective Duration | (0018,0072) | 3 | Total time in seconds that data was actually taken for the entire Multi-frame Image. |
| Actual Frame Duration | (0018,1242) | 3 | Elapsed time of data acquisition in msec per each Frame. |
| Multiplexed Audio Channels Description Code Sequence | (003A,0300) | 2C | Description of any multiplexed audio channels. See Section C.*******.3. Zero or more Items may be included in this Sequence. Required if the Transfer Syntax used to encode the Multi-frame Image contains multiplexed (interleaved) audio channels, such as is possible with MPEG2 Systems (see [ ISO/IEC 13818-1 ] ). |
| &gt;Channel Identification Code | (003A,0301) | 1 | A reference to the audio channel as identified within Transfer Syntax encoded bit stream (1 for the main channel, 2 for the second channel and 3 to 9 to the complementary channels). |
| &gt;Channel Mode | (003A,0302) | 1 | A coded descriptor qualifying the mode of the channel: **Enumerated Values:** MONO 1 signal STEREO 2 simultaneously acquired (left and right) signals |
| &gt;Channel Source Sequence | (003A,0208) | 1 | A coded descriptor of the audio channel source. Only a single Item shall be included in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |   |   | D [CID 3000 Audio Channel Source](part16.html#sect_CID_3000). |
##### C.******* Cine Module Attribute Descriptions
###### C.*******.1 Frame Time
Frame Time (0018,1063) is the nominal time (in milliseconds) between individual Frames of a Multi-frame Image. If the Frame Increment Pointer points to this Attribute, Frame Time shall be used in the following manner to calculate 'the relative time' for each Frame:
Frame 'Relative Time' (n) = Frame Delay + Frame Time * (n-1)
where: n = number of Frame within the Multi-frame Image and the first Frame number is one
### Note
When there is only one Frame present, Frame Time (0018,1063) may have either a Value of 0, or a nominal value that would apply if there were multiple Frames.
###### C.*******.2 Frame Time Vector
Frame Time Vector (0018,1065) is an array that contains the time increments (in milliseconds) between the nth Frame and the previous Frame for a Multi-frame Image. The first Frame always has a time increment of 0. If the Frame Increment Pointer points to this Attribute, the Frame Time Vector shall be used in the following manner to calculate 'relative time'  T(n)  for Frame  n  :
where t  i  is the i  th  Frame Time Vector component.
###### C.*******.3 Multiplexed Audio
During a video acquisition, audio may be used for voice commentary of what is being observed, as well as to record sound-based physiological information such as Doppler audio.
Some Transfer Syntaxes allow for the multiplexing of interleaved audio with video data, and the Attributes of the Cine Module support this encoding paradigm. They are not intended to describe audio acquired simultaneously when it is encoded in other SOP Instances or within Attributes other than Pixel Data (7FE0,0010) of the same SOP Instance.
Synchronization between audio and video is assumed to be encoded at the Transfer Syntax level (i.e., within the encoded bit stream).
### Note
If no audio was recorded, the Multiplexed Audio Channels Description Code Sequence (003A,0300) will be present and contain no Items.