#### C.8.8.14 RT Beams Module
The RT Beams Module contains information defining equipment parameters for delivery of external radiation beams.
**Table C.8-50. RT Beams Module Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Beam Sequence | (300A,00B0) | 1 | Sequence of treatment beams for current RT Plan. One or more Items shall be included in this Sequence. |
| &gt;Beam Number | (300A,00C0) | 1 | Identification number of the Beam. The Value of Beam Number (300A,00C0) shall be unique within the RT Plan in which it is created. See Note 1. |
| &gt;Beam Name | (300A,00C2) | 3 | User-defined name for Beam. See Note 1. |
| &gt;Entity Long Label | (3010,0038) | 3 | User-defined label for this Beam. See Note 1. |
| &gt;Beam Description | (300A,00C3) | 3 | User-defined description for Beam. See Note 1. |
| &gt;Definition Source Sequence | (0008,1156) | 3 | Instances containing the source of the Beam information. Only a single Item is permitted in this Sequence. See Section C.********9 and Section C.********. |
| &gt;&gt;Include Table 10-11 SOP Instance Reference Macro Attributes. |   |   |   |
| &gt;Beam Type | (300A,00C4) | 1 | Motion characteristic of Beam. See Note 5. **Enumerated Values:** STATIC All Control Point Sequence (300A,0111) remain unchanged between consecutive pairs of control points with changing Cumulative Meterset Weight (300A,0134). DYNAMIC One or more Control Point Sequence (300A,0111) change between one or more consecutive pairs of control points with changing Cumulative Meterset Weight (300A,0134). |
| &gt;Radiation Type | (300A,00C6) | 2 | Particle type of Beam. **Defined Terms:** PHOTON ELECTRON NEUTRON PROTON |
| &gt;Primary Fluence Mode Sequence | (3002,0050) | 3 | Sequence defining whether the primary fluence of the treatment beam uses a non-standard fluence-shaping. Only a single Item is permitted in this Sequence. |
| &gt;&gt;Fluence Mode | (3002,0051) | 1 | Describes whether the fluence shaping is the standard mode for the beam or an alternate. **Enumerated Values:** STANDARD Uses standard fluence-shaping NON_STANDARD Uses a non-standard fluence-shaping mode |
| &gt;&gt;Fluence Mode ID | (3002,0052) | 1C | Identifier for the specific fluence-shaping mode. Required if Fluence Mode (3002,0051) has the Value NON_STANDARD. |
| &gt;High-Dose Technique Type | (300A,00C7) | 1C | Type of high-dose treatment technique. **Defined Terms:** TBI Total Body Irradiation HDR High Dose Rate Required if treatment technique requires a dose that would normally require overriding of treatment machine safety controls. |
| &gt;RT Treatment Technique Code Sequence | (3010,0080) | 3 | Type of treatment technique. Only a single Item shall be included in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes. |   |   | B [CID 9583 RT Plan Radiotherapy Procedure Technique](part16.html#sect_CID_9583). |
| &gt;Treatment Machine Name | (300A,00B2) | 2 | User-defined name identifying treatment machine to be used for beam delivery. See Note 2. |
| &gt;Manufacturer | (0008,0070) | 3 | Manufacturer of the equipment to be used for beam delivery. |
| &gt;Institution Name | (0008,0080) | 3 | Institution where the equipment is located that is to be used for beam delivery. |
| &gt;Institution Address | (0008,0081) | 3 | Mailing address of the institution where the equipment is located that is to be used for beam delivery. |
| &gt;Institutional Department Name | (0008,1040) | 3 | Department in the institution where the equipment is located that is to be used for beam delivery. |
| &gt;Institutional Department Type Code Sequence | (0008,1041) | 3 | A coded description of the type of Department or Service within the healthcare facility. Only a single Item is permitted in this Sequence. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |   |   | B [CID 7030 Institutional Department/Unit/Service](part16.html#sect_CID_7030). |
| &gt;Manufacturer's Model Name | (0008,1090) | 3 | Manufacturer's model name of the equipment that is to be used for beam delivery. |
| &gt;Device Serial Number | (0018,1000) | 3 | Manufacturer's serial number of the equipment that is to be used for beam delivery. |
| &gt;Date of Manufacture | (0018,1204) | 3 | The date the equipment that is to be used for beam delivery was originally manufactured or re-manufactured (as opposed to refurbished). |
| &gt;Date of Installation | (0018,1205) | 3 | The date the equipment that is to be used for beam delivery was installed in its current location. The equipment may or may not have been used prior to installation in its current location. |
| &gt;Primary Dosimeter Unit | (300A,00B3) | 3 | Measurement unit of machine dosimeter. See Section C.********. **Enumerated Values:** MU Monitor Unit MINUTE minute |
| &gt;Table Top Position Alignment UID | (300A,0054) | 3 | Identifies the positional alignment of the Table Top for which the Values of Table Top Vertical Position (300A,0128), Table Top Longitudinal Position (300A,0129) and Table Top Lateral Position (300A,012A) in the Control Point Sequence (300A,0111) are applicable. See Section C.*********. |
| &gt;Referenced Tolerance Table Number | (300C,00A0) | 3 | Uniquely identifies Tolerance Table specified by Tolerance Table Number (300A,0042) within Tolerance Table Sequence in RT Tolerance Tables Module. These tolerances are to be used for verification of treatment machine settings. |
| &gt;Source-Axis Distance | (300A,00B4) | 3 | Radiation source to Gantry rotation axis distance of the equipment that is to be used for beam delivery (mm). |
| &gt;Enhanced RT Beam Limiting Device Definition Flag | (3008,00A3) | 3 | Whether the RT Beam Limiting Devices are specified by the Enhanced RT Beam Limiting Device Sequence (3008,00A1). **Enumerated Values:** YES NO |
| &gt;Beam Limiting Device Sequence | (300A,00B6) | 1C | Sequence of beam limiting device (collimator) jaw or leaf (element) sets. Required if Enhanced RT Beam Limiting Device Definition Flag (3008,00A3) is absent, or is present and has the Value NO. One or more Items shall be included in this Sequence. |
| &gt;&gt;RT Beam Limiting Device Type | (300A,00B8) | 1 | Type of beam limiting device (collimator). **Enumerated Values:** X symmetric jaw pair in IEC X direction Y symmetric jaw pair in IEC Y direction ASYMX asymmetric jaw pair in IEC X direction ASYMY asymmetric jaw pair in IEC Y direction MLCX single layer multileaf collimator in IEC X direction MLCY single layer multileaf collimator in IEC Y direction |
| &gt;&gt;Source to Beam Limiting Device Distance | (300A,00BA) | 3 | Radiation source to beam limiting device (collimator) distance of the equipment that is to be used for beam delivery (mm). |
| &gt;&gt;Number of Leaf/Jaw Pairs | (300A,00BC) | 1 | Number of leaf (element) or jaw pairs (equal to 1 for standard beam limiting device jaws). |
| &gt;&gt;Leaf Position Boundaries | (300A,00BE) | 2C | Boundaries of beam limiting device (collimator) leaves (in mm) in IEC BEAM LIMITING DEVICE coordinate axis appropriate to RT Beam Limiting Device Type (300A,00B8), i.e., X-axis for MLCY, Y-axis for MLCX. Contains N+1 Values, where N is the Number of Leaf/Jaw Pairs (300A,00BC), starting from Leaf (Element) Pair 1. Required if RT Beam Limiting Device Type (300A,00B8) is MLCX or MLCY. May be present otherwise. See Note 3. |
| &gt;Enhanced RT Beam Limiting Device Sequence | (3008,00A1) | 1C | Enhanced RT Beam Limiting Device Descriptions. Required if Enhanced RT Beam Limiting Device Definition Flag (3008,00A3) is present and has the Value YES. One or more Items shall be included in this Sequence. |
| &gt;&gt;Include Table C.*********-1 RT Beam Limiting Device Definition Macro Attributes. |   |   | Device Type Code Sequence (3010,002E) within RT Accessory Device Identification Macro D [CID 9540 Movable Beam Limiting Device Type](part16.html#sect_CID_9540). See Section C.********7. |
| &gt;Referenced Patient Setup Number | (300C,006A) | 3 | Uniquely identifies Patient Setup to be used for current beam, specified by Patient Setup Number (300A,0182) within Patient Setup Sequence of RT Patient Setup Module. |
| &gt;Referenced Reference Image Sequence | (300C,0042) | 3 | Reference images used for validation of current beam. One or more Items are permitted in this Sequence. |
| &gt;&gt;Include Table 10-11 SOP Instance Reference Macro Attributes |   |   |   |
| &gt;&gt;Reference Image Number | (300A,00C8) | 1 | Uniquely identifies Reference Image within Referenced Reference Image Sequence (300C,0042). |
| &gt;&gt;Start Cumulative Meterset Weight | (300C,0008) | 3 | Cumulative Meterset Weight within current Beam at which image acquisition starts. |
| &gt;&gt;End Cumulative Meterset Weight | (300C,0009) | 3 | Cumulative Meterset Weight within current Beam at which image acquisition ends. |
| &gt;Planned Verification Image Sequence | (300A,00CA) | 3 | Sequence of planned verification images to be acquired during current beam. One or more Items are permitted in this Sequence. See Section C.********. |
| &gt;&gt;Start Cumulative Meterset Weight | (300C,0008) | 3 | Cumulative Meterset Weight within current Beam at which image acquisition will start. |
| &gt;&gt;Meterset Exposure | (3002,0032) | 3 | Meterset duration over which image is to be acquired, specified in Monitor units (MU) or minutes as defined by Primary Dosimeter Unit (300A,00B3). |
| &gt;&gt;End Cumulative Meterset Weight | (300C,0009) | 3 | Cumulative Meterset Weight within current Beam at which image acquisition will end. |
| &gt;&gt;RT Image Plane | (3002,000C) | 3 | Describes whether or not image plane is normal to beam axis. **Enumerated Values:** NORMAL image plane normal to beam axis NON_NORMAL image plane non-normal to beam axis |
| &gt;&gt;X-Ray Image Receptor Angle | (3002,000E) | 3 | X-Ray Image Receptor Angle i.e., orientation of IEC X-RAY IMAGE RECEPTOR coordinate system with respect to IEC GANTRY coordinate system (degrees). See Section C.********. |
| &gt;&gt;RT Image Orientation | (3002,0010) | 3 | The direction cosines of the first row and the first column with respect to the IEC X-RAY IMAGE RECEPTOR coordinate system. |
| &gt;&gt;RT Image Position | (3002,0012) | 3 | The x and y coordinates (in mm) of the upper left hand corner of the image, in the IEC X-RAY IMAGE RECEPTOR coordinate system. This is the center of the first pixel transmitted. |
| &gt;&gt;RT Image SID | (3002,0026) | 3 | Radiation machine source to image plane distance (mm). |
| &gt;&gt;Imaging Device-Specific Acquisition Parameters | (300A,00CC) | 3 | User-specified device-specific parameters that describe how the imager will acquire the image. |
| &gt;&gt;Referenced Reference Image Number | (300C,0007) | 3 | Uniquely identifies Reference Image to which planned verification image is related, specified by Reference Image Number (300A,00C8) within Referenced Reference Image Sequence (300C,0042). |
| &gt;Treatment Delivery Type | (300A,00CE) | 3 | Delivery Type of treatment. **Defined Terms:** TREATMENT normal patient treatment OPEN_PORTFILM portal image acquisition with open field TRMT_PORTFILM portal image acquisition with treatment port CONTINUATION continuation of interrupted treatment SETUP no treatment beam is applied for this RT Beam. To be used for specifying the gantry, couch, and other machine positions where X-Ray set-up images or measurements are to be taken |
| &gt;Referenced Dose Sequence | (300C,0080) | 3 | Related Instances of RT Dose (for grids, isodose curves, and named/unnamed point doses). One or more Items are permitted in this Sequence. |
| &gt;&gt;Include Table 10-11 SOP Instance Reference Macro Attributes |   |   |   |
| &gt;Number of Wedges | (300A,00D0) | 1 | Number of wedges associated with current Beam. |
| &gt;Wedge Sequence | (300A,00D1) | 1C | Sequence of treatment wedges. Required if Number of Wedges (300A,00D0) is non-zero. One or more Items shall be included in this Sequence. |
| &gt;&gt;Wedge Number | (300A,00D2) | 1 | Identification number of the Wedge. The Value of Wedge Number (300A,00D2) shall be unique within the Beam in which it is created. |
| &gt;&gt;Wedge Type | (300A,00D3) | 2 | Type of wedge (if any) defined for Beam. **Defined Terms:** STANDARD standard (static) wedge DYNAMIC moving beam limiting device (collimator) jaw simulating wedge MOTORIZED single wedge that can be removed from beam remotely |
| &gt;&gt;Wedge ID | (300A,00D4) | 3 | User-supplied identifier for Wedge. |
| &gt;&gt;Accessory Code | (300A,00F9) | 3 | An identifier for the accessory intended to be read by a device such as a bar-code reader. |
| &gt;&gt;Wedge Angle | (300A,00D5) | 2 | Nominal wedge angle (degrees). |
| &gt;&gt;Wedge Factor | (300A,00D6) | 2 | Nominal wedge factor under machine calibration conditions at the beam energy specified by the Nominal Beam Energy (300A,0114) of the first Control Point of the Control Point Sequence (300A,0111). |
| &gt;&gt;Wedge Orientation | (300A,00D8) | 2 | Orientation of wedge, i.e., orientation of IEC WEDGE FILTER coordinate system with respect to IEC BEAM LIMITING DEVICE coordinate system (degrees). |
| &gt;&gt;Source to Wedge Tray Distance | (300A,00DA) | 3 | Radiation source to wedge tray attachment edge distance (in mm) for current wedge. |
| &gt;&gt;Effective Wedge Angle | (300A,00DE) | 3 | Effective wedge angle (degrees). See Section C.********4. |
| &gt;Number of Compensators | (300A,00E0) | 1 | Number of compensators associated with current Beam. |
| &gt;Total Compensator Tray Factor | (300A,00E2) | 3 | Compensator Tray transmission factor (between 0 and 1), at the beam energy specified by the Nominal Beam Energy (300A,0114) of the first Control Point of the Control Point Sequence (300A,0111). |
| &gt;Compensator Sequence | (300A,00E3) | 1C | Sequence of treatment compensators. One or more Items shall be included in this Sequence. Required if Number of Compensators (300A,00E0) is non-zero. |
| &gt;&gt;Compensator Description | (300A,02EB) | 3 | User defined description for the compensator. |
| &gt;&gt;Compensator Number | (300A,00E4) | 1C | Identification number of the Compensator. The Value of Compensator Number (300A,00E4) shall be unique within the Beam in which it is created. Required if Number of Compensators (300A,00E0) is non-zero. |
| &gt;&gt;Compensator Type | (300A,00EE) | 3 | Type of compensator (if any). **Defined Terms:** STANDARD physical (static) compensator DYNAMIC moving Beam Limiting Device (collimator) simulating physical compensator |
| &gt;&gt;Material ID | (300A,00E1) | 2C | User-supplied identifier for material used to manufacture Compensator. Required if Number of Compensators (300A,00E0) is non-zero. |
| &gt;&gt;Compensator ID | (300A,00E5) | 3 | User-supplied identifier for compensator. |
| &gt;&gt;Accessory Code | (300A,00F9) | 3 | An identifier for the Compensator intended to be read by a device such as a bar-code reader. |
| &gt;&gt;Compensator Tray ID | (300A,00EF) | 3 | User-supplied identifier for compensator tray. |
| &gt;&gt;Tray Accessory Code | (300A,0355) | 3 | An identifier for the Tray intended to be read by a device such as a bar-code reader. |
| &gt;&gt;Source to Compensator Tray Distance | (300A,00E6) | 2 | Radiation source to compensator tray attachment edge distance (in mm) for current compensator. |
| &gt;&gt;Compensator Divergence | (300A,02E0) | 3 | Indicates presence or absence of geometrical divergence of the compensator. **Enumerated Values:** PRESENT the compensator is shaped according to the beam geometrical divergence. ABSENT the compensator is not shaped according to the beam geometrical divergence. |
| &gt;&gt;Compensator Mounting Position | (300A,02E1) | 3 | Indicates on which side of the Compensator Tray the compensator is mounted. **Enumerated Values:** PATIENT_SIDE the compensator is mounted on the side of the Compensator Tray that is towards the patient. SOURCE_SIDE the compensator is mounted on the side of the Compensator Tray that is towards the radiation source. DOUBLE_SIDED the compensator has a shaped (i.e., non-flat) surface on both sides of the Compensator Tray. |
| &gt;&gt;Compensator Rows | (300A,00E7) | 1 | Number of rows in the compensator. A row is defined to be in the X direction of the IEC Beam Limiting Device Coordinate system. |
| &gt;&gt;Compensator Columns | (300A,00E8) | 1 | Number of columns in the compensator. A column is defined to be in the Y direction of the IEC Beam Limiting Device Coordinate system. |
| &gt;&gt;Compensator Pixel Spacing | (300A,00E9) | 1 | Physical distance (in mm) between the center of each pixel projected onto machine isocentric plane. Specified by a numeric pair - adjacent row spacing (delimiter) adjacent column spacing. See Section 10.7.1.3 for further explanation of the order of the Values. |
| &gt;&gt;Compensator Position | (300A,00EA) | 1 | The x and y coordinates of the upper left hand corner (first pixel transmitted) of the compensator, projected onto the machine isocentric plane in the IEC BEAM LIMITING DEVICE coordinate system (mm). |
| &gt;&gt;Compensator Transmission Data | (300A,00EB) | 1C | A data stream of the pixel samples that comprise the compensator, expressed as broad-beam transmission values (between 0 and 1) along a ray line passing through the pixel, at the beam energy specified by the Nominal Beam Energy (300A,0114) of the first Control Point of the Control Point Sequence (300A,0111). The order of pixels encoded is left to right, top to bottom, i.e., the upper left pixel is encoded first followed by the remainder of the first row, followed by the first pixel of the 2 nd row, then the remainder of the 2 nd row and so on) when viewed from the radiation source. Required if Material ID (300A,00E1) is zero-length. May be present if Material ID (300A,00E1) is non-zero length. See Section C.********0 and Section C.********1. ### Note If the Value Length of this Data Element exceeds 65534 bytes and an Explicit VR Transfer Syntax is used, then the Value Representation UN can be used for this Data Element. See [PS3.5 Section 6.2.2](part05.html#sect_6.2.2). |
| &gt;&gt;Compensator Thickness Data | (300A,00EC) | 1C | A data stream of the pixel samples that comprise the compensator, expressed as thicknesses (in mm). The order of pixels encoded is left to right, top to bottom, i.e., the upper left pixel is encoded first followed by the remainder of the first row, followed by the first pixel of the 2 nd row, then the remainder of the 2 nd row and so on) when viewed from the radiation source. Required if Material ID (300A,00E1) is non-zero length. May be present if Material ID (300A,00E1) is zero length. See Section C.8.8.14.9 and Section C.********0 and Section C.********1, Block and Compensator Precedence for Dosimetric Calculations. ### Note If the Value Length of this Data Element exceeds 65534 bytes and an Explicit VR Transfer Syntax is used, then the Value Representation UN can be used for this Data Element. See [PS3.5 Section 6.2.2](part05.html#sect_6.2.2). |
| &gt;&gt;Source to Compensator Distance | (300A,02E2) | 1C | A data stream of the pixel samples that comprise the distance from the radiation source to the compensator surface closest to the radiation source (in mm). The order of pixels encoded is left to right, top to bottom (upper left pixel, followed by the remainder of row 1, followed by the remainder of the columns). Required if Material ID (300A,00E1) is non-zero length, and Compensator Mounting Position (300A,02E1) is DOUBLE_SIDED. May be present if Material ID (300A,00E1) is zero length and Compensator Mounting Position (300A,02E1) is DOUBLE_SIDED. See Section C.8.8.14.9 and Section C.********1. |
| &gt;Number of Boli | (300A,00ED) | 1 | Number of boli associated with current Beam. |
| &gt;Referenced Bolus Sequence | (300C,00B0) | 1C | Sequence of boli associated with Beam. Required if Number of Boli (300A,00ED) is non-zero. One or more Items shall be included in this Sequence. |
| &gt;&gt;Referenced ROI Number | (3006,0084) | 1 | Uniquely identifies ROI representing the Bolus specified by ROI Number (3006,0022) in Structure Set ROI Sequence (3006,0020) in Structure Set Module within RT Structure Set in Referenced Structure Set Sequence (300C,0060) in RT General Plan Module. |
| &gt;&gt;Bolus ID | (300A,00DC) | 3 | User-supplied identifier for the Bolus. |
| &gt;&gt;Bolus Description | (300A,00DD) | 3 | User-defined description for the Bolus. |
| &gt;&gt;Accessory Code | (300A,00F9) | 3 | An identifier for the accessory intended to be read by a device such as a bar-code reader. |
| &gt;Number of Blocks | (300A,00F0) | 1 | Number of shielding blocks associated with Beam. |
| &gt;Total Block Tray Factor | (300A,00F2) | 3 | Total block tray transmission for all block trays (between 0 and 1) at the beam energy specified by the Nominal Beam Energy (300A,0114) of the first Control Point of the Control Point Sequence (300A,0111). |
| &gt;Block Sequence | (300A,00F4) | 1C | Sequence of blocks associated with Beam. One or more Items shall be included in this Sequence. Required if Number of Blocks (300A,00F0) is non-zero. |
| &gt;&gt;Block Tray ID | (300A,00F5) | 3 | User-supplied identifier for block tray. |
| &gt;&gt;Tray Accessory Code | (300A,0355) | 3 | An identifier for the Tray intended to be read by a device such as a bar-code reader. |
| &gt;&gt;Accessory Code | (300A,00F9) | 3 | An identifier for the Block intended to be read by a device such as a bar-code reader. |
| &gt;&gt;Source to Block Tray Distance | (300A,00F6) | 2 | Radiation Source to attachment edge of block tray assembly (mm). |
| &gt;&gt;Block Type | (300A,00F8) | 1 | Type of block. **Enumerated Values:** SHIELDING blocking material is inside contour APERTURE blocking material is outside contour |
| &gt;&gt;Block Divergence | (300A,00FA) | 2 | Indicates presence or otherwise of geometrical divergence. **Enumerated Values:** PRESENT block edges are shaped for beam divergence ABSENT block edges are not shaped for beam divergence |
| &gt;&gt;Block Mounting Position | (300A,00FB) | 3 | Indicates on which side of the Block Tray the block is mounted. **Enumerated Values:** PATIENT_SIDE the block is mounted on the side of the Block Tray that is towards the patient. SOURCE_SIDE the block is mounted on the side of the Block Tray that is towards the radiation source. |
| &gt;&gt;Block Number | (300A,00FC) | 1 | Identification number of the Block. The Value of Block Number (300A,00FC) shall be unique within the Beam in which it is created. |
| &gt;&gt;Block Name | (300A,00FE) | 3 | User-defined name for block. |
| &gt;&gt;Material ID | (300A,00E1) | 2 | User-supplied identifier for material used to manufacture Block. |
| &gt;&gt;Block Thickness | (300A,0100) | 2C | Physical thickness of block (in mm) parallel to radiation beam axis. Required if Material ID (300A,00E1) is non-zero length. May be present if Material ID (300A,00E1) is zero length. See Section C.******** and Section C.********1. |
| &gt;&gt;Block Transmission | (300A,0102) | 2C | Transmission through the block (between 0 and 1) at the beam energy specified by the Nominal Beam Energy (300A,0114) of the first Control Point of the Control Point Sequence (300A,0111). Required if Material ID (300A,00E1) is zero length. May be present if Material ID (300A,00E1) is non-zero length. See Section C.******** and Section C.********1. |
| &gt;&gt;Block Number of Points | (300A,0104) | 2 | Number of (x,y) pairs defining the block edge. |
| &gt;&gt;Block Data | (300A,0106) | 2 | A data stream of (x,y) pairs that comprise the block edge. The number of pairs shall be equal to Block Number of Points (300A,0104), and the vertices shall be interpreted as a closed polygon. Coordinates are projected onto the machine isocentric plane in the IEC BEAM LIMITING DEVICE coordinate system (mm). See Note 4. |
| &gt;Applicator Sequence | (300A,0107) | 3 | Sequence of Applicators associated with Beam. Only a single Item is permitted in this Sequence. |
| &gt;&gt;Applicator ID | (300A,0108) | 1 | User or machine supplied identifier for Applicator. |
| &gt;&gt;Accessory Code | (300A,00F9) | 3 | An identifier for the accessory intended to be read by a device such as a bar-code reader. |
| &gt;&gt;Applicator Type | (300A,0109) | 1 | Type of Applicator. **Defined Terms:** ELECTRON_SQUARE square electron applicator ELECTRON_RECT rectangular electron applicator ELECTRON_CIRC circular electron applicator ELECTRON_SHORT short electron applicator ELECTRON_OPEN open (dummy) electron applicator PHOTON_SQUARE square photon applicator PHOTON_RECT rectangular photon applicator PHOTON_CIRC circular photon applicator INTRAOPERATIVE intraoperative (custom) applicator STEREOTACTIC stereotactic applicator (deprecated) |
| &gt;&gt;Applicator Geometry Sequence | (300A,0431) | 3 | Describes the applicator aperture geometry. Only a single Item is permitted in this Sequence. |
| &gt;&gt;&gt;Applicator Aperture Shape | (300A,0432) | 1 | Aperture shape of the applicator. **Defined Terms:** SYM_SQUARE A square-shaped aperture symmetrical to the central axis. SYM_RECTANGLE A rectangular-shaped aperture symmetrical to the central axis. SYM_CIRCULAR A circular-shaped aperture symmetrical to the central axis. |
| &gt;&gt;&gt;Applicator Opening | (300A,0433) | 1C | Opening (in mm) of the applicator's aperture in IEC BEAM LIMITING DEVICE coordinate system. In case of square-shaped applicator contains the length of the sides of the square. In case of circular-shaped applicators, contains the diameter of the circular aperture. Required if Applicator Aperture Shape (300A,0432) is SYM_SQUARE or SYM_CIRCULAR. |
| &gt;&gt;&gt;Applicator Opening X | (300A,0434) | 1C | Opening (in mm) of the applicator's aperture in IEC BEAM LIMITING DEVICE coordinate system in X-Direction. Required if Applicator Aperture Shape (300A,0432) is SYM_RECTANGLE. |
| &gt;&gt;&gt;Applicator Opening Y | (300A,0435) | 1C | Opening (in mm) of the applicator's aperture in IEC BEAM LIMITING DEVICE coordinate system in Y-Direction. Required if Applicator Aperture Shape (300A,0432) is SYM_RECTANGLE. |
| &gt;&gt; Source to Applicator Mounting Position Distance | (300A,0436) | 3 | Radiation source to applicator mounting position distance (in mm) for current applicator. |
| &gt;&gt;Applicator Description | (300A,010A) | 3 | User-defined description for Applicator. |
| &gt;General Accessory Sequence | (300A,0420) | 3 | A Sequence of General Accessories associated with this Beam. One or more Items are permitted in this Sequence. |
| &gt;&gt;General Accessory Number | (300A,0424) | 1 | Identification Number of the General Accessory. The Value shall be unique within the Sequence. |
| &gt;&gt;General Accessory ID | (300A,0421) | 1 | User or machine supplied identifier for General Accessory. |
| &gt;&gt;General Accessory Description | (300A,0422) | 3 | User supplied description of General Accessory. |
| &gt;&gt;General Accessory Type | (300A,0423) | 3 | Specifies the type of accessory. **Defined Terms:** GRATICULE Accessory tray with a radio-opaque grid IMAGE_DETECTOR Image acquisition device positioned in the beam line RETICLE Accessory tray with radio-transparent markers or grid |
| &gt;&gt;Accessory Code | (300A,00F9) | 3 | Machine-readable identifier for this accessory. |
| &gt;&gt;Source to General Accessory Distance | (300A,0425) | 3 | Radiation source to general accessory distance (in mm) for current accessory. |
| &gt;Referenced Dose Reference Sequence | (300C,0050) | 3 | A Sequence of Dose References for which verification control points are defined. One or more Items are permitted in this Sequence. |
| &gt;&gt;Referenced Dose Reference Number | (300C,0051) | 1 | Uniquely identifies Dose Reference specified by Dose Reference Number (300A,0012) in Dose Reference Sequence (300A,0010) in RT Prescription Module. |
| &gt;&gt;Depth Value Averaging Flag | (300A,0093) | 1C | Whether or not the depth values have been averaged. **Enumerated Values:** YES The values represent average values from the current Verification Control Point to the next NO The values refer to a single location Required if the referenced beam describes an angular movement and the depth values change during movement. |
| &gt;&gt;Beam Dose Verification Control Point Sequence | (300A,008C) | 1 | Sequence of Items containing Beam Dose Coordinate Verification Control Points. Two or more Items shall be included in this Sequence. |
| &gt;&gt;&gt;Cumulative Meterset Weight | (300A,0134) | 1 | The cumulative Meterset weight value, at which the beam dose point geometrical parameters apply. |
| &gt;&gt;&gt;Referenced Control Point Index | (300C,00F0) | 1C | Uniquely identifies the Control Point specified by Control Point Index (300A,0112) within Beam referenced by Referenced Beam Number (300C,0006). See Section C.********6. Required, if the Referenced Cumulative Meterset corresponds to a Control Point in the Control Point Sequence (300A,0111). |
| &gt;&gt;&gt;Beam Dose Point Depth | (300A,0088) | 1C | The depth (in mm) in the patient along a ray from the source to the dose point specified by the Dose Reference Point Coordinates (300A,0018) or the referenced ROI. Required for all but the last Item in this Sequence and for the last Item if Depth Value Averaging Flag (300A,0093) has a Value of NO. See Note 6. |
| &gt;&gt;&gt;Beam Dose Point Equivalent Depth | (300A,0089) | 1C | The radiological depth in mm (water-equivalent depth, taking tissue heterogeneity into account) in the patient along a ray from the source to the dose point specified by the Dose Reference Point Coordinates (300A,0018) or the referenced ROI. Required for all but the last Item in this Sequence and for the last Item if Depth Value Averaging Flag (300A,0093) has a Value of NO. See Note 6. |
| &gt;&gt;&gt;Beam Dose Point SSD | (300A,008A) | 1C | Source to patient surface (skin) distance in mm along a ray from the source to the dose point specified by the Dose Reference Point Coordinates (300A,0018) or the referenced ROI. Required for all but the last Item in this Sequence and for the last Item if Depth Value Averaging Flag (300A,0093) has a Value of NO. See Note 6. |
| &gt;&gt;&gt;Beam Dose Point Source to External Contour Distance | (300A,0094) | 3 | Source to External Contour distance in mm including devices associated with the patient anatomy model along a ray from the source to the dose point specified by the Dose Reference Point Coordinates (300A,0018) or the referenced ROI. May be present for all but the last Item in this Sequence and for the last Item if Depth Value Averaging Flag (300A,0093) has a Value of NO. See Section C.********5. |
| &gt;Final Cumulative Meterset Weight | (300A,010E) | 1C | Value of Cumulative Meterset Weight (300A,0134) for final Control Point in Control Point Sequence (300A,0111). Required if Cumulative Meterset Weight is non-null in Control Points specified within Control Point Sequence (300A,0111). See Section C.********. |
| &gt;Number of Control Points | (300A,0110) | 1 | Number of control points in Beam. Value shall be greater than or equal to 2. |
| &gt;Control Point Sequence | (300A,0111) | 1 | Sequence of machine configurations describing treatment beam. The number of Items in this Sequence shall equal the Value of Number of Control Points (300A,0110). See Section C.******** and Section C.********. |
| &gt;&gt;Control Point Index | (300A,0112) | 1 | Index of current Control Point, starting at 0 for first Control Point. |
| &gt;&gt;Cumulative Meterset Weight | (300A,0134) | 2 | Cumulative weight to current control point. Cumulative Meterset Weight for the first Item in Control Point Sequence (300A,0111) shall always be zero. Cumulative Meterset Weight for the final Item in Control Point Sequence (300A,0111) shall always be equal to Final Cumulative Meterset Weight. See Section C.********. |
| &gt;&gt;Referenced Dose Reference Sequence | (300C,0050) | 3 | A Sequence of Dose References for current Beam. One or more Items are permitted in this Sequence. |
| &gt;&gt;&gt;Referenced Dose Reference Number | (300C,0051) | 1 | Uniquely identifies Dose Reference specified by Dose Reference Number (300A,0012) in Dose Reference Sequence (300A,0010) in RT Prescription Module. |
| &gt;&gt;&gt;Cumulative Dose Reference Coefficient | (300A,010C) | 2 | Coefficient used to calculate cumulative dose contribution from this Beam to the referenced Dose Reference at the current Control Point. See Section C.********. |
| &gt;&gt;Referenced Dose Sequence | (300C,0080) | 1C | Sequence describing related Instances of RT Dose (for grids, isodose curves, and named/unnamed point doses). One or more Items shall be included in this Sequence. Required if RT Dose is being transmitted, and Dose Summation Type (3004,000A) equals CONTROL_POINT. |
| &gt;&gt;&gt;Include Table 10-11 SOP Instance Reference Macro Attributes |   |   |   |
| &gt;&gt;Nominal Beam Energy | (300A,0114) | 3 | Nominal Beam Energy at control point (MV/MeV). |
| &gt;&gt;Dose Rate Set | (300A,0115) | 3 | Dose Rate to be set on treatment machine for segment beginning at current control point (e.g., MU/min). |
| &gt;&gt;Wedge Position Sequence | (300A,0116) | 1C | A Sequence of Items describing Wedge Positions for the current control point. Required for first Item of Control Point Sequence (300A,0111) if Number of Wedges (300A,00D0) is non-zero, and in subsequent control points if Wedge Position (300A,0118) changes during Beam. See Section C.********. The number of Items in this Sequence shall equal the Value of Number of Wedges (300A,00D0). |
| &gt;&gt;&gt;Referenced Wedge Number | (300C,00C0) | 1 | Uniquely references Wedge described by Wedge Number (300A,00D2) in Wedge Sequence (300A,00D1). |
| &gt;&gt;&gt;Wedge Position | (300A,0118) | 1 | Position of Wedge at current Control Point. **Enumerated Values:** IN OUT |
| &gt;&gt;Beam Limiting Device Position Sequence | (300A,011A) | 1C | Sequence of beam limiting device (collimator) jaw or leaf (element) positions. Required for first Item of Control Point Sequence (300A,0111), or if the values of the Beam Limiting Device change during the Beam, and if Enhanced RT Beam Limiting Device Definition Flag (3008,00A3) is absent, or is present and has the Value NO. One or more Items shall be included in this Sequence. In the first Control Point the number of Items shall be equal to the number of Items of Beam Limiting Device Sequence (300A,00B6). In subsequent Control Points the Items present shall be only those whose values change during the Beam. |
| &gt;&gt;&gt;RT Beam Limiting Device Type | (300A,00B8) | 1 | Type of beam limiting device (collimator). The Value of this Attribute shall correspond to RT Beam Limiting Device Type (300A,00B8) defined in an Item of Beam Limiting Device Sequence (300A,00B6). **Enumerated Values:** X symmetric jaw pair in IEC X direction Y symmetric jaw pair in IEC Y direction ASYMX asymmetric jaw pair in IEC X direction ASYMY asymmetric jaw pair in IEC Y direction MLCX single layer multileaf collimator in IEC X direction MLCY single layer multileaf collimator in IEC Y direction |
| &gt;&gt;&gt;Leaf/Jaw Positions | (300A,011C) | 1 | Positions of beam limiting device (collimator) leaf (element) or jaw pairs (in mm) in IEC BEAM LIMITING DEVICE coordinate axis appropriate to RT Beam Limiting Device Type (300A,00B8), e.g., X-axis for MLCX, Y-axis for MLCY. Contains 2N Values, where N is the Number of Leaf/Jaw Pairs (300A,00BC) in Beam Limiting Device Sequence (300A,00B6). Values shall be listed in IEC leaf (element) subscript order 101, 102, 1N, 201, 202, 2N. See Note 3. |
| &gt;&gt;Enhanced RT Beam Limiting Opening Sequence | (3008,00A2) | 2C | Sequence of beam limiting device (collimator) jaw or leaf (element) positions. Required for first Item of Control Point Sequence (300A,0111), or if the Values of the Beam Limiting Device change during Beam and if Enhanced RT Beam Limiting Device Definition Flag (3008,00A3) is present and has the Value YES. One or more Items shall be included in this Sequence. The number of Items shall equal the number of Items in Enhanced RT Beam Limiting Device Sequence (3008,00A1) in the first Control Point and be equal or less in subsequent Control Points. See Section C.********8 Presence of Items within Sequences in the Control Point Sequence. |
| &gt;&gt;&gt;Include Table C.*********-1 RT Beam Limiting Device Opening Definition Macro Attributes |   |   | See Section C.********7 Enhanced RT Beam Limiting Device Sequence and Enhanced RT Beam Limiting Opening Sequence. |
| &gt;&gt;Gantry Angle | (300A,011E) | 1C | Gantry angle of radiation source, i.e., orientation of IEC GANTRY coordinate system with respect to IEC FIXED REFERENCE coordinate system (degrees). Required for first Item of Control Point Sequence (300A,0111), or if Gantry Angle changes during Beam. See Section C.********3. |
| &gt;&gt;Gantry Rotation Direction | (300A,011F) | 1C | Direction of Gantry Rotation when viewing gantry from isocenter, for segment following Control Point. Required for first Item of Control Point Sequence (300A,0111), or if Gantry Rotation Direction changes during Beam. See Section C.8.8.14.8. **Enumerated Values:** CW clockwise CC counter-clockwise NONE no rotation |
| &gt;&gt;Gantry Pitch Angle | (300A,014A) | 3 | Gantry Pitch Angle. i.e., the rotation of the IEC GANTRY coordinate system about the X-axis of the IEC GANTRY coordinate system (degrees). If used, must be present for first Item of Control Point Sequence (300A,0111), or if used and Gantry Pitch Rotation Angle changes during Beam, must be present. See Section C.8.8.25.6.5. See Section C.********3. |
| &gt;&gt;Gantry Pitch Rotation Direction | (300A,014C) | 3 | Direction of Gantry Pitch Angle when viewing along the positive X-axis of the IEC GANTRY coordinate system, for segment following Control Point. If used, must be present for first Item of Control Point Sequence (300A,0111), or if used and Gantry Pitch Rotation Direction changes during Beam, must be present. See Section C.8.8.14.8 and Section C.8.8.25.6.5. **Enumerated Values:** CW clockwise CC counter-clockwise NONE no rotation |
| &gt;&gt;Beam Limiting Device Angle | (300A,0120) | 1C | Beam Limiting Device angle, i.e., orientation of IEC BEAM LIMITING DEVICE coordinate system with respect to IEC GANTRY coordinate system (degrees). Required for first Item of Control Point Sequence (300A,0111), or if Beam Limiting Device Angle changes during Beam. See Section C.********3. |
| &gt;&gt;Beam Limiting Device Rotation Direction | (300A,0121) | 1C | Direction of Beam Limiting Device Rotation when viewing beam limiting device (collimator) from radiation source, for segment following Control Point. Required for first Item of Control Point Sequence (300A,0111), or if Beam Limiting Device Rotation Direction changes during Beam. See Section C.8.8.14.8. **Enumerated Values:** CW clockwise CC counter-clockwise NONE no rotation |
| &gt;&gt;Patient Support Angle | (300A,0122) | 1C | Patient Support angle, i.e., orientation of IEC PATIENT SUPPORT (turntable) coordinate system with respect to IEC FIXED REFERENCE coordinate system (degrees). Required for first Item of Control Point Sequence (300A,0111), or if Patient Support Angle changes during Beam. See Section C.********3. |
| &gt;&gt;Patient Support Rotation Direction | (300A,0123) | 1C | Direction of Patient Support Rotation when viewing table from above, for segment following Control Point. Required for first Item of Control Point Sequence (300A,0111), or if Patient Support Rotation Direction changes during Beam. See Section C.8.8.14.8. **Enumerated Values:** CW clockwise CC counter-clockwise NONE no rotation |
| &gt;&gt;Table Top Eccentric Axis Distance | (300A,0124) | 3 | Distance (positive) from the IEC PATIENT SUPPORT vertical axis to the IEC TABLE TOP ECCENTRIC vertical axis (mm). |
| &gt;&gt;Table Top Eccentric Angle | (300A,0125) | 1C | Table Top (non-isocentric) angle, i.e., orientation of IEC TABLE TOP ECCENTRIC coordinate system with respect to IEC PATIENT SUPPORT coordinate system (degrees). Required for first Item of Control Point Sequence (300A,0111), or if Table Top Eccentric Angle changes during Beam. See Section C.********3. |
| &gt;&gt;Table Top Eccentric Rotation Direction | (300A,0126) | 1C | Direction of Table Top Eccentric Rotation when viewing table from above, for segment following Control Point. Required for first Item of Control Point Sequence (300A,0111), or if Table Top Eccentric Rotation Direction changes during Beam. See Section C.8.8.14.8. **Enumerated Values:** CW clockwise CC counter-clockwise NONE no rotation |
| &gt;&gt;Table Top Pitch Angle | (300A,0140) | 1C | Table Top Pitch Angle, i.e., the rotation of the IEC TABLE TOP coordinate system about the X-axis of the IEC TABLE TOP coordinate system (degrees). If required by treatment delivery device, shall be present for first Item of Control Point Sequence (300A,0111). If required by treatment delivery device and if Table Top Pitch Angle changes during Beam, shall be present in all subsequent Items of Control Point Sequence (300A,0111). See Section C.********2 and Section C.********3. |
| &gt;&gt;Table Top Pitch Rotation Direction | (300A,0142) | 1C | Direction of Table Top Pitch Rotation when viewing the table along the positive X-axis of the IEC TABLE TOP coordinate system, for segment following Control Point. If required by treatment delivery device, shall be present for first Item of Control Point Sequence (300A,0111). If required by treatment delivery device and if Table Top Pitch Rotation Direction changes during Beam, shall be present in all subsequent Items of Control Point Sequence (300A,0111). See Section C.8.8.14.8 and Section C.********2. **Enumerated Values:** CW clockwise CC counter-clockwise NONE no rotation |
| &gt;&gt;Table Top Roll Angle | (300A,0144) | 1C | Table Top Roll Angle, i.e., the rotation of the IEC TABLE TOP coordinate system about the IEC Y-axis of the IEC TABLE TOP coordinate system (degrees). If required by treatment delivery device, shall be present for first Item of Control Point Sequence (300A,0111). If required by treatment delivery device and if Table Top Roll Angle changes during Beam, shall be present in all subsequent Items of Control Point Sequence (300A,0111). See Section C.********2 and Section C.********3. |
| &gt;&gt;Table Top Roll Rotation Direction | (300A,0146) | 1C | Direction of Table Top Roll Rotation when viewing the table along the positive Y-axis of the IEC TABLE TOP coordinate system, for segment following Control Point. If required by treatment delivery device, shall be present for first Item of Control Point Sequence (300A,0111). If required by treatment delivery device and if Table Top Roll Rotation Direction changes during Beam, shall be present in all subsequent Items of Control Point Sequence (300A,0111). See Section C.8.8.14.8 and Section C.********2. **Enumerated Values:** CW clockwise CC counter-clockwise NONE no rotation |
| &gt;&gt;Table Top Vertical Position | (300A,0128) | 2C | Table Top Vertical position in IEC TABLE TOP coordinate system (mm). Required for first Item of Control Point Sequence (300A,0111), or if Table Top Vertical Position changes during Beam. See Section C.********. |
| &gt;&gt;Table Top Longitudinal Position | (300A,0129) | 2C | Table Top Longitudinal position in IEC TABLE TOP coordinate system (mm). Required for first Item of Control Point Sequence (300A,0111), or if Table Top Longitudinal Position changes during Beam. See Section C.********. |
| &gt;&gt;Table Top Lateral Position | (300A,012A) | 2C | Table Top Lateral position in IEC TABLE TOP coordinate system (mm). Required for first Item of Control Point Sequence (300A,0111), or if Table Top Lateral Position changes during Beam. See Section C.********. |
| &gt;&gt;Isocenter Position | (300A,012C) | 2C | Isocenter coordinates (x,y,z) in the Patient-Based Coordinate System described in Section C.7.6.2.1.1 (mm). Required for first Item of Segment Control Point Sequence, or if Segment Isocenter Position changes during Beam. |
| &gt;&gt;Surface Entry Point | (300A,012E) | 3 | Patient surface entry point coordinates (x,y,z) in the Patient-Based Coordinate System described in Section C.7.6.2.1.1 (mm). |
| &gt;&gt;External Contour Entry Point | (300A,0133) | 3 | External Contour entry point coordinates (x,y,z) in the Patient-Based Coordinate System described in Section C.7.6.2.1.1 (mm). See Section C.********5. |
| &gt;&gt;Source to Surface Distance | (300A,0130) | 3 | Source to Patient Surface (skin) distance (mm). |
| &gt;&gt;Source to External Contour Distance | (300A,0132) | 3 | Source to External Contour distance (mm) including devices associated with the patient anatomy model. For dosimetric purposes this Value may differ from the Source to Surface Distance (300A,0130). See Section C.********5. |
### Note
- Beam Number (300A,00C0) is provided to link related information across Modules, and its Value should not be required to have any real-world interpretation. Beam Name (300A,00C2), a Type 3 Attribute, is intended to store the primary beam identifier (often referred to as "field identifier"). Entity Long Label (3010,0038), a Type 3 Attribute, is intended to store additional beam identifying information (often referred to as "field name"). Beam Description (300A,00C3), a Type 3 Attribute, is intended to store beam summary information (often referred to as "field note"). Equipment supporting these Attributes should state this clearly in the Conformance Statement.
- The RT Beams Module does not support the transmission of treatment unit modeling information such as depth doses and beam profiles, except for absolute dose calibration information.
- Implementers should take note that Leaf Position Boundaries (300A,00BE) are the positions of the mechanical boundaries (projected to the isocentric plane) between beam limiting device (collimator) leaves, fixed for a given beam limiting device (collimator). Leaf/Jaw Positions (300A,011C) are values specific to a given beam control point, specifying the beam limiting device (collimator) leaf (element) openings.
- Block coordinates may not be transmitted when such data is not available from the transmitting system. However, the receiving system may not have internal mechanisms to use or store such data. For example, a plan transmitted from an treatment planning system to a Record and Verify (R&amp;V) system will contain the block data for blocked beams. Subsequent transfer of beam data from the R&amp;V system may omit this data since the R&amp;V system may not have stored it.
- Refer to Section C.******** for examples of STATIC and DYNAMIC Beam Type. Note that beams having Wedge Type = DYNAMIC as the only moving parameter are not considered DYNAMIC according to the definition of Beam Type (300A,00C4).
- Attributes Beam Dose Point Depth (300A,0088), Beam Dose Point Equivalent Depth (300A,0089), Beam Dose Point SSD (300A,008A) were previously included in this Module on the level of Beam Sequence (300A,00B0) &gt; Control Points Beam Sequence (300A,0111) &gt; Referenced Dose Reference Sequence (300C,0050). These Attributes have been retired at this location. See [PS3.3-2011](http://medical.nema.org/MEDICAL/Dicom/2011/11_03pu.pdf).
##### C.******** Meterset Calculations
The Meterset at a given Control Point is equal to Beam Meterset (300A,0086) specified in the Referenced Beam Sequence (300C,0004) of the RT Fraction Scheme Module, multiplied by the Cumulative Meterset Weight (300A,0134) for the Control Point, divided by the Final Cumulative Meterset Weight (300A,010E). The Meterset is specified in units defined by Primary Dosimeter Unit (300A,00B3). If the calculation for Meterset results in a Meterset value that is not an exact multiple of the primary Meterset resolution, then the result shall be rounded to the nearest allowed Meterset value (i.e., less than a half resolution unit shall be rounded down to the nearest resolution unit, and equal or greater than half a resolution unit shall be rounded up to the nearest resolution unit).
Note also that if Final Cumulative Meterset Weight (300A,010E) is equal to 100, then Cumulative Meterset Weight (300A,0134) becomes equivalent to the percentage of Beam Meterset (300A,0086) delivered at each control point. If Final Cumulative Meterset Weight (300A,010E) is equal to Beam Meterset (300A,0086), then the Cumulative Meterset Weight (300A,0134) at each control point becomes equal to the cumulative Meterset delivered at that control point.
##### C.******** Planned Verification Image Sequence
The Planned Verification Image Sequence (300A,00CA) contains Attributes that describe the planned verification images to be acquired during current beam. The Start Cumulative Meterset Weight (300C,0008) specifies the Cumulative Meterset Weight at which image acquisition is to begin. If Meterset Exposure (3002,0032) is present in a Sequence Item and End Cumulative Meterset Weight (300C,0009) is not present then a single image shall be acquired using the Meterset duration specified in Meterset Exposure (3002,0032). If End Cumulative Meterset Weight (300C,0009) is present in a Sequence Item and Meterset Exposure (3002,0032) is not present then a single image shall be acquired over the beam delivery from Start Cumulative Meterset Weight (300C,0008) to End Cumulative Meterset Weight (300C,0009). If both Meterset Exposure (3002,0032) and End Cumulative Meterset Weight (300C,0009) are present in a Sequence Item then images shall be acquired every Meterset Exposure (3002,0032) from Start Cumulative Meterset Weight (300C,0008) to End Cumulative Meterset Weight (300C,0009). No images shall extend past End Cumulative Meterset Weight (300C,0009).
##### C.******** X-Ray Image Receptor Angle
The X-Ray Image Receptor Angle (3002,000E) specifies the rotation of the image receptor device in the IEC X-RAY IMAGE RECEPTOR PLANE. A positive angle corresponds to a counter-clockwise rotation of the X-Ray Image Receptor as viewed from the radiation source in the IEC GANTRY coordinate system. The normal (non-rotated) Value for this parameter is zero degrees.
##### C.******** Multiple Aperture Blocks
All blocks with Block Type (300A,00F8) of APERTURE for a given beam shall have equal Values of Block Transmission (300A,0102) and/or Block Thickness (300A,0100) if they are specified. The composite aperture shall be evaluated as the union of the individual apertures within a single Block. Shielding block transmission(s) shall be applied multiplicatively after the (composite) aperture has been evaluated.
##### C.******** Control Point Sequence
The RT Beams Module uses a single beam model to handle static, arc, and dynamic delivery of external beam radiation by a medical accelerator or gamma beam therapy equipment (cobalt unit). All applicable parameters shall be specified at Control Point 0, with the exception of couch positions (see Section C.******** ). All parameters that change at any control point of a given beam shall be specified explicitly at all control points (including those preceding the change). No assumptions are made about the behavior of machine parameters between specified control points, and communicating devices shall agree on this behavior outside the current Standard.
The Cumulative Meterset Weight (300A,0134) Values in a Control Point Sequence (300A,0111) shall be monotonically increasing in the order of increasing Control Point Index (300A,0112).
Gantry Rotation Direction (300A,011F), Beam Limiting Device Rotation Direction (300A,0121), Patient Support Rotation Direction (300A,0123), and Table Top Eccentric Rotation Direction (300A,0126) are defined as applying to the segment following the control point, and changes to these parameters during treatment may be specified without use of a "non-irradiation" segment. All other Control Point Sequence Attributes are defined only at the control point. To unambiguously encode changes in discrete-valued Attributes such as Wedge Position (300A,0118) and Nominal Beam Energy (300A,0114), a non-irradiation segment where Cumulative Meterset Weight (300A,0134) does not change, shall be used.
Some examples of beam specification using control points are as follows:
- Static delivery: Control Point 0: All applicable treatment parameters defined, Cumulative Meterset Weight = 0
- Control Point 1: Cumulative Meterset Weight = 1, no other parameters defined
- Arc delivery: Control Point 0: All applicable treatment parameters defined, Cumulative Meterset Weight = 0, Gantry Rotation Direction = rotation direction , Gantry Angle = initial angle
- Control Point 1: Cumulative Meterset Weight = 1, Gantry Rotation Direction = NONE, Gantry Angle = final angle
- Dynamic delivery of two equally weighted segments : Control Point 0: All applicable treatment parameters defined, Cumulative Meterset Weight = 0
- Control Point 1: All changing treatment parameters defined (including those which do not change at this control point), Cumulative Meterset Weight = 0.5
- Control Point 2: All changing treatment parameters defined (including those which do not change at this control point), Cumulative Meterset Weight = 1
- Dynamic Delivery of two unequally weighted segments with a step change in table angle : Control Point 0: All applicable treatment parameters defined, Patient Support Angle = initial angle , Patient Support Rotation Direction = NONE, Cumulative Meterset Weight = 0
- Control Point 1: All changing parameters defined (including those that do not change at this control point), Cumulative Meterset Weight = 0.3, Patient Support Angle = initial angle , Patient Support Rotation Direction = rotation direction
- Control Point 2: All changing parameters defined (although none should change at this control point), Cumulative Meterset Weight = 0.3, Patient Support Angle = new angle , Patient Support Rotation Direction = NONE
- Control Point 3: All changing parameters defined (including those that do not change at this control point), Cumulative Meterset Weight = 1, Patient Support Angle = new angle , Patient Support Rotation Direction = NONE
- Dynamic delivery with moving MLC leaves and stationary collimator jaws: : In this example the collimator jaws stay in the same position throughout the Beam, while the MLC leaves change positions. : Table C.********-1 illustrates the presence of Items in the Beam Limiting Device Position Sequence (300A,011A) and sample values. : **Table C.********-1. Example of dynamic collimation in RT Beams Module** Control Point Index (300A,0112) Number of Items present in Beam Limiting Device Position Sequence (300A,011A) Leaf/Jaw Positions (300A,011C) for Item with RT Beam Limiting Device Type (300A,00B8) = X Leaf/Jaw Positions (300A,011C) for Item with RT Beam Limiting Device Type (300A,00B8) = Y Leaf/Jaw Positions (300A,011C) for Item with RT Beam Limiting Device Type (300A,00B8) = Z 0 3 present with Values -5/5 present with Values -4/4 present with Values -4.9/-4.8//3.9/3.8 1 1 absent absent -4.8/-4.7//3.8/3.7 2 1 absent absent -4.7/-4.6//3.7/3.6 3 1 absent absent -4.6/-4.5//3.6/3.5 4 1 absent absent -4.5/-4.4//3.5/3.4 5 1 absent absent -4.4/-4.3//3.4/3.3 6 1 absent absent -4.3/-4.2//3.3/3.2
##### C.******** Absolute and Relative Machine Coordinates
All treatment machine parameters except couch translations are specified in absolute machine coordinates as defined by  [  IEC 61217  ] . For the Table Top Vertical Position (300A,0128), Table Top Longitudinal Position (300A,0129), and Table Top Lateral Position (300A,012A), if the first Control Point contains a Value of non-zero length, all subsequent Control Point position values are absolute values in their respective coordinate system. If the first Control Point contains a zero-length Value, all subsequent Control Point position values are specified relative to the (unknown) initial value.
##### C.******** Cumulative Dose Reference Coefficient
The Cumulative Dose Reference Coefficient (300A,010C) is the value by which Beam Dose (300A,0084) is multiplied to obtain the dose to the referenced dose reference site at the current control point (and after previous control points have been successfully administered). The Cumulative Dose Reference Coefficient (300A,010C) is by definition zero for the initial control point. The Cumulative Dose Reference Coefficient (300A,010C) of the final control point multiplied by Beam Dose (300A,0084) results in the final dose to the referenced dose reference site for the current beam. Dose calculation for dose reference sites other than points is not well defined.
The sum of the doses of all beams calculated to the referenced dose reference may be used in different clinical scenarios, indicated by the Dose Value Purpose (300A,061D) Value, see Section C.********.
Example
In a single target case with two beams, a volume prescription of 20 Gy over 10 fractions, the single target may be represented by two Items in the Dose Reference Sequence (300A,0010): one for tracking of the dose values over the course of the delivery of a treatment plan, and one for QA purposes, where the dose value is recalculated and compared to the planned value. The first Item may therefore contain a nominal dose by a physician (e.g., 20Gy), the second Item a calculated dose at a given spatial location (e.g., 21.785Gy). The dose values are determined using the Beam Dose (300A,0084) with a Beam Dose Meaning (300A,008B) FRACTION_LEVEL (indicating that the beam dose was calculated on fraction level and carries a nominally distributed dose only).
| Dose Reference Sequence | (300A,0010) | &lt;Sequence&gt; |
| &gt;Item 1 |   |   |
| &gt;Dose Reference Number | (300A,0012) | 1 |
| &gt;Dose Reference UID | (300A,0013) | *******.1 |
| &gt;Dose Reference Structure Type | (300A,0014) | VOLUME |
| &gt;Dose Value Purpose | (300A,061D) | TRACKING |
| &gt;Dose Value Interpretation | (300A,068B) | NOMINAL |
| &gt;Dose Reference Description | (300A,0016) | Tumor |
| &gt;Referenced ROI Number | (3006,0084) | 5 |
| &gt;Dose Reference Type | (300A,0020) | TARGET |
| &gt;Item 2 |   |   |
| &gt;Dose Reference Number | (300A,0012) | 2 |
| &gt;Dose Reference UID | (300A,0013) | *******.2 |
| &gt;Dose Reference Structure Type | (300A,0014) | COORDINATES |
| &gt;Dose Value Purpose | (300A,061D) | QA |
| &gt;Dose Value Interpretation | (300A,068B) | ACTUAL |
| &gt;Dose Reference Description | (300A,0016) | Tumor |
| &gt;Dose Reference Point Coordinates | (300A,0018) | 3.1\4.2\5.3 |
| &gt;Dose Reference Type | (300A,0020) | TARGET |
**Table C.********-1. Cumulative Dose Reference Calculation Example**
|   | Beam Dose (300A,0084) with Beam Dose Meaning (300A,008B): FRACTION_LEVEL | Dose Reference Number (300A,0012): 1 | Dose Reference Number (300A,0012): 2 | Final Value of Cumulative Dose Reference Coefficient (300A,010C) | Final Dose Value | Dose Value Purpose (300A,061D) / Dose Value Interpretation (300A,068B) | Final Value of Cumulative Dose Reference Coefficient (300A,010C) | Final Dose Value | Dose Value Purpose (300A,061D) / Dose Value Interpretation (300A,068B) |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| Beam 1 | 1.2 Gy | 1.0 | 1.2 Gy |   | 1.1476 | 1.3771 Gy |   |
| Beam 2 | 0.8 Gy | 1.0 | 0.8 Gy |   | 1.00175 | 0.8014 Gy |   |
| Sum |   |   | 2.0 Gy | TRACKING / NOMINAL |   | 2.1785 Gy | QA / ACTUAL |
| Sum x Fractions |   |   | 20.0 Gy | TRACKING / NOMINAL |   | 21.785 Gy |
##### C.8.8.14.8 Machine Rotations
For the machine rotation angles Gantry Angle (300A,011E), Beam Limiting Device Angle (300A,0120), Patient Support Angle (300A,0122), and Table Top Eccentric Angle (300A,0125), rotation direction is specified as clockwise (CW), counter-clockwise (CC), or NONE. The maximum permitted rotation between two Control Points is 360 degrees. Examples:
- Gantry Angle moves from 5 degrees to 5 degrees, Gantry Rotation Direction = NONE: No movement.
- Gantry Angle moves from 5 degrees to 5 degrees, Gantry Rotation Direction = CW: Full clockwise rotation (360 degrees).
- Table Angle moves from 170 degrees to 160 degrees, Table Rotation Direction = CC: Counter-clockwise rotation by 350 degrees (note direction of increasing table angle as defined by [ IEC 61217 ] ).
##### C.8.8.14.9 Compensator Thickness Data and Source to Compensator Distance
The values stored in Compensator Thickness Data (300A,00EC) and Source to Compensator Distance (300A,02E2) shall be parallel to the radiation beam axis if Compensator Divergence (300A,02E0) equals ABSENT, or divergent according to the beam geometrical divergence if Compensator Divergence (300A,02E0) equals PRESENT. If Compensator Divergence (300A,02E0) is not present, then the parallel or divergent nature of the thicknesses is as if ABSENT was specified for Compensator Divergence (300A,02E0).
##### C.********0 Compensator Transmission and Thickness Data Direction
The direction of the rows and columns in Compensator Transmission Data (300A,00EB) and Compensator Thickness Data (300A,00EC) is defined as follows: The direction of rows goes along the positive Xb direction and the direction of the columns does along the negative Yb direction of the IEC X-BEAM LIMITING DEVICE coordinate system. Other interpretations shall be documented in an implementation's conformance statement.
##### C.********1 Block and Compensator Precedence for Dosimetric Calculations
If Block Thickness (300A,0100) and Block Transmission (300A,0102) are present, Block Transmission shall have precedence for dosimetric calculations. If Compensator Transmission Data (300A,00EB) and Compensator Thickness Data (300A,00EC) are present, Compensator Transmission Data shall have precedence for dosimetric calculations.
##### C.********2 Table Top Pitch and Table Top Roll
Pitch and Roll Coordinate Systems of the Table Top are defined in  [  IEC 61217  ] . These angles are defined as rotations of the IEC Table Top System as indicated below.
The Table Top Pitch Angle is defined as the rotation of the coordinate axes Yt, Zt about axis Xt by an angle t; see Figure C.8.8.14-1. An increase in the value of angle t corresponds to the clockwise rotation of the Table Top as viewed from the Table Top coordinate system origin along the positive Xt axis.
The Table Top Roll Angle is defined as the rotation of the coordinate axes Xt, Zt about axis Yt by an angle t; see Figure C.8.8.14-2. An increase in the value of angle t corresponds to the clockwise rotation of the Table Top as viewed from the Table Top coordinate system origin along the positive Yt axis.
It is important to observe that the point of rotation is the origin of the table top system after the rotation of the PATIENT SUPPORT(s) system about Zs by s after the rotation of the Table top eccentric rotation (e) system about Ze by e and after the translation of Table top (t) system along Xe Ye Ze. This means that the rotation point of the Pitch and Roll angles is typically not the isocenter. The translational values Xt Yt Zt may need to be adjusted to preserve the patient position at the isocenter. e.g., a rotation of the Pitch angle by a positive angle at a position originally at Xt=0, Yt=100 and Zt=0 will lead to a negative Zt value and a slightly lower Yt if the patient position at isocenter is to be maintained.
The Pitch Angle rotation is applied before the Roll Angle rotation.
**Figure C.8.8.14-1. Table Top Pitch Angle**
**Figure C.8.8.14-2. Table Top Roll Angle**
##### C.********3 Angular Values in RT Beams Module
The Attributes that define angles refer to coordinate systems defined by  [  IEC 61217  ] . Where indicated in the DICOM Attribute definition, the angle uses the coordinate system (orientation of the rotation axis and the origin of the rotating coordinate system) defined in  [  IEC 61217  ] , however DICOM makes no restrictions on the range of values stored.  [  IEC 61217  ]  defines restrictions that only apply to user interface presentation.
##### C.********4 Effective Wedge Angle
The Effective Wedge Angle (300A,00DE) and Radiation Beam Effective Wedge Angle (300A,0654) describe the dosimetric angle of a motorized wedge accounting for the partial presence of the wedge in the beam. The presence of the wedge in the beam is either specified by the Wedge Position (300A,0118) in the Wedge Position Sequence (300A,0116) included in the Control Point Sequence (300A,0111) of the current beam or the RT Control Point Sequence of the current Radiation. When the wedge is in the beam throughout all control points, the Effective Wedge Angle (300A,00DE) and Radiation Beam Effective Wedge Angle (300A,0654) will have the same Value as the Wedge Angle (300A,00D5) or Radiation Beam Wedge Angle (300A,0652). Otherwise the Effective Wedge Angle (300A,00DE) or Radiation Beam Effective Wedge Angle (300A,0654) will have a lower value than the Wedge Angle (300A,00D5) or Radiation Beam Wedge Angle (300A,0652).
##### C.********5 Source to External Contour Distance and External Contour Entry Point
The Source to External Contour Distance (300A,0132) is the distance to the beam entry point (External Contour Entry Point), which may include Bolus, Patient Positioning Devices, Patient Immobilization Devices or other devices. This value is useful for including the attenuation effects of external devices on the dose calculation and for patient setup.
##### C.********6 Referenced Control Point
The number of Items in the Beam Dose Verification Control Point Sequence (300A,008C) is not required to be the same as in the Control Point Sequence (300A,0111). A different sampling can be chosen for the Beam Dose Verification Control Point Sequence, but where the Cumulative Meterset Weight of a Control Point Sequence (300A,008C) Item is the same it shall be referenced by the Referenced Control Point Index (300C,00F0).
##### C.********7 Enhanced RT Beam Limiting Device Sequence and Enhanced RT Beam Limiting Opening Sequence
When the Value of Enhanced RT Beam Limiting Device Definition Flag (3008,00A3) has the Value YES, the following applies to the content of Enhanced RT Beam Limiting Device Sequence (3008,00A1) and Enhanced RT Beam Limiting Opening Sequence (3008,00A2):
- For the Beam Modifier Definition Coordinate System used the following applies: The Base Beam Modifier Definition Coordinate System is the [ IEC 61217 ] GANTRY coordinate system.
- The RT Device Distance Reference Location is (130358, DCM, "Nominal Radiation Source Location").
- The Value of the RT Beam Modifier Definition Distance (300A,0688) equals the Value of Source-Axis Distance (300A,00B4).
- The Value of the Beam Modifier Orientation Angle (300A,0645) is 0 for IEC X direction and 90 for IEC Y direction.
### Note
The Values of boundaries and openings are therefore the same as if comparable parameters would be expressed in the Beam Limiting Device Sequence (300A,00B6).
- Values of Attributes of the Module RT Tolerance Tables C.8.8.11 apply to the Enhanced RT Beam Limiting Device Openings as follows: Tolerance Module C.8.8.11 RT Beam Limiting Device Type (300A,00B8) Enhanced RT Beam Limiting Opening Sequence (3008,00A2) Device Type Code Sequence (3010,002E) and Beam Modifier Orientation Angle (300A,0645) Beam Limiting Device Position Tolerance (300A,004A) X, ASYMX Parallel RT Beam Delimiter Positions (300A,064A) (130330, DCM, "Jaw Pair") Beam Modifier Orientation Angle (300A,0645) = 0 Beam Limiting Device Position Tolerance (300A,004A) Y, ASYMY Parallel RT Beam Delimiter Positions (300A,064A) (130330, DCM, "Jaw Pair") Beam Modifier Orientation Angle (300A,0645) = 90 Beam Limiting Device Position Tolerance (300A,004A) MLCX Parallel RT Beam Delimiter Positions (300A,064A) (130331, DCM, "Leaf Pair") or (130333, DCM, "Single Leaves") Beam Modifier Orientation Angle (300A,0645) = 0 Beam Limiting Device Position Tolerance (300A,004A) MLCY Parallel RT Beam Delimiter Positions (300A,064A) (130331, DCM, "Leaf Pair") or (130333, DCM, "Single Leaves") Beam Modifier Orientation Angle (300A,0645) = 90 Beam Limiting Device Position Tolerance (300A,004A) MLCX RT Beam Limiting Device Offset (300A,064B) (130330, DCM, "Jaw Pair"), (130331, DCM, "Leaf Pair") or (130333, DCM, "Single Leaves") Beam Modifier Orientation Angle (300A,0645) = 0 Beam Limiting Device Position Tolerance (300A,004A) MLCY RT Beam Limiting Device Offset (300A,064B) (130330, DCM, "Jaw Pair"), (130331, DCM, "Leaf Pair") or (130333, DCM, "Single Leaves") Beam Modifier Orientation Angle (300A,0645) = 90
##### C.********8 Presence of Items within Sequences in the Control Point Sequence
Items within Sequences in the Control Point Sequence shall be present in the first Control Point or if the Value of any Attribute in an Item changes during the Beam.
If an Item is present, all Attributes of that Item shall be present if the Attribute requirements apply, even if the Value of the Attribute does not change during the Beam.
Example:
A beam may be delivered with two MLCs "A" and "B", where the values of MLC positions for MLC "A" do not change during the Beam, while the values for MLC "B" are changing. Each MLC has a constant value for its RT Beam Limiting Device Offset (300A,064B).
The Item describing MLC "A" is present in the first Control Point only.
The Items describing MLC "B" will be present in all Control Points containing the values of Parallel RT Beam Delimiter Positions. The Values of RT Beam Limiting Device Offset (300A,064B) for that MLC will be also present in all Items, even if these values remain constant during the Beam.
##### C.********9 Definition Source Sequence
The Definition Source Sequence (0008,1156) may reference SOP Instances of Second Generation Radiotherapy IODs containing the same clinical content as the current Item.
Permitted SOP Classes in this Sequence shall contain the following Module:
- Section C.36.13 RT Radiation Common Module
##### C.********* Table Top Position Alignment UID
The Table Top Position Alignment UID (300A,0054) identifies the positional alignment of the Table Top. When different Table Tops have the same Table Top Position Alignment UID (300A,0054), the Values of Table Top Vertical Position (300A,0128), Table Top Longitudinal Position (300A,0129) and Table Top Lateral Position (300A,012A) are known to be interchangeable between these devices and when applied, end up at the same treatment position relative to the treatment delivery device.
Table Top position values are typically acquired during the first treatment session and these acquired values will be applied in consecutive treatment sessions. During the course of the treatment, the patient may be treated with a treatment delivery device different from the one that the positions were originally acquired with. If Table Top Position Alignment UID (300A,0054) in plan differs from the Table Top Position Alignment UID configured for the treatment delivery device, the device can take appropriate action and e.g., ignore the incoming positional values or prompt the user to verify the applied Table Top position before actual delivery.
The acquired Table Top positions can be updated into the consecutive treatment plans or communicated using the RT Beams Delivery Instruction IOD.