<div class="section">
 <div class="titlepage">
  <div>
   <div>
    <h3 class="title">
     <a id="sect_C.11.2" shape="rect">
     </a>
     C.11.2 VOI LUT Module
    </h3>
   </div>
  </div>
 </div>
 <p>
  <a id="para_427f5eb6-cfd8-4e9e-8047-e9b3afde44c0" shape="rect">
  </a>
  <a class="xref" href="#table_C.11-2" shape="rect" title="Table C.11-2. VOI LUT Module Attributes">
   Table C.11-2
  </a>
  specifies the Attributes of the
  <a class="xref" href="#sect_C.11.2" shape="rect" title="C.11.2 VOI LUT Module">
   VOI LUT Module
  </a>, which describe the VOI LUT.
 </p>
 <div class="table">
  <a id="table_C.11-2" shape="rect">
  </a>
  <p class="title">
   <strong>
    Table C.11-2. VOI LUT Module Attributes
   </strong>
  </p>
  <div class="table-contents">
   <table frame="box" rules="all">
    <thead>
     <tr valign="top">
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_7b7e1055-a73c-46b3-839f-773cb0b3fc7a" shape="rect">
        </a>
        Attribute Name
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_1c48d5d1-d742-4a86-9ceb-ddac6965ac99" shape="rect">
        </a>
        Tag
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_98c8a326-4990-4860-b710-193d7e4cbf6b" shape="rect">
        </a>
        Type
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_259496ab-58ab-4bef-a1eb-f002424467d9" shape="rect">
        </a>
        Attribute Description
       </p>
      </th>
     </tr>
    </thead>
    <tbody>
     <tr valign="top">
      <td align="left" colspan="4" rowspan="1">
       <p>
        <a id="para_445cf3d1-2016-4df4-b516-b38e35a02b2e" shape="rect">
        </a>
        <span class="italic">
         Include
         <a class="xref" href="#table_C.11-2b" shape="rect" title="Table C.11-2b. VOI LUT Macro Attributes">
          Table C.11-2b VOI LUT Macro Attributes
         </a>
        </span>
       </p>
      </td>
     </tr>
    </tbody>
   </table>
  </div>
 </div>
 <br class="table-break" clear="none"/>
 <div class="table">
  <a id="table_C.11-2b" shape="rect">
  </a>
  <p class="title">
   <strong>
    Table C.11-2b. VOI LUT Macro Attributes
   </strong>
  </p>
  <div class="table-contents">
   <table frame="box" rules="all">
    <thead>
     <tr valign="top">
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_d61a80d9-c22c-40b6-9a70-da1abd7d8159" shape="rect">
        </a>
        Attribute Name
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_5ff85ee1-618c-427d-b75b-cf0014082389" shape="rect">
        </a>
        Tag
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_b167d9fa-04ba-40e4-9465-9e8ba7c29a29" shape="rect">
        </a>
        Type
       </p>
      </th>
      <th align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_51fb6b43-54d3-4c9b-85b0-962e1efc5984" shape="rect">
        </a>
        Attribute Description
       </p>
      </th>
     </tr>
    </thead>
    <tbody>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_6cf459c0-7173-4ade-a142-9a28ee76ced9" shape="rect">
        </a>
        VOI LUT Sequence
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_0d25a715-e25c-45e2-ba38-744d0db5a094" shape="rect">
        </a>
        (0028,3010)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_8594621f-b321-4047-8276-28d77fb9208e" shape="rect">
        </a>
        1C
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_e9d7f59c-dae8-43aa-9257-a5fa93ce87d2" shape="rect">
        </a>
        Defines a Sequence of VOI LUTs.
       </p>
       <p>
        <a id="para_7dfd3858-7bea-4c48-b69e-e04cf2be8f40" shape="rect">
        </a>
        One or more Items shall be included in this Sequence.
       </p>
       <p>
        <a id="para_bec9ea36-dd19-4de8-84ef-55f5e23aac94" shape="rect">
        </a>
        Required if Window Center (0028,1050) is not present. May be present otherwise.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_8a2bfbb9-3274-4501-a2d5-94c62259463c" shape="rect">
        </a>
        &gt;LUT Descriptor
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_9d4d0522-cb7b-4603-b55e-a54156b60283" shape="rect">
        </a>
        (0028,3002)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_c1877125-2538-436b-a1c6-c735d1f1f397" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_e3a75469-b733-42c9-930c-908f99ebb5de" shape="rect">
        </a>
        Specifies the format of the LUT Data in this Sequence.
       </p>
       <p>
        <a id="para_9ec79c72-d1e5-4ed4-a77c-6d75a2a1a938" shape="rect">
        </a>
        See
        <a class="xref" href="#sect_C.********" shape="rect" title="C.******** LUT Descriptor">
         Section C.********
        </a>
        for further explanation.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_19017ebb-706f-4867-95a3-8e80326fa0c9" shape="rect">
        </a>
        &gt;LUT Explanation
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_a6dfd43d-7154-4d72-a106-104d9e0e8eb4" shape="rect">
        </a>
        (0028,3003)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_313b5383-6bc8-45a4-9696-29df190b152e" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_e5bf817d-9db5-45d4-a05e-55cca8df9728" shape="rect">
        </a>
        Free form text explanation of the meaning of the LUT.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_b8bd4f5c-b956-4898-af4b-5a15e1686c7a" shape="rect">
        </a>
        &gt;LUT Data
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_94ddbc95-5145-40a0-98e0-3c5e8855eee1" shape="rect">
        </a>
        (0028,3006)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_81405fa2-da86-4fdf-a6f4-b6b9ec4175a9" shape="rect">
        </a>
        1
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_23b298c5-7b67-4821-9f44-4f67827307b7" shape="rect">
        </a>
        LUT Data in this Sequence.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_ebfe0812-3506-4441-90ca-9d771244fd6d" shape="rect">
        </a>
        Window Center
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_a479cde9-702a-4918-9127-ee84675abdd1" shape="rect">
        </a>
        (0028,1050)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_79dea68f-6f1a-4a66-927f-bb8724bfc2b8" shape="rect">
        </a>
        1C
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_87f0bc34-2bec-4b70-8e14-61fe3ea63316" shape="rect">
        </a>
        Window Center for display.
       </p>
       <p>
        <a id="para_69a7673b-8827-444e-b7e9-ed827d88e883" shape="rect">
        </a>
        See
        <a class="xref" href="#sect_C.********" shape="rect" title="C.******** Window Center and Window Width">
         Section C.********
        </a>
        for further explanation.
       </p>
       <p>
        <a id="para_1c4f5fdf-8a57-4099-815b-a0e948a4847d" shape="rect">
        </a>
        Required if VOI LUT Sequence (0028,3010) is not present. May be present otherwise.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_1c20a9b7-de6c-4cf0-b6cb-fa8c1dd52cee" shape="rect">
        </a>
        Window Width
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_c53fd4df-e371-42a8-b534-a89e39a7b015" shape="rect">
        </a>
        (0028,1051)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_74968fe9-0cac-4e60-b676-f8e7fe5ff85f" shape="rect">
        </a>
        1C
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_ae20e976-7415-4c12-ae38-f45e88c5cc95" shape="rect">
        </a>
        Window Width for display. See
        <a class="xref" href="#sect_C.********" shape="rect" title="C.******** Window Center and Window Width">
         Section C.********
        </a>
        for further explanation.
       </p>
       <p>
        <a id="para_a010e7fe-73aa-4c7d-8699-9af7e685379a" shape="rect">
        </a>
        Required if Window Center (0028,1050) is present.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_35fcdca6-f277-426e-b323-aba0ce360fe9" shape="rect">
        </a>
        Window Center &amp; Width Explanation
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_de6be26d-aa06-4925-b2bd-bcaa76908b10" shape="rect">
        </a>
        (0028,1055)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_97b53b00-bab7-4c08-b96c-d28955546a27" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_8e00e5fa-f6fe-4eb6-8042-e3aa5969271a" shape="rect">
        </a>
        Free form explanation of the meaning of the Window Center and Width. Multiple values correspond to multiple Window Center and Width values.
       </p>
      </td>
     </tr>
     <tr valign="top">
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_adc6cae2-7c25-4e26-8da3-828c456a3163" shape="rect">
        </a>
        VOI LUT Function
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_f7f3bcf1-f019-4f87-8e54-149b0d4e9137" shape="rect">
        </a>
        (0028,1056)
       </p>
      </td>
      <td align="center" colspan="1" rowspan="1">
       <p>
        <a id="para_de3379f7-24c9-4774-8ce1-6a15538db0a3" shape="rect">
        </a>
        3
       </p>
      </td>
      <td align="left" colspan="1" rowspan="1">
       <p>
        <a id="para_4936889c-430b-4ec8-8009-cb3da34249c4" shape="rect">
        </a>
        Describes a VOI LUT function to apply to the Values of Window Center (0028,1050) and Window Width (0028,1051).
       </p>
       <p>
        <a id="para_b58b7a4e-cf27-45f2-ade8-b9318f231053" shape="rect">
        </a>
        See
        <a class="xref" href="#sect_C.********" shape="rect" title="C.******** VOI LUT Function">
         Section C.********
        </a>
        for further explanation.
       </p>
       <div class="variablelist">
        <p class="title">
         <strong>
          Defined Terms:
         </strong>
        </p>
        <dl class="variablelist compact">
         <dt>
          <span class="term">
           LINEAR
          </span>
         </dt>
         <dd>
          <p>
           <a id="para_e6775251-42ef-4fc3-9c9c-84e41f62892b" shape="rect">
           </a>
          </p>
         </dd>
         <dt>
          <span class="term">
           LINEAR_EXACT
          </span>
         </dt>
         <dd>
          <p>
           <a id="para_50f27c93-4e1b-4062-8247-26baaec58f92" shape="rect">
           </a>
          </p>
         </dd>
         <dt>
          <span class="term">
           SIGMOID
          </span>
         </dt>
         <dd>
          <p>
           <a id="para_20a01234-c3b3-4083-abeb-556b03e1f3db" shape="rect">
           </a>
          </p>
         </dd>
        </dl>
       </div>
       <p>
        <a id="para_75b1cfa0-ea7a-4290-a324-10947a739587" shape="rect">
        </a>
        When this Attribute is not present, the interpretation of the Values of Window Center (0028,1050) and Window Width (0028,1051) is linear as in
        <a class="xref" href="#sect_C.********" shape="rect" title="C.******** Window Center and Window Width">
         Section C.********
        </a>.
       </p>
      </td>
     </tr>
    </tbody>
   </table>
  </div>
 </div>
 <br class="table-break" clear="none"/>
 <div class="section">
  <div class="titlepage">
   <div>
    <div>
     <h4 class="title">
      <a id="sect_C.11.2.1" shape="rect">
      </a>
      C.11.2.1 VOI LUT Module Attribute Descriptions
     </h4>
    </div>
   </div>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h5 class="title">
       <a id="sect_C.********" shape="rect">
       </a>
       C.******** LUT Descriptor
      </h5>
     </div>
    </div>
   </div>
   <p>
    <a id="para_84ca867f-9539-4c07-8922-f7b35d826e61" shape="rect">
    </a>
    The three Values of LUT Descriptor (0028,3002) describe the format of the LUT Data in the corresponding LUT Data (0028,3006).
   </p>
   <p>
    <a id="para_2260e26e-8f5b-4241-8e2d-379132c1a8ff" shape="rect">
    </a>
    The first Value is the number of entries in the lookup table. When the number of table entries is equal to 2
    <sup>
     16
    </sup>
    then this Value shall be 0.
   </p>
   <p>
    <a id="para_adae5358-40f7-475c-803d-9f27a03cfa00" shape="rect">
    </a>
    The second Value is the first input value mapped. The Value Representation of the second Value (US or SS) depends on the source of the input to the VOI LUT, and shall be:
   </p>
   <div class="itemizedlist">
    <ul class="itemizedlist" style="list-style-type: disc; ">
     <li class="listitem">
      <p>
       <a id="para_71c11533-3764-4c80-a043-306754cf89d3" shape="rect">
       </a>
       the same as specified by Pixel Representation (0028,0103), if there is no Modality LUT or Rescale Slope and Intercept specified;
      </p>
     </li>
     <li class="listitem">
      <p>
       <a id="para_cc83d0ee-116a-43c4-b18d-299dea30d86f" shape="rect">
       </a>
       SS if the possible output range after application of the Rescale Slope and Intercept may be signed;
      </p>
      <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
       <h3 class="title">
        Note
       </h3>
       <p>
        <a id="para_d061e293-c802-4910-9e48-efd6e9189a3a" shape="rect">
        </a>
        This is always the case for the CT Image IOD in which the Rescale Type is specified to be Hounsfield Units, which are always signed.
       </p>
      </div>
     </li>
     <li class="listitem">
      <p>
       <a id="para_95a23eb5-e989-477b-804f-008657bddb83" shape="rect">
       </a>
       US otherwise.
      </p>
     </li>
    </ul>
   </div>
   <p>
    <a id="para_be43b3cb-0b5e-4747-8a93-f09fc3bc04ba" shape="rect">
    </a>
    This input value is mapped to the first entry in the LUT. All input values less than the first value mapped are also mapped to the first entry in the LUT Data. An input value one greater than the first value mapped is mapped to the second entry in the LUT Data. Subsequent input values are mapped to the subsequent entries in the LUT Data up to an input value equal to number of entries + first value mapped - 1 that is mapped to the last entry in the LUT Data. Input values greater than or equal to number of entries + first value mapped are also mapped to the last entry in the LUT Data.
   </p>
   <p>
    <a id="para_9bf4fa48-bd88-43d0-8bf0-84ec9de9cc8e" shape="rect">
    </a>
    The third Value specifies the number of bits for each entry in the LUT Data. If the VOI LUT is included in an Image IOD, The third Value of LUT Descriptor (0028,3002) shall be 8 or 16 bits, unless otherwise specialized. If the VOI LUT is included in a Presentation State IOD, The third Value of LUT Descriptor (0028,3002) shall be between 8 and 16 inclusive. The LUT Data shall be stored in a format equivalent to 8 bits allocated when the number of bits for each entry is 8, and 16 bits allocated when the number of bits for each entry is 16, where in both cases the high bit is equal to bits stored - 1, and where bits stored is The third Value.
   </p>
   <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
    <h3 class="title">
     Note
    </h3>
    <div class="orderedlist">
     <ol class="orderedlist" type="1">
      <li class="listitem">
       <p>
        <a id="para_77dcbd67-0b9e-4292-9263-9207875f8927" shape="rect">
        </a>
        Since LUT Descriptor (0028,3002) is multi-valued, in an Explicit VR Transfer Syntax, only one Value Representation (US or SS) may be specified, even though the first and third values are always by definition interpreted as unsigned. The explicit VR actually used is dictated by the VR needed to represent the second Value.
       </p>
      </li>
      <li class="listitem">
       <p>
        <a id="para_a06eea99-839e-4559-8d78-1e59937ab8d7" shape="rect">
        </a>
        Some implementations have encoded 8 bit entries with 16 bits allocated, padding the high bits; this can be detected by comparing the number of entries specified in the LUT Descriptor with the actual Value Length of the LUT Data entry. The Value Length in bytes should equal the number of entries if bits allocated is 8, and be twice as long if bits allocated is 16.
       </p>
      </li>
     </ol>
    </div>
   </div>
   <p>
    <a id="para_f92c3617-ab69-41cd-989e-b29c3b498269" shape="rect">
    </a>
    The LUT Data contains the LUT entry values.
   </p>
   <p>
    <a id="para_ef57e7f2-f5c1-49aa-b736-db90c549e163" shape="rect">
    </a>
    The output range is from 0 to 2
    <sup>
     n
    </sup>
    -1 where n is The third Value of LUT Descriptor. This range is always unsigned.
   </p>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h5 class="title">
       <a id="sect_C.********" shape="rect">
       </a>
       C.******** Window Center and Window Width
      </h5>
     </div>
    </div>
   </div>
   <div class="section">
    <div class="titlepage">
     <div>
      <div>
       <h6 class="title">
        <a id="sect_C.********.1" shape="rect">
        </a>
        C.********.1 Default LINEAR Function
       </h6>
      </div>
     </div>
    </div>
    <p>
     <a id="para_e94291c7-07cc-4ece-a203-5f841316163f" shape="rect">
     </a>
     If VOI LUT Function (0028,1056) is absent or has a Value of LINEAR, Window Center (0028,1050) and Window Width (0028,1051) specify a linear conversion from stored pixel values (after any Modality LUT or Rescale Slope and Intercept specified in the IOD have been applied) to values to be displayed. Window Center contains the input value that is the center of the window. Window Width contains the width of the window.
    </p>
    <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
     <h3 class="title">
      Note
     </h3>
     <p>
      <a id="para_bc03caec-482d-494f-a2ef-41c9cdc33610" shape="rect">
      </a>
      The terms "window center" and "window width" are not consistently used in practice, nor were they defined in previous releases of the Standard. The definitions here are presented for the purpose of defining consistent meanings for identity and threshold transformations while preserving the common practice of using integer values for center and width.
     </p>
    </div>
    <p>
     <a id="para_fdba9a54-54a8-4c55-812a-ca36ccf62ff6" shape="rect">
     </a>
     Window Width (0028,1051) shall always be greater than or equal to 1.
    </p>
    <p>
     <a id="para_847757a6-aec1-4624-acac-39253df4d713" shape="rect">
     </a>
     When Window Width (0028,1051) is greater than 1, these Attributes select the range of input values that are to be mapped to the full range of the displayed output.
    </p>
    <p>
     <a id="para_28f8329f-019d-4af4-a5c0-2f3600a56734" shape="rect">
     </a>
     When Window Width (0028,1051) is equal to 1, they specify a threshold below which input values will be displayed as the minimum output value.
    </p>
    <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
     <h3 class="title">
      Note
     </h3>
     <p>
      <a id="para_293644fe-4d3e-4f38-a0ae-129ea1ad4d53" shape="rect">
      </a>
      Whether the minimum output value is rendered as black or white may depend on the Value of Photometric Interpretation (0028,0004) or the presence of a
      <a class="xref" href="#sect_C.11.4" shape="rect" title="C.11.4 Presentation LUT Module">
       Presentation LUT Module
      </a>.
     </p>
    </div>
    <p>
     <a id="para_eb8cd21d-9712-434f-8574-549df91b2b2d" shape="rect">
     </a>
     These Attributes are applied according to the following pseudo-code, where x is the input value, y is an output value with a range from y
     <sub>
      min
     </sub>
     to y
     <sub>
      max
     </sub>, c is Window Center (0028,1050) and w is Window Width (0028,1051):
    </p>
    <div class="itemizedlist">
     <ul class="itemizedlist" style="list-style-type: none; ">
      <li class="listitem" style="list-style-type: none">
       <p>
        <a id="para_0f75357f-6960-4fb9-94b2-e32af3404d4f" shape="rect">
        </a>
        if (x &lt;= c - 0.5 - (w-1) /2), then y = y
        <sub>
         min
        </sub>
       </p>
      </li>
      <li class="listitem" style="list-style-type: none">
       <p>
        <a id="para_570ac055-2c4d-4683-a399-f8d71992f99f" shape="rect">
        </a>
        else if (x &gt; c - 0.5 + (w-1) /2), then y = y
        <sub>
         max
        </sub>
       </p>
      </li>
      <li class="listitem" style="list-style-type: none">
       <p>
        <a id="para_3444862b-e06e-453e-9f51-9dfaeac648de" shape="rect">
        </a>
        else y = ((x - (c - 0.5)) / (w-1) + 0.5) * (y
        <sub>
         max
        </sub>
        - y
        <sub>
         min
        </sub>
        ) + y
        <sub>
         min
        </sub>
       </p>
      </li>
     </ul>
    </div>
    <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
     <h3 class="title">
      Note
     </h3>
     <div class="orderedlist">
      <ol class="orderedlist" type="1">
       <li class="listitem">
        <p>
         <a id="para_2db6ff52-057f-4e09-8cc6-f86a1da89bd2" shape="rect">
         </a>
         For the purpose of this definition, a floating point calculation without integer truncation is assumed, though the manner of implementation may vary as long as the result is the same.
        </p>
       </li>
       <li class="listitem">
        <p>
         <a id="para_bfabc447-6238-471a-a73a-e4c8d8fe4659" shape="rect">
         </a>
         The pseudo-code function computes a continuous value over the output range without any discontinuity at the boundaries. The value of 0 for w is expressly forbidden, and the value of 1 for w does not cause division by zero, since the continuous segment of the function will never be reached for that case.
        </p>
       </li>
       <li class="listitem">
        <p>
         <a id="para_8eced552-d9da-412b-827f-a34d1cf6554f" shape="rect">
         </a>
         For example, for an output range 0 to 255:
        </p>
        <div class="itemizedlist">
         <ul class="itemizedlist" style="list-style-type: none; ">
          <li class="listitem" style="list-style-type: none">
           <p>
            <a id="para_1ba45ad0-877e-40e4-be3d-d27936b7fa28" shape="rect">
            </a>
            c=2048, w=4096 becomes:
           </p>
           <div class="itemizedlist">
            <ul class="itemizedlist" style="list-style-type: none; ">
             <li class="listitem" style="list-style-type: none">
              <p>
               <a id="para_db7cd844-d17c-4be5-8e6a-718d261088eb" shape="rect">
               </a>
               if (x &lt;= 0) then y = 0
              </p>
             </li>
             <li class="listitem" style="list-style-type: none">
              <p>
               <a id="para_1f201335-0cff-4200-b44e-f24acab2e58e" shape="rect">
               </a>
               else if (x &gt; 4095) then y = 255
              </p>
             </li>
             <li class="listitem" style="list-style-type: none">
              <p>
               <a id="para_bdb24493-6301-4f2c-bcae-21f5fd13fcb1" shape="rect">
               </a>
               else y = ((x - 2047.5) / 4095 + 0.5) * (255-0) + 0
              </p>
             </li>
            </ul>
           </div>
          </li>
          <li class="listitem" style="list-style-type: none">
           <p>
            <a id="para_703579da-45e5-4b77-bcee-4431b35c7c05" shape="rect">
            </a>
            c=2048, w=1 becomes:
           </p>
           <div class="itemizedlist">
            <ul class="itemizedlist" style="list-style-type: none; ">
             <li class="listitem" style="list-style-type: none">
              <p>
               <a id="para_1a0d0121-156b-4367-ac9b-3015b43c569f" shape="rect">
               </a>
               if (x &lt;= 2047.5) then y = 0
              </p>
             </li>
             <li class="listitem" style="list-style-type: none">
              <p>
               <a id="para_3cd9b120-3ead-422d-aeca-3aa7367698c4" shape="rect">
               </a>
               else if (x &gt; 2047.5) then y = 255
              </p>
             </li>
             <li class="listitem" style="list-style-type: none">
              <p>
               <a id="para_f8015a4b-6f40-43fe-bf25-1c16923fe2e2" shape="rect">
               </a>
               else /* not reached */
              </p>
             </li>
            </ul>
           </div>
          </li>
          <li class="listitem" style="list-style-type: none">
           <p>
            <a id="para_67ccd49a-e99e-4782-9913-6816a2a3dc30" shape="rect">
            </a>
            c=0, w=100 becomes:
           </p>
           <div class="itemizedlist">
            <ul class="itemizedlist" style="list-style-type: none; ">
             <li class="listitem" style="list-style-type: none">
              <p>
               <a id="para_0fb4f126-82bb-41db-8e9d-ad6553bcb1c2" shape="rect">
               </a>
               if (x &lt;= -50) then y = 0
              </p>
             </li>
             <li class="listitem" style="list-style-type: none">
              <p>
               <a id="para_49fd122c-fbdc-4521-9634-99a105927dd7" shape="rect">
               </a>
               else if (x &gt; 49) then y = 255
              </p>
             </li>
             <li class="listitem" style="list-style-type: none">
              <p>
               <a id="para_3d8205e7-4052-4ced-8f2d-b5b094ff7d99" shape="rect">
               </a>
               else y = ((x + 0.5) / 99 + 0.5) * (255-0) + 0
              </p>
             </li>
            </ul>
           </div>
          </li>
          <li class="listitem" style="list-style-type: none">
           <p>
            <a id="para_f6b41c0b-5ca1-453d-8a24-17b522f195ae" shape="rect">
            </a>
            c=0, w=1 becomes:
           </p>
           <div class="itemizedlist">
            <ul class="itemizedlist" style="list-style-type: none; ">
             <li class="listitem" style="list-style-type: none">
              <p>
               <a id="para_d2fc34bd-7d80-458c-91da-00ecb2e3e213" shape="rect">
               </a>
               if (x &lt;= -0.5) then y = 0
              </p>
             </li>
             <li class="listitem" style="list-style-type: none">
              <p>
               <a id="para_04cc180c-a173-41a9-8f0a-edb90a91d633" shape="rect">
               </a>
               else if (x &gt; -0.5) then y = 255
              </p>
             </li>
             <li class="listitem" style="list-style-type: none">
              <p>
               <a id="para_8319fa7b-e10e-44b1-8931-11b04efc9de4" shape="rect">
               </a>
               else /* not reached */
              </p>
             </li>
            </ul>
           </div>
          </li>
         </ul>
        </div>
       </li>
       <li class="listitem">
        <p>
         <a id="para_04fce9b6-2e0a-4c2e-aafa-b9b1cbc19dae" shape="rect">
         </a>
         A Window Center of 2
         <sup>
          n-1
         </sup>
         and a Window Width of 2
         <sup>
          n
         </sup>
         selects the range of input values from 0 to 2
         <sup>
          n
         </sup>
         -1. This represents a mathematical identity VOI LUT transformation over the possible input values (whether used or not) in the case where no Modality LUT is specified and the stored pixel data are n bit unsigned integers.
        </p>
        <p>
         <a id="para_36003823-d7c7-4c45-9b07-7ca11c8fdcd8" shape="rect">
         </a>
         In the case where x1 is the lowest input value actually used in the Pixel Data and x2 is the highest, a Window Center of (x1+x2+1)/2 and a Window Width of (x2-x1+1) selects the range of input values from x1 to x2, which represents the full range of input values present as opposed to possible. This is distinct from the mathematical identity VOI LUT transformation, which instead selects the full range of input values possible as opposed to those actually used. The mathematical identity and full input range transformations are the same when x1 = 0 and x2 is 2
         <sup>
          n
         </sup>
         -1 and the input values are n bit unsigned integers. See also Note 7.
        </p>
       </li>
       <li class="listitem">
        <p>
         <a id="para_f063a797-f4c3-4af3-ae6b-bc1826967e64" shape="rect">
         </a>
         A Window Width of 1 is typically used to represent a "threshold" operation in which those integer input values less than the Window Center are represented as the minimum displayed value and those greater than or equal to the Window Center are represented as the maximum displayed value. A Window Width of 2 will have the same result for integral input values.
        </p>
       </li>
       <li class="listitem">
        <p>
         <a id="para_3f347f9d-dfce-4b0d-9468-04aaf5e30ced" shape="rect">
         </a>
         The application of Window Center (0028,1050) and Window Width (0028,1051) may select a signed input range. There is no implication that this signed input range is clipped to zero.
        </p>
       </li>
       <li class="listitem">
        <p>
         <a id="para_1e655bdb-c0e3-485d-ac39-463f79faeef5" shape="rect">
         </a>
         The selected input range may exceed the actual range of the input values, thus effectively "compressing" the contrast range of the displayed data into a narrower band of the available contrast range, and "flattening" the appearance. There are no limits to the maximum value of the window width, or to the minimum or maximum value of window level, both of which may exceed the actual or possible range of input values.
        </p>
       </li>
       <li class="listitem">
        <p>
         <a id="para_1ec43b52-3004-4934-b481-4806a5da6846" shape="rect">
         </a>
         Input values "below" the window are displayed as the minimum output value and input values "above" the window are displayed as the maximum output value. This is the common usage of the window operation in medical imaging. There is no provision for an alternative approach in which all values "outside" the window are displayed as the minimum output value.
        </p>
       </li>
       <li class="listitem">
        <p>
         <a id="para_d04aca60-7265-4947-ab07-8b4d367d0716" shape="rect">
         </a>
         The output of the Window Center/Width or VOI LUT transformation is either implicitly scaled to the full range of the display device if there is no succeeding transformation defined, or implicitly scaled to the full input range of the succeeding transformation step (such as the Presentation LUT), if present. See
         <a class="xref" href="#sect_C.11.6.1" shape="rect" title="C.11.6.1 Softcopy Presentation LUT Module Attribute Descriptions">
          Section C.11.6.1
         </a>.
        </p>
       </li>
       <li class="listitem">
        <p>
         <a id="para_fdb4d099-f504-43bb-92c7-2bc258ea24e4" shape="rect">
         </a>
         Fractional values of Window Center and Window Width are permitted (since the VR of these Attributes is Decimal String), and though they are not often encountered, applications should be prepared to accept them.
        </p>
       </li>
      </ol>
     </div>
    </div>
   </div>
   <div class="section">
    <div class="titlepage">
     <div>
      <div>
       <h6 class="title">
        <a id="sect_C.********.2" shape="rect">
        </a>
        C.********.2 General Requirements for Window Center and Window Width
       </h6>
      </div>
     </div>
    </div>
    <p>
     <a id="para_9095059d-2bab-45e0-b5d8-48694a78488d" shape="rect">
     </a>
     The Window Center (0028,1050), Window Width (0028,1051) and VOI LUT Function (0028,1056) shall be used only for Images with Photometric Interpretation (0028,0004) Values of MONOCHROME1 and MONOCHROME2. They have no meaning for other Images.
    </p>
    <p>
     <a id="para_70a579a5-3791-498e-8af2-8f8a6d0d1504" shape="rect">
     </a>
     If multiple values are present in the Window Center (0028,1050) and Window Width (0028,1051), both shall have the same number of Values and shall be considered as pairs. Multiple values indicate that multiple alternative views may be presented.
    </p>
    <p>
     <a id="para_309f4f56-a401-468a-ae28-4062df59d613" shape="rect">
     </a>
     If any VOI LUT, specified by the VOI LUT Sequence (0028,3010), is included by an Image, a pair of Window Center (0028,1050) and Window Width (0028,1051) Values, or the VOI LUT, but not both at the same time, may be applied to the Image for display. Inclusion of both indicates that multiple alternative views may be presented.
    </p>
    <p>
     <a id="para_2319878d-b602-4b2b-9fa8-d7cb72369c60" shape="rect">
     </a>
     If multiple Items are present in VOI LUT Sequence (0028,3010), only one may be applied to the Image for display. Multiple Items indicate that multiple alternative views may be presented.
    </p>
    <p>
     <a id="para_98020741-7376-44fd-ba7d-34df83b49ef2" shape="rect">
     </a>
     If the
     <a class="xref" href="#sect_C.11.2" shape="rect" title="C.11.2 VOI LUT Module">
      VOI LUT Module
     </a>
     is defined in an IOD and if neither VOI LUT Sequence (0028,3010) nor Window Center (0028,1050) and Window Width (0028,1051) are present, then the VOI LUT stage of the grayscale pipeline (described in
     <a class="olink" href="part04.html#sect_N.2" shape="rect">
      Section N.2 in PS3.17
     </a>
     ) is defined to be an identity transformation.
    </p>
    <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
     <h3 class="title">
      Note
     </h3>
     <div class="orderedlist">
      <ol class="orderedlist" type="1">
       <li class="listitem">
        <p>
         <a id="para_622abe46-3cb1-4916-bd2b-419b9981d0aa" shape="rect">
         </a>
         This requirement is specified so that IODs that define a particular output space for the grayscale pipeline, such as P-Values, are not in an undefined state when no VOI LUT Sequence (0028,3010) or Window Center (0028,1050) and Window Width (0028,1051) are present.
        </p>
       </li>
       <li class="listitem">
        <p>
         <a id="para_63c9bb3c-1bf9-40b5-82c4-8b7479505601" shape="rect">
         </a>
         Though the VOI LUT Module, VOI LUT Sequence (0028,3010) and Window Center (0028,1050) and Window Width (0028,1051) may be optional in a particular IOD, implementations that render images are expected to implement and apply these transformations when they are present in the image, unless overridden by the user, a presentation state, or a hanging protocol, and to allow the user to select which transformation to apply when multiple transformations are present.
        </p>
       </li>
      </ol>
     </div>
    </div>
   </div>
  </div>
  <div class="section">
   <div class="titlepage">
    <div>
     <div>
      <h5 class="title">
       <a id="sect_C.********" shape="rect">
       </a>
       C.******** VOI LUT Function
      </h5>
     </div>
    </div>
   </div>
   <p>
    <a id="para_bb15e5ab-e6b4-4b6b-b90d-7770550aa366" shape="rect">
    </a>
    The VOI LUT Function (0028,1056) specifies a potentially non-linear conversion for the output of the (conceptual) Modality LUT values to the input of the (conceptual) Presentation LUT.
   </p>
   <p>
    <a id="para_f172a009-381d-4d50-83be-c4fbf8ad86d6" shape="rect">
    </a>
    The behavior for the Value LINEAR is defined in
    <a class="xref" href="#sect_C.********.1" shape="rect" title="C.********.1 Default LINEAR Function">
     Section C.********.1
    </a>. For all other values, the VOI LUT Function (0028,1056) shall include a unique descriptor of the LUT function to be used. Each descriptor is associated with a bivariate function of Window Center (0028,1050) and Window Width (0028,1051).
   </p>
   <p>
    <a id="para_e1f935e7-8eda-4cda-a758-76121efe27ed" shape="rect">
    </a>
    If the VOI LUT Function (0028,1056) is present with a Value other than LINEAR, the values provided in Window Center (0028,1050) and Window Width (0028,1051) shall not be interpreted as a linear conversion of the (conceptual) Modality LUT values to the input to the (conceptual) Presentation LUT - but as parameters for the function defined by the VOI LUT Function descriptor in (0028,1056).
   </p>
   <p>
    <a id="para_60ef1b34-5c2c-465d-8fac-e6af54df0b6d" shape="rect">
    </a>
    When defined, each descriptor must provide the functional relationship between the output of the (conceptual) Modality LUT values to the input of the (conceptual) Presentation LUT.
   </p>
   <div class="section">
    <div class="titlepage">
     <div>
      <div>
       <h6 class="title">
        <a id="sect_C.********.1" shape="rect">
        </a>
        C.********.1 SIGMOID Function
       </h6>
      </div>
     </div>
    </div>
    <p>
     <a id="para_498647cf-389d-44ac-b938-5459237ad144" shape="rect">
     </a>
     If the Value of VOI LUT Function (0028,1056) is SIGMOID, the function to be used to convert the output of the (conceptual) Modality LUT values to the input of the (conceptual) Presentation LUT is given by
    </p>
    <div class="equation">
     <a id="equation_C.11-1" shape="rect">
     </a>
     <p class="title">
      <strong>
       Equation C.11-1. 
      </strong>
     </p>
     <div class="equation-contents">
      <object data="figures/part03_withmml_image_15.svg" style="" type="image/svg+xml">
       <param name="src" value="image_15.svg"/>
      </object>
     </div>
    </div>
    <br class="equation-break" clear="none"/>
    <p>
     <a id="para_18050a53-83af-4b86-8f52-122c68636429" shape="rect">
     </a>
     where
    </p>
    <div class="variablelist">
     <dl class="variablelist">
      <dt>
       <span class="term">
        x
       </span>
      </dt>
      <dd>
       <p>
        <a id="para_ad4ce919-f04d-4963-b5ca-63fea457be09" shape="rect">
        </a>
        is the input value of the LUT (i.e., the output of the (conceptual) Modality LUT).
       </p>
      </dd>
      <dt>
       <span class="term">
        c
       </span>
      </dt>
      <dd>
       <p>
        <a id="para_01144b49-802d-42be-9411-92932ecb1646" shape="rect">
        </a>
        is the Window Center defined interactively by the user or by using the values provided in Window Center (0028,1050).
       </p>
      </dd>
      <dt>
       <span class="term">
        w
       </span>
      </dt>
      <dd>
       <p>
        <a id="para_92830ce7-37df-4934-8a36-64e9226ccf61" shape="rect">
        </a>
        is the Window Width defined interactively by the user or by using the values provided in Window Width (0028,1051).
       </p>
      </dd>
      <dt>
       <span class="term">
        y
       </span>
      </dt>
      <dd>
       <p>
        <a id="para_43f63cd6-5052-4fbf-b25b-d4865f8b17c6" shape="rect">
        </a>
        is the output value
       </p>
      </dd>
      <dt>
       <span class="term">
        y
        <sub>
         min
        </sub>
       </span>
      </dt>
      <dd>
       <p>
        <a id="para_cd44a39a-94e9-4c7a-b526-c295c0ce1097" shape="rect">
        </a>
        is the minimum output value
       </p>
      </dd>
      <dt>
       <span class="term">
        y
        <sub>
         max
        </sub>
       </span>
      </dt>
      <dd>
       <p>
        <a id="para_341ff0bb-2ec1-408b-a4eb-af9d382b9276" shape="rect">
        </a>
        is the maximum output value
       </p>
      </dd>
     </dl>
    </div>
    <p>
     <a id="para_8c9795fe-9383-45e1-956e-399526cdb478" shape="rect">
     </a>
     Window Width (0028,1051) shall always be greater than 0.
    </p>
    <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
     <h3 class="title">
      Note
     </h3>
     <p>
      <a id="para_6679ee00-9760-4491-a390-a551fceeb10a" shape="rect">
      </a>
      Window Width (0028,1051) is required to be greater than zero to prevent division by zero (quite apart from being meaningless).
     </p>
    </div>
   </div>
   <div class="section">
    <div class="titlepage">
     <div>
      <div>
       <h6 class="title">
        <a id="sect_C.********.2" shape="rect">
        </a>
        C.********.2 LINEAR_EXACT Function
       </h6>
      </div>
     </div>
    </div>
    <p>
     <a id="para_d833e4bb-bf6f-4aae-b609-c100de5afad6" shape="rect">
     </a>
     If the Value of VOI LUT Function (0028,1056) is LINEAR_EXACT, the function to be used to convert the output of the (conceptual) Modality LUT values to the input of the (conceptual) Presentation LUT is given by the following pseudo-code, where x is the input value, y is an output value with a range from y
     <sub>
      min
     </sub>
     to y
     <sub>
      max
     </sub>, c is Window Center (0028,1050) and w is Window Width (0028,1051):
    </p>
    <div class="itemizedlist">
     <ul class="itemizedlist" style="list-style-type: none; ">
      <li class="listitem" style="list-style-type: none">
       <p>
        <a id="para_3004934b-d4d5-461b-879e-e471b75c9a68" shape="rect">
        </a>
        if (x &lt;= c - w/2), then y = y
        <sub>
         min
        </sub>
       </p>
      </li>
      <li class="listitem" style="list-style-type: none">
       <p>
        <a id="para_6ab524e0-126b-4740-a247-df1949effb78" shape="rect">
        </a>
        else if (x &gt; c + w/2), then y = y
        <sub>
         max
        </sub>
       </p>
      </li>
      <li class="listitem" style="list-style-type: none">
       <p>
        <a id="para_7c93fc6a-ca82-44d1-8401-5e397d28fc12" shape="rect">
        </a>
        else y = ((x - c) / w + 0.5) * (y
        <sub>
         max
        </sub>
        - y
        <sub>
         min
        </sub>
        ) + y
        <sub>
         min
        </sub>
       </p>
      </li>
     </ul>
    </div>
    <p>
     <a id="para_8e358143-5f7e-4101-8747-510cac1a18ae" shape="rect">
     </a>
     Window Width (0028,1051) shall always be greater than 0.
    </p>
    <div class="note" style="margin-left: 0.5in; margin-right: 0.5in;">
     <h3 class="title">
      Note
     </h3>
     <div class="orderedlist">
      <ol class="orderedlist" type="1">
       <li class="listitem">
        <p>
         <a id="para_2f9003bb-27af-47e1-a15b-937ab679f269" shape="rect">
         </a>
         For example, given stored unsigned pixel values from 0 to 65535, a Rescale Intercept (0028,1052) of 0 and a Rescale Slope (0028,1053) of 1.0/65535, a Window Width (0028,1051) of 1.0 and a Window Center (0028,1050) of 0.5 would specify the entire range of values (the identity transformation for those rescale values).
        </p>
       </li>
       <li class="listitem">
        <p>
         <a id="para_3389f7d6-9f7e-4742-91e8-02fd2f5306ee" shape="rect">
         </a>
         Window Width (0028,1051) is required to be greater than zero to prevent division by zero (quite apart from being meaningless).
        </p>
       </li>
      </ol>
     </div>
    </div>
   </div>
  </div>
 </div>
</div>
