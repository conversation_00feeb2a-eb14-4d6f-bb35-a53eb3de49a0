### C.12.2 Common Instance Reference Module
Table C.12-8 defines the Attributes that describe the hierarchical relationships of any SOP Instances referenced from other Modules within the Instance in which this Module occurs.
**Table C.12-8. Common Instance Reference Module Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Referenced Series Sequence | (0008,1115) | 1C | Sequence of Items each of which includes the Attributes of one Series. One or more Items shall be included in this Sequence. Required if this Instance references Instances in this Study. |
| &gt;Series Instance UID | (0020,000E) | 1 | Unique identifier of the Series containing the referenced Instances. |
| &gt;Referenced Instance Sequence | (0008,114A) | 1 | Sequence of Items each providing a reference to an Instance that is part of the Series defined by Series Instance UID (0020,000E) in the enclosing Item. One or more Items shall be included in this Sequence. |
| &gt;&gt;Include Table 10-11 SOP Instance Reference Macro Attributes |   |   |   |
| Studies Containing Other Referenced Instances Sequence | (0008,1200) | 1C | Sequence of Items each identifying a Study other than the Study of which this Instance is a part, which Studies contain Instances that are referenced elsewhere in this Instance. One or more Items shall be included in this Sequence. Required if this Instance references Instances in other Studies. |
| &gt;Study Instance UID | (0020,000D) | 1 | Unique identifier of the Study containing the referenced Instances. |
| &gt;Include Table 10-4 Series and Instance Reference Macro Attributes |   |   |   |