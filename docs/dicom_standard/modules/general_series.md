#### C.7.3.1 General Series Module
Table C.7-5a specifies the Attributes of the General Series Module, which identify and describe general information about the Series within a Study.
**Table C.7-5a. General Series Module Attributes**
| Attribute Name | Tag | Type | Attribute Description |
| --- | --- | --- | --- |
| Modality | (0008,0060) | 1 | Type of device, process or method that originally acquired or produced the data used to create the Instances in this Series. See Section C.*******.1 for Defined Terms. |
| Series Instance UID | (0020,000E) | 1 | Unique identifier of the Series. |
| Series Number | (0020,0011) | 2 | A number that identifies this Series. |
| Laterality | (0020,0060) | 2C | Laterality of (paired) body part examined. Required if the body part examined is a paired structure and Image Laterality (0020,0062) or Frame Laterality (0020,9072) or Measurement Laterality (0024,0113) are not present. **Enumerated Values:** R right L left ### Note - Some IODs support Image Laterality (0020,0062) at the Image level or Frame Laterality (0020,9072) at the Frame level in the Frame Anatomy Functional Group Macro or Measurement Laterality (0024,0113) at the Measurement level, which can provide a more comprehensive mechanism for specifying the laterality of the body part(s) being examined. - There is no value for both left and right, for which Image Laterality (0020,0062) at the Image level or Frame Laterality (0020,9072) may be used instead. - There is no value for median, for which Primary Anatomic Structure Modifier Sequence (0008,2230) or Anatomic Region Modifier Sequence (0008,2220) may be used instead. |
| Series Date | (0008,0021) | 3 | Date the Series started. |
| Series Time | (0008,0031) | 3 | Time the Series started. |
| Performing Physician's Name | (0008,1050) | 3 | Name of the physician(s) administering the Series. |
| Performing Physician Identification Sequence | (0008,1052) | 3 | Identification of the physician(s) administering the Series. One or more Items are permitted in this Sequence. If more than one Item, the number and order shall correspond to the Value of Performing Physician's Name (0008,1050), if present. |
| &gt;Include Table 10-1 Person Identification Macro Attributes |   |   |   |
| Protocol Name | (0018,1030) | 3 | User-defined description of the conditions under which the Series was performed. ### Note This Attribute conveys Series-specific protocol identification and may or may not be identical to the protocol described in the Performed Protocol Code Sequence (0040,0260) in the Performed Protocol Code Sequence (0040,0260) in Table 10-16 Performed Procedure Step Summary Macro Attributes. |
| Series Description | (0008,103E) | 3 | Description of the Series. |
| Series Description Code Sequence | (0008,103F) | 3 | A coded description of the Series. Only a single Item is permitted in this Sequence. |
| &gt;Include Table 8.8-1 Code Sequence Macro Attributes |   |   | No Baseline CID is defined. |
| Operators' Name | (0008,1070) | 3 | Name(s) of the operator(s) supporting the Series. |
| Operator Identification Sequence | (0008,1072) | 3 | Identification of the operator(s) supporting the Series. One or more Items are permitted in this Sequence. If more than one Item, the number and order shall correspond to the Value of Operators' Name (0008,1070), if present. |
| &gt;Include Table 10-1 Person Identification Macro Attributes |   |   |   |
| Referenced Performed Procedure Step Sequence | (0008,1111) | 3 | Uniquely identifies the Performed Procedure Step SOP Instance to which the Series is related. Only a single Item is permitted in this Sequence. |
| &gt;Include Table 10-11 SOP Instance Reference Macro Attributes |   |   |   |
| Related Series Sequence | (0008,1250) | 3 | Identification of Series significantly related to this Series. One or more Items are permitted in this Sequence. ### Note - For example, for a combined CT and PET acquisition, the CT images and PET images would be in separate Series that could cross-reference each other with multiple purpose of reference codes meaning same anatomy, simultaneously acquired and same indication. - The related Series may have different Frames of Reference and hence require some sort of registration before spatial coordinates can be directly compared. - This Attribute is not intended for conveying localizer reference information, for which Referenced Image Sequence (0008,1140) should be used. |
| &gt;Study Instance UID | (0020,000D) | 1 | Instance UID of Study to which the related Series belongs. |
| &gt;Series Instance UID | (0020,000E) | 1 | Instance UID of Related Series. |
| &gt;Purpose of Reference Code Sequence | (0040,A170) | 2 | Describes the purpose for which the reference is made. Zero or more Items shall be included in this Sequence. When absent, implies that the reason for the reference is unknown. |
| &gt;&gt;Include Table 8.8-1 Code Sequence Macro Attributes |   |   | D [CID 7210 Related Series Purpose of Reference](part16.html#sect_CID_7210). |
| Body Part Examined | (0018,0015) | 3 | Text description of the part of the body examined. See [Annex L Correspondence of Anatomic Region Codes and Body Part Examined Defined Terms in PS3.16](part16.html#chapter_L) for Defined Terms ### Note Some IODs support the Anatomic Region Sequence (0008,2218), which can provide a more comprehensive mechanism for specifying the body part being examined. |
| Patient Position | (0018,5100) | 2C | Patient position descriptor relative to the equipment. Required for images where Patient Orientation Code Sequence (0054,0410) is not present and whose SOP Class UID (0008,0016) is one of the following: "1.2.840.10008.*******.1.2" (CT Image Storage), "1.2.840.10008.*******.1.4" (MR Image Storage), "1.2.840.10008.*******.1.2.1" (Enhanced CT Image Storage), "1.2.840.10008.*******.1.4.1" (Enhanced MR Image Storage), "1.2.840.10008.*******.1.4.3" (Enhanced Color MR Image Storage), "1.2.840.10008.*******.1.4.2" (MR Spectroscopy Storage SOP Class). May be present for other SOP Classes if Patient Orientation Code Sequence (0054,0410) is not present. See Section C.*******.2 for Defined Terms and further explanation. |
| Smallest Pixel Value in Series | (0028,0108) | 3 | The minimum value of all images in this Series. |
| Largest Pixel Value in Series | (0028,0109) | 3 | The maximum value of all images in this Series. |
| Request Attributes Sequence | (0040,0275) | 3 | Sequence that contains Attributes from the Imaging Service Request. One or more Items are permitted in this Sequence. |
| &gt;Include Table 10-9 Request Attributes Macro Attributes |   |   | No Baseline CID is defined. |
| Include Table 10-16 Performed Procedure Step Summary Macro Attributes |   |   | No Baseline CID is defined. |
| Anatomical Orientation Type | (0010,2210) | 1C | The anatomical orientation type used in Instances generated by this equipment. **Enumerated Values:** BIPED QUADRUPED Required if the Patient is a non-human organism and the anatomical Frame of Reference is not bipedal. May be present otherwise. See Section C.*******.1 and Section C.*******.1. ### Note If this Attribute is not present, the default human standard anatomical position is used to define the patient orientation of projection images and the Patient-Based Coordinate System of cross-sectional images. |
| Treatment Session UID | (300A,0700) | 3 | A unique identifier of the RT Treatment Session to which Instances in this Series belong. |
### Note
- If any Performed Procedure Step SOP Class is supported as an SCU by a Storage SCU, the SCU is strongly encouraged to support the Attribute Referenced Performed Procedure Step Sequence (0008,1111). This Attribute references the Performed Procedure Step SOP Instance, and extraction of this Attribute from a Composite Instance may allow retrieval of the Performed Procedure Step SOP Instance.
- If the Storage SCU does not conform to any Performed Procedure Step SOP Class, it is still advisable to include the Attributes Performed Procedure Step Start Date (0040,0244), Performed Procedure Step Start Time (0040,0245) and Performed Procedure Step Description (0040,0254) into the Composite Instances.
##### C.******* General Series Module Attribute Descriptions
###### C.*******.1 Modality
**Defined Terms:**
ANN
Annotation
AR
Autorefraction
ASMT
Content Assessment Results
AU
Audio
BDUS
Bone Densitometry (ultrasound)
BI
Biomagnetic imaging
BMD
Bone Densitometry (X-Ray)
CFM
Confocal Microscopy
CR
Computed Radiography
CT
Computed Tomography
CTPROTOCOL
CT Protocol (Performed)
DMS
Dermoscopy
DG
Diaphanography
DOC
Document
DX
Digital Radiography
ECG
Electrocardiography
EEG
Electroencephalography
EMG
Electromyography
EOG
Electrooculography
EPS
Cardiac Electrophysiology
ES
Endoscopy
FID
Fiducials
GM
General Microscopy
HC
Hard Copy
HD
Hemodynamic
IO
Intra-Oral Radiography
IOL
Intraocular Lens Data
IVOCT
Intravascular Optical Coherence Tomography
IVUS
Intravascular Ultrasound
KER
Keratometry
KO
Key Object Selection
LEN
Lensometry
LS
Laser surface scan
MG
Mammography
MR
Magnetic Resonance
M3D
Model for 3D Manufacturing
NM
Nuclear Medicine
OAM
Ophthalmic Axial Measurements
OCT
Optical Coherence Tomography (non-Ophthalmic)
OP
Ophthalmic Photography
OPM
Ophthalmic Mapping
OPT
Ophthalmic Tomography
OPTBSV
Ophthalmic Tomography B-scan Volume Analysis
OPTENF
Ophthalmic Tomography En Face
OPV
Ophthalmic Visual Field
OSS
Optical Surface Scan
OT
Other
PA
Photoacoustic
PLAN
Plan
### Note
The term "PLAN" denotes Series describing planned activities. It is not be confused with radiotherapy treatment plans.
POS
Position Sensor
PR
Presentation State
PT
Positron emission tomography (PET)
PX
Panoramic X-Ray
REG
Registration
RESP
Respiratory
RF
Radio Fluoroscopy
RG
Radiographic imaging (conventional film/screen)
RTDOSE
Radiotherapy Dose
RTIMAGE
Radiotherapy Image
RTINTENT
Radiotherapy Intent
RTPLAN
Radiotherapy Plan
RTRAD
RT Radiation
RTRECORD
RT Treatment Record
RTSEGANN
Radiotherapy Segment Annotation
RTSTRUCT
Radiotherapy Structure Set
RWV
Real World Value Map
SEG
Segmentation
SM
Slide Microscopy
SMR
Stereometric Relationship
SR
SR Document
SRF
Subjective Refraction
STAIN
Automated Slide Stainer
TEXTUREMAP
Texture Map
TG
Thermography
US
Ultrasound
VA
Visual Acuity
XA
X-Ray Angiography
XAPROTOCOL
XA Protocol (Performed)
XC
External-camera Photography
**Retired Defined Terms:**
AS
Angioscopy
CD
Color flow Doppler
CF
Cinefluorography
CP
Culposcopy
CS
Cystoscopy
DD
Duplex Doppler
DF
Digital fluoroscopy
DM
Digital microscopy
DS
Digital Subtraction Angiography
EC
Echocardiography
FA
Fluorescein angiography
FS
Fundoscopy
LP
Laparoscopy
MA
Magnetic resonance angiography
MS
Magnetic resonance spectroscopy
OPR
Ophthalmic Refraction
ST
Single-photon emission computed tomography (SPECT)
VF
Videofluorography
### Note
- The XA modality incorporates the retired modality DS.
- The RF modality incorporates the retired modalities CF, DF, VF.
- The modality listed in the Modality Data Element (0008,0060) may not match the name of the IOD in which it appears. For example, a SOP Instance from X-Ray Angiographic Image IOD may list the RF modality when an RF implementation produces an XA object.
- The MR modality incorporates the retired modalities MA and MS.
- The US modality incorporates the retired modalities EC, CD, and DD.
- The NM modality incorporates the retired modality ST.
###### C.*******.2 Patient Position
Patient Position (0018,5100) specifies the positioning of the patient relative to the imaging equipment space. This Attribute is intended for annotation purposes only. It does not provide an exact mathematical relationship of the patient to the imaging equipment. The information in Patient Position (0018,5100) is more formally modeled in the Enhanced Patient Orientation Module. See Section C.7.6.30 for additional detail, guidance, and discussion of vertical gantry cases.
When multiple subjects are present in the same image, and arranged with different positions, then the Patient Position (0018,5100) in the General Series Module is nominal, does not apply to each subject, but does define the relationship of the nominal Patient-Based Coordinate System to the machine.
### Note
In conjunction with the Patient Position (0018,5100) in each Item of the Group of Patients Identification Sequence (0010,0027), Patient Position (0018,5100) in the General Series Module may be helpful to compute patient-relative spatial information for each subject from the Attributes of the Image Plane Module.
**When facing the front of the imaging equipment:**
HF
Head First is defined as the patient's head being positioned toward the front of the imaging equipment (i.e., head entering the front of the equipment).
FF
Feet First is defined as the patient's feet being positioned toward the front of the imaging equipment (i.e., feet entering the front of the equipment).
LF
Left First is defined as the patient's left side being positioned towards the front of the imaging equipment (i.e., patient's left side entering the front of the equipment).
RF
Right First is defined as the patient's right being positioned towards the front of the imaging equipment (i.e., patient's right side entering the front of the equipment).
AF
Anterior First is defined as the patient's anterior being positioned towards the front of the imaging equipment (i.e., patient's anterior side entering the front of the equipment).
PF
Posterior First is defined as the patient's posterior being positioned towards the front of the imaging equipment (i.e., patient's posterior side entering the front of the equipment).
P
Prone is defined as the patient's face being positioned in a downward (gravity) direction.
S
Supine is defined as the patient's face being in an upward direction.
DR
Decubitus Right is defined as the patient's right side being in a downward direction.
DL
Decubitus Left is defined as the patient's left side being in a downward direction.
V
Vertical is defined as the patient's feet being positioned in a downward (gravity) direction.
I
Inverted is defined as the patient's head being positioned in a downward (gravity) direction.
**Defined Terms:**
HFP
Head First-Prone
HFS
Head First-Supine
HFDR
Head First-Decubitus Right
HFDL
Head First-Decubitus Left
HFV
Head First-Vertical
HFI
Head First-Inverted
FFDR
Feet First-Decubitus Right
FFDL
Feet First-Decubitus Left
FFP
Feet First-Prone
FFS
Feet First-Supine
FFV
Feet First-Vertical
FFI
Feet First-Inverted
LFP
Left First-Prone
LFS
Left First-Supine
LFDR
Left First-Decubitus Right
LFDL
Left First-Decubitus Left
RFP
Right First-Prone
RFS
Right First-Supine
RFDR
Right First-Decubitus Right
RFDL
Right First-Decubitus Left
AFP
Anterior First-Prone
AFS
Anterior First-Supine
AFDR
Anterior First-Decubitus Right
AFDL
Anterior First-Decubitus Left
PFP
Posterior First-Prone
PFS
Posterior First-Supine
PFDR
Posterior First-Decubitus Right
PFDL
Posterior First-Decubitus Left
### Note
- For quadrupeds, separate concepts for ventral and dorsal are not introduced, rather it is expected that anterior and posterior will be considered synonymous as they are when applied to the trunk.
- In previous releases of the standard, imaging equipment that is aligned vertically with respect to gravity was not addressed. Doing so introduced additional prone, supine, and decubitus variants, as well as vertical and inverted concepts. Older implementations might not recognize or have appropriate behaviors for those Defined Terms. See Section C.7.6.30 for relevant examples.
The Figure C.*******.2-1 illustrates some of these Defined Terms for imaging equipment with a table, such as in X-Ray Angiography. The orientation of the patient related to gravity is always recumbent.
**Figure C.*******.2-1. Representation of the Eight Different Patient Positions on the X-Ray Table**
**Figure C.*******.2-2. Example of Right First-Prone (RFP) Patient Position Relative to the Gantry and Table for a Small Animal**