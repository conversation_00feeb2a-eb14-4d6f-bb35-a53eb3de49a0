## A.19 RT Structure Set IOD
### A.19.1 RT Structure Set IOD Description
The focus for this RT Structure Set IOD is primarily to address the requirements for transfer of structures and related data defined on image scanners, virtual simulation workstations, treatment planning systems and similar devices.
When used without structures, the RT Structure Set can be used to transfer references to Image Series, Registration, Deformable Registration, Spatial Fiducial, Segmentation, or any other Instances that may contribute to the creation of structures.
### A.19.2 RT Structure Set IOD Entity-Relationship Model
This IOD uses the E-R Model in Section A.1.2 , with only the Structure Set IE below the Series IE.
### A.19.3 RT Structure Set IOD Module Table
Table A.19.3-1 specifies the Modules of the RT Structure Set IOD .
**Table A.19.3-1. RT Structure Set IOD Modules**
| IE | Module | Reference | Usage |
| --- | --- | --- | --- |
| Patient | Patient | C.7.1.1 | M |
| Patient | Clinical Trial Subject | C.7.1.3 | U |
| Study | General Study | C.7.2.1 | M |
| Study | Patient Study | C.7.2.2 | U |
| Study | Clinical Trial Study | C.7.2.3 | U |
| Series | RT Series | C.8.8.1 | M |
| Series | Clinical Trial Series | C.7.3.2 | U |
| Equipment | General Equipment | C.7.5.1 | M |
| Frame of Reference | Frame of Reference | C.7.4.1 | U - See note 2. |
| Structure Set | Structure Set | C.8.8.5 | M |
| Structure Set | ROI Contour | C.8.8.6 | M |
| Structure Set | RT ROI Observations | C.8.8.8 | M |
| Structure Set | Approval | C.8.8.16 | U |
| Structure Set | General Reference | C.12.4 | U |
| Structure Set | SOP Common | C.12.1 | M |
| Structure Set | Common Instance Reference | C.12.2 | U |
### Note
- The Audio Module (Retired) was previously included in this IOD but has been retired. See [PS3.3-2004](http://medical.nema.org/MEDICAL/Dicom/2004/printed/04_03pu3.pdf) .
- Previously the Frame of Reference Module was not included (see [PS3.3-2014b](http://medical.nema.org/MEDICAL/Dicom/2014b/output/pdf/part03.pdf) ). Historically, more than one Frame of Reference could be referenced within the Structure Set Module 's Referenced Frame of Reference Sequence (3006,0010), in which case the Frame of Reference Module would not have been present.
### A.19.4 RT Structure Set IOD Content Constraints
For Purpose of Reference Code Sequence (0040,A170) within Source Instance Sequence (0042,0013) in the General Reference Module D [CID 7019 âSegmentation Non-Image Source Purpose of Referenceâ](part16.html#sect_CID_7019) shall be used.