## A.18 RT Dose IOD
### A.18.1 RT Dose IOD Description
The focus for this RT Dose IOD is to address the requirements for transfer of dose distributions calculated by radiotherapy treatment planning systems. These distributions may be represented as 2D or 3D grids. This IOD may also contain dose-volume histogram data. This IOD does not provide for definition of doses in beam or other coordinate systems. The application is responsible for transforming data in other, Non-Patient-Based Coordinate Systems to the Patient-Based Coordinate System described in Section C.*******.1 .
### A.18.2 RT Dose IOD Entity-Relationship Model
This IOD uses the E-R Model in Section A.1.2 , with only the Dose IE below the Series IE.
### A.18.3 RT Dose IOD Module Table
Table A.18.3-1 specifies the Modules of the RT Dose IOD .
**Table A.18.3-1. RT Dose IOD Modules**
| IE | Module | Reference | Usage |
| --- | --- | --- | --- |
| Patient | Patient | C.7.1.1 | M |
| Patient | Clinical Trial Subject | C.7.1.3 | U |
| Study | General Study | C.7.2.1 | M |
| Study | Patient Study | C.7.2.2 | U |
| Study | Clinical Trial Study | C.7.2.3 | U |
| Series | RT Series | C.8.8.1 | M |
| Series | Clinical Trial Series | C.7.3.2 | U |
| Frame of Reference | Frame of Reference | C.7.4.1 | M |
| Equipment | General Equipment | C.7.5.1 | M |
| Dose | General Image | C.7.6.1 | C - Required if dose data contains grid-based doses. |
| Dose | Image Plane | C.7.6.2 | C - Required if dose data contains grid-based doses. |
| Dose | Image Pixel | C.7.6.3 | C - Required if dose data contains grid-based doses. |
| Dose | Multi-frame | C.7.6.6 | C - Required if dose data contains grid-based doses and pixel data is multi-frame data. |
| Dose | RT Dose | C.8.8.3 | M |
| Dose | RT DVH | C.8.8.4 | U |
| Dose | SOP Common | C.12.1 | M |
| Dose | Common Instance Reference | C.12.2 | U |
| Dose | Frame Extraction | C.12.3 | C - Required if the SOP Instance was created in response to a Frame-Level retrieve request |
### Note
- Within the RT Dose IOD , the RT Dose Module supports 2D and 3D dose grids, and the RT DVH Module supports dose-volume histogram data. They are not mutually exclusive: they may be included in a single Instance of the object. Product Conformance Statements should clearly state which of these mechanisms is supported and under what conditions.
- The RT Dose IOD has been defined as a Composite IOD, separate from the RT Plan IOD . This has been done for the following reasons: to allow for the multiplicity of possible dose calculations using beam models for the same basic plan,
- to avoid undesirable transmission of large amounts of data with the treatment plan, and
- to accommodate the fact that CT Simulation and other "beam geometry" generating devices that use the RT Plan IOD do not have or require access to this data, either for transmission or storage.
- The Audio Module (Retired) was previously included in this IOD but has been retired. See [PS3.3-2004](http://medical.nema.org/MEDICAL/Dicom/2004/printed/04_03pu3.pdf) .
- The Overlay Plane Module , Multi-frame Overlay Module , Modality LUT Module , Structure Set Module , ROI Contour Module and RT Dose ROI Module (Retired) were previously included in this IOD. See [PS3.3-2022d](http://dicom.nema.org/medical/dicom/2022d/output/pdf/part03.pdf) .