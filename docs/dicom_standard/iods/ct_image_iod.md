## A.3 CT Image IOD
### A.3.1 CT Image IOD Description
The CT Image IOD specifies an image that has been created by a Computed Tomography imaging device.
### A.3.2 CT Image IOD Entity-Relationship Model
This IOD uses the E-R Model in Section A.1.2 , with only the Image IE below the Series IE.
### A.3.3 CT Image IOD Module Table
Table A.3-1 specifies the Modules of the CT Image IOD .
**Table A.3-1. CT Image IOD Modules**
| IE | Module | Reference | Usage |
| --- | --- | --- | --- |
| Patient | Patient | C.7.1.1 | M |
| Patient | Clinical Trial Subject | C.7.1.3 | U |
| Study | General Study | C.7.2.1 | M |
| Study | Patient Study | C.7.2.2 | U |
| Study | Clinical Trial Study | C.7.2.3 | U |
| Series | General Series | C.7.3.1 | M |
| Series | Clinical Trial Series | C.7.3.2 | U |
| Frame of Reference | Frame of Reference | C.7.4.1 | M |
| Frame of Reference | Synchronization | C.7.4.2 | C - Required if time synchronization was applied. |
| Equipment | General Equipment | C.7.5.1 | M |
| Acquisition | General Acquisition | C.7.10.1 | M |
| Image | General Image | C.7.6.1 | M |
| Image | General Reference | C.12.4 | U |
| Image | Enhanced Patient Orientation | C.7.6.30 | U |
| Image | Image Plane | C.7.6.2 | M |
| Image | Image Pixel | C.7.6.3 | M |
| Image | Contrast/Bolus | C.7.6.4 | C - Required if contrast media was used in this image |
| Image | Device | C.7.6.12 | U |
| Image | Specimen | C.7.6.22 | U |
| Image | CT Image | C.8.2.1 | M |
| Image | Multi-energy CT Image | C.8.2.2 | C - Required if Multi-energy CT Acquisition (0018,9361) is YES. |
| Image | Overlay Plane | C.9.2 | U |
| Image | VOI LUT | C.11.2 | U |
| Image | SOP Common | C.12.1 | M |
| Image | Common Instance Reference | C.12.2 | U |
#### A.3.3.1 CT Image IOD Content Constraints
If Multi-energy CT Acquisition (0018,9361) is YES the following constraints will apply:
- The Contrast/Bolus Module shall be present if contrast was administered even if images are processed to remove contrast information from the pixels, e.g. Virtual Non-Contrast images.
- The Real World Value Mapping Sequence (0040,9096) shall be present in the General Image Module .
- For Measurement Units Code Sequence (0040,08EA) in the Real World Value Mapping Sequence (0040,9096) D [CID 301 âMulti-energy Material Unitâ](part16.html#sect_CID_301) shall be used.