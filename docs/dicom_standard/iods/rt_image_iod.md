## A.17 RT Image IOD
### A.17.1 RT Image IOD Description
The focus for this RT Image IOD is to address the requirements for image transfer found in general Radiotherapy (RT) applications performed on conventional simulators, virtual simulators, and portal imaging devices. Such images have a conical imaging geometry and may either be acquired directly from the device, or digitized using a film digitizer. Numeric beam data parameters may also be recorded with the image, indicating the parameter values at the time the image was taken or created.
### A.17.2 RT Image IOD Entity-Relationship Model
This IOD uses the E-R Model in Section A.1.2 , with only the Image IE below the Series IE.
### A.17.3 RT Image IOD Module Table
Table A.17.3-1 specifies the Modules of the RT Image IOD .
**Table A.17.3-1. RT Image IOD Modules**
| IE | Module | Reference | Usage |
| --- | --- | --- | --- |
| Patient | Patient | C.7.1.1 | M |
| Patient | Clinical Trial Subject | C.7.1.3 | U |
| Study | General Study | C.7.2.1 | M |
| Study | Patient Study | C.7.2.2 | U |
| Study | Clinical Trial Study | C.7.2.3 | U |
| Series | RT Series | C.8.8.1 | M |
| Series | Clinical Trial Series | C.7.3.2 | U |
| Frame of Reference | Frame of Reference | C.7.4.1 | U |
| Equipment | General Equipment | C.7.5.1 | M |
| Acquisition | General Acquisition | C.7.10.1 | M |
| Image | General Image | C.7.6.1 | M |
| Image | General Reference | C.12.4 | U |
| Image | Image Pixel | C.7.6.3 | M |
| Image | Contrast/Bolus | C.7.6.4 | C - Required if contrast media was used in this image. |
| Image | Cine | C.7.6.5 | C - Required if Multi-frame Image is a cine image. |
| Image | Multi-frame | C.7.6.6 | C - Required if pixel data is multi-frame data. |
| Image | Device | C.7.6.12 | U |
| Image | RT Image | C.8.8.2 | M |
| Image | Modality LUT | C.11.1 | U |
| Image | VOI LUT | C.11.2 | U |
| Image | Approval | C.8.8.16 | U |
| Image | SOP Common | C.12.1 | M |
| Image | Common Instance Reference | C.12.2 | U |
| Image | Frame Extraction | C.12.3 | C - Required if the SOP Instance was created in response to a Frame-Level retrieve request |
### Note
- The inclusion of the Multi-frame Module allows for the expression of time-dependent image series or multiple exposures of identical beam geometries (i.e., multiple exposure portal images). If a time-dependent series of images (such as port images or DRRs) is represented the Cine Module is used to indicate this. This would subsequently allow analysis of Patient movement during treatment. Multiple exposure images allow individual images of treatment ports and open field ports to be grouped into a single Multi-frame Image.
- The Modality LUT Module has been included to allow the possibility of conversion between portal image pixel values and dose transmitted through the Patient. The VOI LUT Module has been included to allow the possibility of translation between stored pixel values (after the Modality LUT has been applied if specified) and display levels.
- The Curve Module (Retired) and Audio Module (Retired) were previously included in the Image IE for this IOD but have been retired. See [PS3.3-2004](http://medical.nema.org/MEDICAL/Dicom/2004/printed/04_03pu3.pdf) .
- The General Equipment Module contains information describing the equipment used to acquire or generate the RT Image (such as a portal imager, conventional simulator or treatment planning system). However, the equipment Attributes in the RT Image Module describe the equipment on which the treatment has been or will be given, typically an electron accelerator.
- For RT Images that contain no relevant pixel data, such as BEV images without DRR information, Pixel Data (7FE0,0010) should be filled with a sequence of zeros.
- The Frame of Reference Module has been included to allow the indication of spatial association of two or more RT Image Instances (e.g., where the images have been acquired in the same Frame of Reference, or have been resampled to share the same Frame of Reference). If the Frame of Reference occurs within a SOP Instance within a given Series, then all SOP Instances within that Series will be spatially related. For example, two RT Images may share the same Frame of Reference if they are located on the same physical plane, as determined by the treatment machine Gantry Angle (300A,011E) and source to image plane distance specified by RT Image SID (3002,0026).