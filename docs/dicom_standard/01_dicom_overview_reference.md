# DICOM Standard Overview - Reference Guide

## Executive Summary

Digital Imaging and Communications in Medicine (DICOM) is the standard for the communication and management of medical imaging information and related data. This reference guide provides a comprehensive overview of the DICOM Standard (PS3) structure, scope, and components.

## What DICOM Facilitates

The DICOM Standard facilitates interoperability of medical imaging equipment by specifying:

- **Network Communications**: A set of protocols for devices claiming conformance to follow
- **Command Syntax & Semantics**: Commands and associated information that can be exchanged using these protocols
- **Media Communication**: Media storage services, file formats, and medical directory structures for interchange media
- **Implementation Information**: Required information for conformance claims

## What DICOM Does NOT Specify

The DICOM Standard does not specify:
- Implementation details of any features on conforming devices
- Overall set of features expected from integrated systems
- Testing/validation procedures for conformance assessment

## Scope and Application

The DICOM Standard pertains to the field of Medical Informatics. Within that field, it addresses the exchange of digital information between medical imaging equipment and other systems.

### Primary Focus Areas
This Standard has been developed with an emphasis on diagnostic medical imaging as practiced in radiology, cardiology, pathology, dentistry, ophthalmology and related disciplines, and image-based therapies such as interventional radiology, radiotherapy and surgery.

### Extended Applications
However, it is also applicable to a wide range of image and non-image related information exchanged in clinical, research, veterinary, and other medical environments.

## Historical Development

### Early Development (1983-1988)
The American College of Radiology (ACR) and the National Electrical Manufacturers Association (NEMA) formed a joint committee in 1983 to develop a standard to:
- Promote communication of digital image information, regardless of device manufacturer
- Facilitate development and expansion of picture archiving and communication systems (PACS)
- Allow creation of diagnostic information databases for geographically distributed devices

### Version Evolution
- **Version 1.0 (1985)**: ACR-NEMA Standards Publication No. 300-1985
- **Version 2.0 (1988)**: ACR-NEMA Standards Publication No. 300-1988

### Modern DICOM (1993)
In 1993, ACR-NEMA Standard 300 was substantially revised and replaced by this Standard, designated Digital Imaging and Communications in Medicine (DICOM).

#### Major Enhancements Over ACR-NEMA
- **Network Environment**: Supports networked operation using TCP/IP (vs. point-to-point only)
- **Offline Media Exchange**: Supports industry standard media (CD-R, DVD-R, USB) and file systems
- **Service-Oriented Protocol**: Specifies command semantics and device reactions
- **Conformance Levels**: Explicitly describes conformance statement requirements

### Committee Evolution
In 1995, with the addition of DICOM capabilities for cardiology imaging supported by the American College of Cardiology, the ACR-NEMA Joint Committee was reorganized as the DICOM Standards Committee, a broad collaboration of stakeholders across all medical imaging specialties.

## Global Applicability

DICOM is a world-wide standard that can be used in every locale. It provides mechanisms to handle data that support cultural requirements, such as different writing systems, character sets, languages, and structures for addresses and person names.

### Localization Support
- Code sets specification (e.g., procedure codes)
- Data element usage profiling
- Local policy requirements
- Integration with mechanisms like IHE (Integrating the Healthcare Enterprise) profiles

## Standard Maintenance

### Continuous Evolution
The DICOM Standard is an evolving standard and it is maintained in accordance with the Procedures of the DICOM Standards Committee.

### Update Process
- Proposals welcome from all users
- Supplements and corrections balloted several times yearly
- Approved Final Text becomes official immediately
- Periodic consolidation into updated editions

### Compatibility Requirements
A requirement in updating the Standard is to maintain effective compatibility with previous editions.

### Feature Retirement
- Retired features may still be used but are not maintained
- Discouraged for new implementations
- Alternative features remain in current Standard

## Core Concepts

### Information Objects
Many DICOM services involve the exchange of persistent information objects, such as images. An instance of such an information object may be exchanged across many systems and many organizational contexts, and over time.

### Global Unique Identifiers
Each instance is identified by a globally unique object identifier, which persists with the instance across all exchanges. Changes to the semantic content of an instance are defined to create a new instance, which is assigned a new globally unique object identifier.

### SOP Classes
Conformance to the DICOM Standard is stated in terms of Service-Object Pair (SOP) Classes, which represent Services (such as Storage using network, media, or web) operating on types of Information Objects (such as CT or MR images).

### Information Model
A large number of information objects defined in the DICOM Standard follow a common composite information model with information entities representing Patient, Study, Series, Equipment, Frame of Reference, and the specific instance data type.

#### Real-World Mapping
- **Study**: Approximately equivalent to an ordered procedure
- **Series**: Approximately equivalent to a performed data acquisition protocol element
- Less clear mapping in domains like Radiotherapy but maintained for consistency

## Standard Structure (Multi-Part Document)

DICOM consists of the following parts:

### Part 1: Introduction and Overview (PS3.1)
- Overview of entire DICOM Standard
- History, scope, goals, and structure
- Description of each Standard part

### Part 2: Conformance (PS3.2)
PS3.2 of the DICOM Standard defines principles that implementations claiming conformance to the Standard shall follow:
- Conformance requirements
- Conformance statement structure
- Referenced conformance sections

### Part 3: Information Object Definitions (PS3.3)
PS3.3 of the DICOM Standard specifies a number of Information Object Classes that provide an abstract definition of real-world entities applicable to communication of digital medical images and related information.

#### Two Types of Information Object Classes:
1. **Normalized**: Only inherent attributes (e.g., study date in study class)
2. **Composite**: Includes related but non-inherent attributes (e.g., patient name in CT image class)

### Part 4: Service Class Specifications (PS3.4)
PS3.4 of the DICOM Standard defines a number of Service Classes. A Service Class associates one or more Information Objects with one or more Commands to be performed upon these objects.

#### Service Class Examples:
- Storage Service Class
- Query/Retrieve Service Class
- Print Management Service Class
- Workflow Management Service Classes

### Part 5: Data Structures and Encoding (PS3.5)
PS3.5 of the DICOM Standard specifies how DICOM applications construct and encode the Data Set information resulting from the use of the Information Objects and Services Classes defined in PS3.3 and PS3.4.
- Standard image compression techniques (JPEG lossless/lossy)
- Data Stream encoding rules
- International character set semantics

### Part 6: Data Dictionary (PS3.6)
PS3.6 of the DICOM Standard is the centralized registry that defines the collection of all DICOM Data Elements available to represent information.

#### For Each Element Specifies:
- A unique identifying tag
- A name
- Value characteristics
- Semantic meaning

### Part 7: Message Exchange (PS3.7)
PS3.7 of the DICOM Standard specifies both the service and protocol used by an application in a medical imaging environment to exchange Messages over the communications support services defined in PS3.8.
- DIMSE Services operations and notifications
- Association establishment/termination rules
- Command request/response exchange rules
- Message encoding rules

### Part 8: Network Communication Support (PS3.8)
PS3.8 of the DICOM Standard specifies the communication services and the upper layer protocols necessary to support, in a networked environment, communication between DICOM applications.
- Upper Layer Service (subset of OSI Presentation Service)
- DICOM Upper Layer Protocol with TCP/IP
- Association management

### Part 10: Media Storage and File Format (PS3.10)
PS3.10 of the DICOM Standard specifies a general model for the storage of medical imaging information on removable media.
- Layered model for media storage
- DICOM file format
- Secure DICOM file format
- Media storage application profiles

### Part 11: Media Storage Application Profiles (PS3.11)
PS3.11 of the DICOM Standard specifies application specific subsets of the DICOM Standard to which an implementation may claim conformance.
- Clinical context descriptions
- Media storage service classes
- Information object specifications
- Media format selections

### Part 12: Media Formats and Physical Media (PS3.12)
PS3.12 of the DICOM Standard facilitates the interchange of information between applications in medical environments by specifying:
- Physical media characteristics
- File format specifications

### Part 14: Grayscale Standard Display Function (PS3.14)
PS3.14 specifies a standardized display function for consistent display of grayscale images.
- Display calibration methods
- Human visual perception-based function
- Barten's model of human visual system

### Part 15: Security and System Management Profiles (PS3.15)
PS3.15 of the DICOM Standard specifies security and system management profiles to which implementations may claim conformance.
- References external standard protocols (DHCP, LDAP, TLS, ISCL)
- Security techniques (public keys, smart cards)
- Data encryption schemes

### Part 16: Content Mapping Resource (PS3.16)
PS3.16 of the DICOM Standard specifies:
- Content mapping resources
- Template specifications

### Part 17: Explanatory Information (PS3.17)
PS3.17 of the DICOM Standard specifies:
- Explanatory information
- Implementation guidance

### Part 18: Web Services (PS3.18)
PS3.18 of the DICOM Standard specifies the means whereby Web Services can be used for retrieving or storing a DICOM object.
- HTTP requests for data retrieval/storage
- Media type specifications
- Proxy functionality for DICOM SCU/SCP

### Part 19: Application Hosting (PS3.19)
PS3.19 of the DICOM Standard specifies an Application Programming Interface (API) to a DICOM based medical computing system into which programs written to that standardized interface can "plug-in".
- Standardized plug-in model
- Host system independence
- API specifications and data models

### Part 20: Imaging Reports using HL7 CDA (PS3.20)
PS3.20 of the DICOM Standard specifies templates for the encoding of imaging reports using the HL7 Clinical Document Architecture Release 2 (CDA R2, or simply CDA) Standard.
- Clinical procedure report templates
- CDA implementation guide
- DICOM Structured Report transformation

### Part 21: Transformations (PS3.21)
PS3.21 of the DICOM Standard specifies the transformations between DICOM and other representations of the same information.
- NCI Annotation and Image Markup format transformations

### Part 22: Real-Time Communication (PS3.22)
PS3.22 of the DICOM Standard specifies an SMPTE ST 2110-10 based service for the real-time transport of DICOM metadata.
- Real-time DICOM metadata transport
- Video/audio flow association
- SMPTE ST 2110-20/30 integration

## Communication Models

Figure 5-1 presents the general communication model of the Standard, which spans both network (on-line) and media storage interchange (off-line) communication.

### Transport Mechanisms
- **DICOM Message Service and Upper Layer Service**: Network communication independence
- **DICOM Web Service API and HTTP Service**: Hypertext protocol transport
- **Basic DICOM File Service**: Media storage independence
- **DICOM Real-Time Communication**: Real-time metadata transport

## Key Definitions

### Attribute
A property of an Information Object. An Attribute has a name and a value that are independent of any encoding scheme.

### Data Element
A unit of information as defined by a single entry in the data dictionary.

### Data Set
Exchanged information consisting of a structured set of Attributes. The value of each Attribute in a Data Set is expressed as a Data Element.

### Information Object
An abstraction of a real information entity (e.g., CT Image, Structured Report, etc.) that is acted upon by one or more DICOM Commands.

### SOP Class
The pair of an Information Object and either a DIMSE Service Group, a Media Storage Service, or a Web Service.

### Conformance Statement
A formal statement that describes a specific implementation of the DICOM Standard. It specifies the Service Classes, Information Objects, Communication Protocols, Security Profiles, and Media Storage Application Profiles supported by the implementation.

## Conformance Requirements

### SOP Class-Based Conformance
Conformance to the DICOM Standard is through specified SOP Classes using DIMSE messages, Web Services, media interchange, or the hosted application API.

### Forward/Backward Compatibility
Once such a unit of conformance is specified in the Standard, all changes thereto are forward and backward compatible (except in rare cases where the original specification was non-interoperable, or conflicted with another standard).

## Standard Citation Guidelines

### General Reference Format
NEMA PS3 / ISO 12052, Digital Imaging and Communications in Medicine (DICOM) Standard, National Electrical Manufacturers Association, Rosslyn, VA, USA (available free at http://www.dicomstandard.org/)

### Conformance Reference Examples
- Network Exchange: "conformant to the DICOM [SOP Class] for network exchange as a Service Class [User|Provider]"
- Media Exchange: "conformant to the DICOM [SOP Class] for media exchange as a File Set [Creator|Updater|Reader]"
- Web Services: "conformant to the DICOM [Web Service] as [Origin-server|User-agent]"

## Maintenance and Evolution

Under the procedures of the DICOM Standards Committee, the Standard is in constant revision. Supplements and corrections to the Standard are balloted and approved several times a year.

### Change Management
- Final Text changes go into effect immediately
- Periodic consolidation into published editions
- Standard officially changes when each change is approved
- Published editions are convenience documents only

---

*This reference guide is based on DICOM PS3.1 (Introduction and Overview) and provides a comprehensive summary of the DICOM Standard structure, scope, and key concepts for quick reference.*