# PyRT-DICOM

[![PyPI - Version](https://img.shields.io/pypi/v/pyrt-dicom.svg)](https://pypi.org/project/pyrt-dicom)
[![PyPI - Python Version](https://img.shields.io/pypi/pyversions/pyrt-dicom.svg)](https://pypi.org/project/pyrt-dicom)

-----

**Table of Contents**

- [Installation](#installation)
- [License](#license)

## Installation

```console
pip install pyrt-dicom
```

## PyRT-DICOM ("Pirate DICOM")

Python library for creating radiotherapy DICOM files with CT, RTImage, RTDose, RTPlan, and RTStruct modalities. Built on top of pydicom, this library provides a strongly-typed, IntelliSense-friendly interface for creating DICOM Information Object Definitions (IODs) through modular composition.

### Core Features

- **Modular Architecture**: IODs are composed from individual DICOM modules
- **Type Safety**: All DICOM data elements as class attributes for IntelliSense
- **No Free Text Configuration**: Strongly-typed interfaces with strict enum enforcement
- **Optional Validation**: No validation at creation, validate on-demand or during export
- **Auto-Generated UIDs**: UIDs generated at object creation with cross-dataset linking
- **Memory Storage**: Store all data in memory for fast access and simplicity

### Development Status

🚨 **PROOF OF CONCEPT PHASE** 🚨

This project is currently in POC phase, focusing on RT Dose IOD to validate the modular approach using multiple inheritance. No base classes or abstractions until POC success criteria are met.

## License

`pyrt-dicom` is distributed under the terms of the [MIT](https://spdx.org/licenses/MIT.html) license.